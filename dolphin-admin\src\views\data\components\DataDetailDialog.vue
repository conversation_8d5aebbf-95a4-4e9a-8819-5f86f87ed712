<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reqGetDataInfo } from '@/api/data'
import type { DataItem } from '@/api/data/type'

// 定义组件名称
defineOptions({
  name: 'DataDetailDialog',
})

// 定义props
interface Props {
  dataId: string | null
}

const props = defineProps<Props>()

// 数据详情
const dataDetail = ref<DataItem | null>(null)
const loading = ref(false)

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 获取诊断分类标签文本
const getDiagnosisLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    normal: '正常',
    benign: '良性',
    malignant: '恶性',
    uncertain: '未定',
    other: '其他',
  }
  return labelMap[category] || category
}

// 获取检查部位文本
const getExamPartText = (value?: string) => {
  if (!value) return ''
  const options = {
    thyroid: '甲状腺',
    breast: '乳房',
    liver: '肝脏',
    gallbladder: '胆囊',
    heart: '心脏',
    pancreas: '胰腺',
    spleen: '脾脏',
    prostate: '前列腺',
  }
  return options[value as keyof typeof options] || value
}

// 获取切面类型文本
const getSliceTypeText = (value?: string) => {
  if (!value) return ''
  const options = {
    sagittal: '矢状面',
    coronal: '冠状面',
    transverse: '横断面',
    oblique: '斜切面',
    longitudinal: '纵切面',
    cross: '横切面',
  }
  return options[value as keyof typeof options] || value
}

// 获取数据详情
const fetchDataDetail = async (id: string) => {
  try {
    loading.value = true
    const result = await reqGetDataInfo(id)
    dataDetail.value = result as any as DataItem
  } catch (error) {
    ElMessage.error('获取详情失败，请稍后重试')
    dataDetail.value = null
  } finally {
    loading.value = false
  }
}

// 显示详情对话框
const showDetail = async (id: string) => {
  await fetchDataDetail(id)
  
  if (!dataDetail.value) return

  const imageInfo = (() => {
    if (dataDetail.value.images && dataDetail.value.images.length > 0) {
      return `${dataDetail.value.images.length}张图像`
    } else if (dataDetail.value.image) {
      return '1张图像'
    } else {
      return '无图像'
    }
  })()

  const info = `
ID: ${dataDetail.value.id}
图像: ${imageInfo}
检查部位: ${getExamPartText(dataDetail.value.part) || '未设置'}
切面类型: ${getSliceTypeText(dataDetail.value.section) || '未设置'}
诊断分类: ${getDiagnosisLabel(dataDetail.value.classification)}
详细诊断: ${dataDetail.value.detail || '无'}
特征分析: ${dataDetail.value.caption?.join(', ') || '无'}
诊断联想: ${dataDetail.value.associate?.join(', ') || '无'}
诊断依据: ${dataDetail.value.basis?.join(', ') || '无'}
病灶信息: ${dataDetail.value.focus || '无'}
位置: ${dataDetail.value.location || '无'}
备注: ${dataDetail.value.remark || '无'}
报告: ${dataDetail.value.report || '无'}
创建时间: ${formatTime(dataDetail.value.createTime)}
更新时间: ${formatTime(dataDetail.value.updateTime)}
  `.trim()

  ElMessageBox.alert(info, '数据详情', {
    confirmButtonText: '确定',
    type: 'info',
    customStyle: {
      width: '500px',
    },
  })
}

// 监听dataId变化
watch(
  () => props.dataId,
  (newId) => {
    if (newId) {
      showDetail(newId)
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  showDetail,
})
</script>

<template>
  <!-- 这个组件主要通过ElMessageBox显示详情，不需要模板内容 -->
  <div v-if="loading" style="display: none;">加载中...</div>
</template>

<style scoped lang="scss">
// 这个组件主要使用ElMessageBox，不需要特殊样式
</style>
