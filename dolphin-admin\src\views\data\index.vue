<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reqSearchData, reqDeleteData } from '@/api/data'
import type { DataItem, SearchQuery } from '@/api/data/type'
import DataSearchBar from './components/DataSearchBar.vue'
import DataOperationBar from './components/DataOperationBar.vue'
import DataTable from './components/DataTable.vue'
import DataExportDialog from './components/DataExportDialog.vue'
import DataDetailDialog from './components/DataDetailDialog.vue'

// 定义组件名称
defineOptions({
  name: 'DataManagement',
})

// 响应式数据
const loading = ref(false)

// 表格引用
const tableRef = ref()

// 选中的行数据
const selectedRows = ref<DataItem[]>([])

// 导出对话框显示状态
const exportDialogVisible = ref(false)

// 详情对话框相关
const detailDialogRef = ref()
const currentDetailId = ref<string | null>(null)

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
})

// 表格数据
const tableData = ref<DataItem[]>([])

// 当前搜索参数
const currentSearchParams = ref<{ classification: string; diagnoseId: string }>({
  classification: '',
  diagnoseId: '',
})

// 获取数据列表
const getDataList = async () => {
  try {
    loading.value = true

    const params: SearchQuery = {
      pageNumber: pagination.current - 1, // API使用从0开始的页码
      pageSize: pagination.size,
      classification: currentSearchParams.value.classification || undefined,
      diagnoseId: currentSearchParams.value.diagnoseId || undefined,
    }

    const result = await reqSearchData(params)
    if (Array.isArray(result)) {
      tableData.value = result.map((item: any) => ({
        ...item,
        images: item.image ? [item.image] : [],
        caption: Array.isArray(item.caption) ? item.caption : item.caption ? [item.caption] : [],
        associate: Array.isArray(item.associate) ? item.associate : item.associate ? [item.associate] : [],
        basis: Array.isArray(item.basis) ? item.basis : item.basis ? [item.basis] : [],
        // 确保其他字段存在
        focus: item.focus || '',
        location: item.location || '',
        remark: item.remark || '',
        report: item.report || '',
      }))
      pagination.total = result.length
    } else if (result && typeof result === 'object') {
      // 如果返回分页对象
      const pageResult = result as any

      const records = pageResult.records || pageResult.data || pageResult.content || []
      // 确保数据是数组格式，并进行基本的数据清理
      tableData.value = Array.isArray(records)
        ? records.map((item: any) => ({
            ...item,
            // 将单个image字段转换为images数组格式，以保持组件兼容性
            images: item.image ? [item.image] : [],
            caption: Array.isArray(item.caption) ? item.caption : item.caption ? [item.caption] : [],
            associate: Array.isArray(item.associate) ? item.associate : item.associate ? [item.associate] : [],
            basis: Array.isArray(item.basis) ? item.basis : item.basis ? [item.basis] : [],
            // 确保其他字段存在
            focus: item.focus || '',
            location: item.location || '',
            remark: item.remark || '',
            report: item.report || '',
          }))
        : []
      pagination.total = pageResult.total || pageResult.totalElements || 0
    } else {
      tableData.value = []
      pagination.total = 0
    }
  } catch (error: any) {
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: { classification: string; diagnoseId: string }) => {
  currentSearchParams.value = params
  pagination.current = 1 // 重置到第一页
  selectedRows.value = [] // 清空选中状态
  getDataList()
}

// 重置搜索
const handleReset = () => {
  currentSearchParams.value = { classification: '', diagnoseId: '' }
  pagination.current = 1 // 重置到第一页
  selectedRows.value = [] // 清空选中状态
  getDataList()
}

// 处理选中变化
const handleSelectionChange = (selection: DataItem[]) => {
  selectedRows.value = selection
}

// 删除选中的记录
const handleDeleteSelected = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  const count = selectedRows.value.length
  const ids = selectedRows.value.map((row) => row.id).join(', ')

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${count} 条数据记录吗？\nID: ${ids}\n此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
      },
    )

    // 批量删除
    const deletePromises = selectedRows.value.map((row) => reqDeleteData(row.id))
    await Promise.all(deletePromises)

    ElMessage.success(`成功删除 ${count} 条数据`)

    // 清空选中状态
    selectedRows.value = []
    tableRef.value?.clearSelection()

    // 删除成功后刷新数据列表
    await getDataList()
  } catch (error: any) {
    // 用户取消删除
    if (error === 'cancel') {
      ElMessage.info('已取消删除')
      return
    }

    // 删除失败
    ElMessage.error('删除失败，请稍后重试')
  }
}

// 删除单行记录
const handleDelete = async (row: DataItem) => {
  try {
    await ElMessageBox.confirm(`确定要删除ID为 ${row.id} 的数据记录吗？此操作不可恢复！`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false,
    })

    // 调用删除接口
    await reqDeleteData(row.id)

    ElMessage.success('删除成功')

    // 删除成功后刷新数据列表
    await getDataList()
  } catch (error: any) {
    // 用户取消删除
    if (error === 'cancel') {
      ElMessage.info('已取消删除')
      return
    }

    // 删除失败
    ElMessage.error('删除失败，请稍后重试')
  }
}

// 查看详情
const handleView = (row: DataItem) => {
  currentDetailId.value = row.id
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  selectedRows.value = [] // 清空选中状态
  getDataList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  selectedRows.value = [] // 清空选中状态
  getDataList()
}

// 处理导出按钮点击
const handleExport = () => {
  exportDialogVisible.value = true
}

// 处理导出对话框关闭
const handleExportClose = () => {
  exportDialogVisible.value = false
}

// 组件挂载
onMounted(() => {
  getDataList()
})
</script>
<template>
  <div class="data-management">
    <!-- 操作按钮区域 -->
    <DataOperationBar
      :selected-count="selectedRows.length"
      :loading="loading"
      @export="handleExport"
      @delete-selected="handleDeleteSelected"
    />

    <!-- 搜索筛选区域 -->
    <DataSearchBar
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :loading="loading"
      @selection-change="handleSelectionChange"
      @view="handleView"
      @delete="handleDelete"
    />

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 导出数据对话框 -->
    <DataExportDialog
      v-model:visible="exportDialogVisible"
      @close="handleExportClose"
    />

    <!-- 详情对话框 -->
    <DataDetailDialog
      ref="detailDialogRef"
      :data-id="currentDetailId"
    />
  </div>
</template>

<style scoped lang="scss">
.data-management {
  background-color: var(--bg-secondary);
  min-height: 100%;

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-primary);

    :deep(.el-pagination) {
      .el-pagination__total,
      .el-pagination__jump {
        color: var(--text-primary);
        font-size: 13px;
      }

      .el-pager li {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;
        font-size: 13px;
        min-width: 30px;
        height: 30px;
        line-height: 30px;

        &:hover {
          background-color: var(--bg-hover) !important;
        }

        &.is-active {
          background-color: var(--medical-blue) !important;
          color: #ffffff !important;
        }
      }

      .btn-prev,
      .btn-next {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;
        font-size: 13px;
        min-width: 30px;
        height: 30px;
        line-height: 30px;

        &:hover {
          background-color: var(--bg-hover) !important;
        }

        &:disabled {
          background-color: var(--bg-secondary) !important;
          color: var(--text-tertiary) !important;
        }
      }
    }
  }
}
</style>
