<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
// 图标导入已移至各个子组件中

// 导入组件
import {
  DataPreview,
  ImageUploadSection,
  LesionInfoSection,
  ImageAnalysisSection,
  DiagnosisSection,
  FormActionsSection,
} from './components'

// 导入API接口
import { reqSaveDiagnose } from '@/api/data'

// 导入类型定义
// [建议] 在你的 types.ts 文件中为这两个类型也加上 remark 和 report 字段
import type { MedicalFormData } from './types'
import type { DiagnoseDto } from '@/api/data/type'

// 导入坐标工具函数
import { convertLocationFormat } from '@/utils/coordinateUtils'

// 导入状态管理
import useImageStore from '@/store/modules/image'

// 初始化状态管理
const imageStore = useImageStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const previewVisible = ref(false)
// 抽屉状态管理已移至各个子组件中

// 表单数据
const formData = reactive<MedicalFormData>({
  imageFile: null,
  fileName: '',
  imagePreview: '',
  imageIds: [], // 图像上传后的ID数组

  examPart: '', // 检查部位
  sliceType: '', // 切面类型
  location: [], // 标注位置 (来自标注)
  caption: [], // 特征分析
  associate: [], // 诊断联想
  basis: [], // 诊断依据
  classification: '', // 诊断分类
  detail: '', // 详细诊断
  remark: '', // 备注
  report: '', // 病史
})

// 检查部位选项
const examPartOptions = [
  { label: '甲状腺', value: 'thyroid' },
  { label: '乳房', value: 'breast' },
  { label: '肝脏', value: 'liver' },
  { label: '胆囊', value: 'gallbladder' },
  { label: '心脏', value: 'heart' },
  { label: '胰腺', value: 'pancreas' },
  { label: '脾脏', value: 'spleen' },
  { label: '前列腺', value: 'prostate' },
]

// 切面类型选项
const sliceTypeOptions = [
  { label: '矢状面', value: 'sagittal' },
  { label: '冠状面', value: 'coronal' },
  { label: '横断面', value: 'transverse' },
  { label: '斜切面', value: 'oblique' },
  { label: '纵切面', value: 'longitudinal' },
  { label: '横切面', value: 'cross' },
]

// 表单验证规则
const rules = {
  imageFile: [{ required: true, message: '请上传医学图像文件', trigger: 'change' }],
  examPart: [{ required: true, message: '请选择检查部位', trigger: 'change' }],
  sliceType: [{ required: true, message: '请选择切面类型', trigger: 'change' }],
  caption: [
    {
      validator: (_rule: any, value: string[], callback: Function) => {
        if (value.length === 0) {
          callback(new Error('请至少添加一个图像特征'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  associate: [
    {
      validator: (_rule: any, value: string[], callback: Function) => {
        // 只有选择良性、恶性、未定或其他时才需要验证诊断联想
        if (
          formData.classification === 'benign' ||
          formData.classification === 'malignant' ||
          formData.classification === 'uncertain' ||
          formData.classification === 'other'
        ) {
          if (value.length === 0) {
            callback(new Error('请至少添加一个诊断联想'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  basis: [
    {
      validator: (_rule: any, value: string[], callback: Function) => {
        // 只有选择良性、恶性、未定或其他时才需要验证诊断依据
        if (
          formData.classification === 'benign' ||
          formData.classification === 'malignant' ||
          formData.classification === 'uncertain' ||
          formData.classification === 'other'
        ) {
          if (value.length === 0) {
            callback(new Error('请至少添加一个诊断依据'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  classification: [{ required: true, message: '请选择诊断分类', trigger: 'change' }],
  detail: [{ required: true, message: '请填写详细诊断', trigger: 'blur' }],
  // remark 和 report 字段为非必填，因此无需添加验证规则
}

// 计算属性
const classificationText = computed(() => {
  const typeMap: Record<string, string> = {
    normal: '正常',
    benign: '良性',
    malignant: '恶性',
    uncertain: '未定',
    other: '其他',
  }
  return typeMap[formData.classification] || ''
})


// 文件上传回调
const handleUploadSuccess = (_file: File, result: { imageId: string }) => {
  // 将上传成功后返回的图像ID添加到数组中
  formData.imageIds.push(result.imageId)

  // 检查是否有该图像的标注数据
  checkAndLoadAnnotationData(result.imageId)

  ElMessage.success('图像上传成功！')
}

const handleUploadError = (_error: Error) => {
  ElMessage.error('图像上传失败，请重试')
}

// 检查并加载标注数据
const checkAndLoadAnnotationData = (imageId: string) => {
  if (!imageId) {
    return
  }

  try {
    // 从Pinia状态管理获取所有标注数据
    const allAnnotationPoints = imageStore.getAllAnnotationPoints()

    if (allAnnotationPoints.length > 0) {
      // 转换标注点为位置字符串格式，确保坐标为整数
      formData.location = allAnnotationPoints.map((point) => `(${Math.round(point.x)},${Math.round(point.y)})`)

      ElMessage.success(`已自动填充标注数据：${allAnnotationPoints.length}个标注点`)
    } else {
      // 清空标注位置
      formData.location = []
    }
  } catch (error) {
    ElMessage.error('加载标注数据失败')
  }
}

// 验证标注数据完整性
const validateAnnotationData = (): boolean => {
  // 如果有图像但没有标注位置数据，给出提示
  if (formData.imageIds.length > 0 && formData.location.length === 0) {
    ElMessage.warning('建议先进行图像标注，以获得更准确的位置信息')
    return true // 不强制要求标注，只是提示
  }

  return true
}

// 表单操作方法
const previewData = () => {
  previewVisible.value = true
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证标注数据完整性
    if (!validateAnnotationData()) {
      return
    }

    loading.value = true


    // 准备提交数据
    const diagnoseData: DiagnoseDto = {
      associate: formData.associate, // 诊断联想
      basis: formData.basis, // 诊断依据
      caption: formData.caption, // 特征分析
      classification: formData.classification, // 诊断分类
      detail: formData.detail, // 详细诊断
      remark: formData.remark, // 备注
      report: formData.report, // 病史
      focus: imageStore.getCombinedLesionDescription(), // 合并的病灶信息 (来自标注页面)
      location: convertLocationFormat(
        imageStore.getAllAnnotationPoints().map((a) => `(${Math.round(a.x)},${Math.round(a.y)})`),
      ), // 所有标注位置 (来自标注页面)
      part: formData.examPart, // 检查部位
      section: formData.sliceType, // 切面类型
      image: imageStore.getImageIdForSubmit() || formData.imageIds[0], // 优先使用Canvas快照ID，否则使用原始图片ID
    }

    const result = await reqSaveDiagnose(diagnoseData)

    // 显示保存成功消息
    ElMessage.success('数据保存成功！')

    // 清理Pinia中的所有数据
    try {
      imageStore.clearAllData()
    } catch (error) {
      // 清理失败时静默处理，不影响用户体验
    }

    // 触发自定义事件
    const submitData = {
      ...diagnoseData,
      fileName: formData.fileName,
      timestamp: new Date().toISOString(),
      recordId: result || '',
    }
    emit('submit', submitData)

    // 保存成功后清空表单
    await clearFormAfterSave()
  } catch (error) {
    ElMessage.error('数据保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 保存成功后清空表单（不需要用户确认）
const clearFormAfterSave = async () => {
  formRef.value?.resetFields()

  // 重置数据
  Object.assign(formData, {
    imageFile: null,
    fileName: '',
    imagePreview: '',
    imageIds: [], // 重置图像ID数组
    examPart: '', // 清空检查部位
    sliceType: '', // 清空切面类型
    location: [], // 清空标注位置
    caption: [], // 清空特征分析
    associate: [], // 清空诊断联想
    basis: [], // 清空诊断依据
    classification: '', // 诊断分类
    detail: '', // 详细诊断
    remark: '', // 备注
    report: '', // 病史
  })
}

const resetForm = async () => {
  try {
    await ElMessageBox.confirm('确定要重置表单吗？所有数据将被清除。', '确认重置', {
      type: 'warning',
    })

    formRef.value?.resetFields()

    // 重置数据
    Object.assign(formData, {
      imageFile: null,
      fileName: '',
      imagePreview: '',
      imageIds: [], // 重置图像ID数组
      examPart: '', // 重置检查部位
      sliceType: '', // 重置切面类型
      location: [], // 重置标注位置
      caption: ['边界清晰', '回声均匀', '形态规则'], // 特征分析
      associate: ['考虑良性结节', '结合BRADS分级'], // 诊断联想
      basis: ['无钙化表现', '血流信号正常'], // 诊断依据
      classification: '', // 诊断分类
      detail: '', // 详细诊断
      remark: '', // 备注
      report: '', // 病史
    })

    // 标签输入状态由子组件管理，无需重置

    ElMessage.success('表单已重置')
  } catch {
    // 用户取消
  }
}

// 抽屉切换方法已移至各个子组件中

// 状态恢复逻辑
const initializeFromStore = () => {
  // 清理过期的图像数据
  imageStore.cleanupExpiredImage()

  // 恢复医学信息
  const savedExamInfo = imageStore.getExamInfo()

  if (savedExamInfo.examPart) {
    formData.examPart = savedExamInfo.examPart
  }

  if (savedExamInfo.sliceType) {
    formData.sliceType = savedExamInfo.sliceType
  }

  // 优先检查是否有标注后的图片（用于显示）
  if (imageStore.hasAnnotatedImage()) {
    const annotatedImage = imageStore.getLatestAnnotatedImage()

    if (annotatedImage) {
      // 显示标注后的图片
      formData.imageFile = annotatedImage.file
      formData.fileName = annotatedImage.file?.name || '标注后图片'
      formData.imagePreview = annotatedImage.previewUrl

      // 使用Canvas快照的imageId（如果有的话）
      const imageIdForSubmit = imageStore.getImageIdForSubmit()
      if (imageIdForSubmit) {
        formData.imageIds = [imageIdForSubmit]
      } else {
        formData.imageIds = [annotatedImage.imageId]
      }

      // 检查并加载标注数据
      checkAndLoadAnnotationData(annotatedImage.originalImageId)

      ElMessage.success('已恢复标注后的图像数据！')

      return
    }
  }

  // 如果没有标注后的图片，检查是否有原始裁剪图像数据
  if (imageStore.hasOriginalCroppedImage()) {
    const originalImage = imageStore.getOriginalCroppedImage()

    if (originalImage) {
      // 显示原始图像
      formData.imageFile = originalImage.file || null
      formData.fileName = originalImage.name || ''
      formData.imagePreview = originalImage.src || ''

      // 如果有图像ID，添加到数组中
      if (originalImage.id) {
        const imageId = String(originalImage.id)
        formData.imageIds = [imageId]

        // 检查是否有该图像的标注数据
        checkAndLoadAnnotationData(imageId)
      }

      ElMessage.success('已恢复原始图像数据，可以开始标注！')
    }
  }
}

// 监听表单字段变化，同步到Pinia
watch(
  () => formData.examPart,
  (newValue) => {
    imageStore.setExamInfo(newValue, formData.sliceType)
  },
)

watch(
  () => formData.sliceType,
  (newValue) => {
    imageStore.setExamInfo(formData.examPart, newValue)
  },
)

// 监听状态管理中的标注后图片更新
watch(
  () => imageStore.hasAnnotatedImage(),
  (hasAnnotated) => {
    if (hasAnnotated) {
      // 重新初始化，会自动加载最新的标注后图片
      initializeFromStore()
    }
  },
  { immediate: false },
)

// 组件挂载时初始化
onMounted(() => {
  initializeFromStore()
})

// 定义事件
const emit = defineEmits<{
  submit: [data: any]
}>()
</script>
<template>
  <div class="medical-data-collection">
    <div class="form-container">
      <el-form ref="formRef" :model="formData" :rules="rules">
        <div class="form-layout">
          <!-- 左栏：图像上传和预览 -->
          <div class="left-column">
            <ImageUploadSection
              :form-data="formData"
              :exam-part-options="examPartOptions"
              :slice-type-options="sliceTypeOptions"
              @update:image-file="formData.imageFile = $event"
              @update:image-preview="formData.imagePreview = $event"
              @update:file-name="formData.fileName = $event"
              @update:exam-part="formData.examPart = $event"
              @update:slice-type="formData.sliceType = $event"
              @upload-success="handleUploadSuccess"
              @upload-error="handleUploadError"
            />
          </div>

          <!-- 中栏：病灶信息、图像特征分析 -->
          <div class="middle-column">
            <LesionInfoSection :form-data="formData" @update:report="formData.report = $event" />

            <ImageAnalysisSection :form-data="formData" @update:caption="formData.caption = $event" />
          </div>

          <!-- 右栏：诊断扩展信息 -->
          <div class="right-column">
            <DiagnosisSection
              :form-data="formData"
              @update:classification="formData.classification = $event"
              @update:detail="formData.detail = $event"
              @update:remark="formData.remark = $event"
              @update:associate="formData.associate = $event"
              @update:basis="formData.basis = $event"
            />
          </div>
        </div>
      </el-form>
      <FormActionsSection
        :loading="loading"
        @preview-data="previewData"
        @submit-form="submitForm"
        @reset-form="resetForm"
      />
    </div>

    <!-- 预览对话框 -->
    <DataPreview v-model:visible="previewVisible" :form-data="formData" :diagnosis-type-text="classificationText" />
  </div>
</template>

<style scoped>
.medical-data-collection {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0px;
  background: var(--bg-secondary);
  min-height: calc(100vh - 70px);
}

.form-container {
  background: transparent;
}

/* 三栏布局 */
.form-layout {
  display: grid;
  grid-template-columns: 1.2fr 1fr 1fr;
  grid-template-areas:
    'left middle right'
    'left actions actions';
  gap: 24px;
  margin-bottom: 24px;
  align-items: start;
}

.left-column {
  grid-area: left;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 600px;
}

.middle-column {
  grid-area: middle;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 600px;
}

.right-column {
  grid-area: right;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 600px;
}

/* 表单布局优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
  line-height: 1.6;
  font-size: 14px;
}

:deep(.el-form-item__content) {
  line-height: 1.6;
}

/* 按钮统一样式 */
:deep(.el-button) {
  height: 36px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}

/* 单选按钮组样式优化 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-radio) {
  margin-right: 0;
  white-space: nowrap;
}

:deep(.el-radio__label) {
  font-size: 14px;
  color: #606266;
}

/* 输入框样式优化 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: box-shadow 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .form-layout {
    gap: 20px;
  }

  .card-container {
    padding: 20px;
  }

  .section-header h3 {
    font-size: 15px;
  }
}

@media (max-width: 1200px) {
  .medical-data-collection {
    padding: 20px;
  }

  .form-layout {
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      'left middle'
      'right right'
      'actions actions';
    gap: 18px;
  }

  .left-column,
  .middle-column,
  .right-column {
    gap: 18px;
  }
}

@media (max-width: 992px) {
  .form-layout {
    grid-template-columns: 1fr;
    grid-template-areas:
      'left'
      'middle'
      'right'
      'actions';
    gap: 16px;
  }

  .left-column,
  .middle-column,
  .right-column {
    gap: 16px;
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .medical-data-collection {
    padding: 16px;
  }

  .card-container {
    padding: 16px;
  }

  .section-header {
    margin-bottom: 16px;
    padding-bottom: 10px;
  }

  .section-header h3 {
    font-size: 14px;
  }

  .section-icon {
    font-size: 16px;
  }

  .form-actions {
    padding: 16px;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
