<template>
  <el-button icon="Refresh" size="default" circle @click="updateRefresh"></el-button>
  <el-button icon="FullScreen" size="default" circle @click="fullScreen"></el-button>
  <!-- 暗色模式切换按钮 -->
  <el-button
    :icon="layoutSettingStore.themeIcon"
    size="default"
    circle
    @click="toggleDarkMode"
    :title="layoutSettingStore.darkMode ? '切换到亮色模式' : '切换到暗色模式'"
  ></el-button>
  <el-button icon="Setting" size="default" circle></el-button>
  <UserAvatar :avatar="userStore.avatar" :size="32" :clickable="false" style="margin: 0px 10px" />
  <!-- 右侧下拉 -->
  <el-dropdown style="cursor: pointer">
    <span class="el-dropdown-link">
      {{ userStore.username }}
      <el-icon class="el-icon--right">
        <arrow-down />
      </el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="goToProfile">个人中心</el-dropdown-item>
        <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import useLayoutSettingStore from '@/store/modules/setting'
import useUserStore from '@/store/modules/user'
import { useRouter, useRoute } from 'vue-router'
import { onMounted } from 'vue'

let $router = useRouter()
let $route = useRoute()

let userStore = useUserStore()
let layoutSettingStore = useLayoutSettingStore()

defineOptions({
  name: 'Setting',
})

let setting = useLayoutSettingStore()
const updateRefresh = () => {
  setting.refresh = !setting.refresh
}

const fullScreen = () => {
  let flag = document.fullscreenElement
  if (flag) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const toggleDarkMode = () => {
  layoutSettingStore.toggleDarkMode()
}

const goToProfile = () => {
  $router.push('/layout/profile')
}

const logout = () => {
  userStore.userLogout()
  $router.push({ path: '/login', query: { redirect: $route.path } })
}

// 初始化主题
onMounted(() => {
  layoutSettingStore.initTheme()
})
</script>

<style scoped lang="scss"></style>
