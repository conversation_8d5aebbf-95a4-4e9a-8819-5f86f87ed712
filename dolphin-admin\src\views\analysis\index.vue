<template>
  <div class="image-analysis-container">
    <!-- 使用ElementPlus容器布局 -->
    <el-container class="main-container">
      <!-- 左侧面板：病例输入 -->
      <el-aside width="320px" class="left-aside">
        <div class="panel-wrapper">
          <!-- 病例输入面板（包含医学影像上传） -->
          <ClinicalInputPanel
            :clinical-question="clinicalQuestion"
            :uploaded-images="uploadedImages"
            :current-image-index="currentImageIndex"
            :lesion-data="lesionData"
            @update:clinical-question="clinicalQuestion = $event"
            @clear-input="handleClearAll"
            @file-selected="handleFileSelected"
            @select-image="selectImage"
            @remove-image="handleRemoveImage"
            @lesion-input-change="handleLesionInputChange"
          />
        </div>
      </el-aside>

      <!-- 中间主要内容区域 -->
      <el-main class="main-content">
        <!-- 图像显示区域 -->
        <div class="image-display-area">
          <!-- 图像工作区 -->
          <ImageWorkspace
            ref="imageWorkspaceRef"
            v-if="hasImages"
            :image-data="currentImageData"
            :canvas-width="720"
            :canvas-height="720"
            :initial-mode="'draw-point'"
            :lesion-description="lesionData"
            @canvas-ready="handleCanvasReady"
            @image-loaded="handleImageLoad"
            @annotations-updated="handleAnnotationsUpdated"
            @annotation-saved="handleAnnotationSaved"
            @canvas-error="handleCanvasError"
            @lesion-exported="handleLesionExported"
          />

          <!-- 空状态 -->
          <EmptyStateDisplay v-else />
        </div>
      </el-main>

      <!-- 右侧面板：标注信息 -->
      <el-aside width="300px" class="right-aside">
        <div class="panel-wrapper">
          <AnnotationInfoPanel
            @lesion-click="handleLesionClick"
            @lesion-deleted="handleLesionDeleted"
            @delete-lesion-from-canvas="handleDeleteLesionFromCanvas"
            @save-all-lesions="handleSaveAllLesions"
          />
        </div>
      </el-aside>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reqUploadImage } from '@/api/data'

// 导入组件
import ClinicalInputPanel from './components/panels/ClinicalInputPanel.vue'
import ImageWorkspace from './components/workspace/ImageWorkspace.vue'
import AnnotationInfoPanel from './components/annotation/AnnotationInfoPanel.vue'
import EmptyStateDisplay from './components/common/EmptyStateDisplay.vue'

// 导入composables和状态管理
import { useImageUpload } from './composables/useImageUpload.js'
import useImageStore from '@/store/modules/image'

// 初始化路由和状态管理
const router = useRouter()
const imageStore = useImageStore()

// 使用composables
const {
  uploadedImages,
  currentImageIndex,
  currentImageData,
  hasImages,
  handleFileSelected,
  selectImage,
  removeImage,
  clearAllImages,
} = useImageUpload()

// 病例输入数据
const clinicalQuestion = ref('')
const lesionData = ref('')

// 组件引用
const imageWorkspaceRef = ref(null)

// 标注相关数据
const annotations = ref([]) // 已保存的标注

// 事件处理方法
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有标注信息吗？图片将保留，但所有标注数据将被清除。', '重置确认', {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 清除输入框数据
    clinicalQuestion.value = ''
    lesionData.value = ''

    // 清除ImageStore中的标注数据（保留图片）
    imageStore.clearAnnotationsOnly()

    // 清除画布上的标注（保留背景图片）
    if (imageWorkspaceRef.value) {
      imageWorkspaceRef.value.clearAllAnnotations()
    }

    // 清除本地标注数据
    annotations.value = []

    ElMessage.success('标注信息已重置，图片已保留')
  } catch {
    // 用户取消重置
  }
}

// 处理病灶输入变化
const handleLesionInputChange = (value) => {
  lesionData.value = value
  // 同步到状态管理
  imageStore.setCurrentLesionDescription(value)
}

const handleRemoveImage = (index) => {
  const isEmpty = removeImage(index)
  if (isEmpty) {
    handleClearAll()
  }
}

// Canvas 相关事件处理
const handleCanvasReady = (canvas) => {
  // Canvas 初始化完成
}

const handleImageLoad = (imageInfo) => {
  // 图像加载完成
}

const handleCanvasError = (error) => {
  // Canvas 错误处理
}

// 标注相关事件处理
const handleAnnotationsUpdated = (newAnnotations) => {
  annotations.value = newAnnotations
}

const handleAnnotationSaved = (savedAnnotations) => {
  // 标注已保存
}

// 病灶相关事件处理
const handleLesionClick = () => {
  // 可以在这里处理病灶选中逻辑，比如高亮显示等
}

const handleLesionDeleted = () => {
  ElMessage.success('病灶删除成功')
}

// 处理从画布删除病灶标注区域
const handleDeleteLesionFromCanvas = (deleteInfo) => {
  if (imageWorkspaceRef.value && deleteInfo.color) {
    // 获取病灶的标注点信息
    const imageStore = useImageStore()
    const lesion = imageStore.getLesionById(deleteInfo.lesionId)
    const lesionPoints = lesion ? lesion.points : []

    const success = imageWorkspaceRef.value.deleteClosedRegion(deleteInfo.color, lesionPoints)
    if (success) {
      // 保存更新后的Canvas快照
      setTimeout(() => {
        imageWorkspaceRef.value.saveCanvasSnapshot()
      }, 100)
    }
  }
}

const handleSaveAllLesions = async (lesions) => {
  if (lesions.length === 0) {
    ElMessage.warning('没有病灶可保存')
    return
  }

  try {
    // 清除之前的Canvas快照imageId，确保上传新的图像
    imageStore.clearCanvasSnapshotImageId()

    // 保存Canvas快照到前端
    if (imageWorkspaceRef.value) {
      const snapshotSuccess = imageWorkspaceRef.value.saveCanvasSnapshot()
      if (!snapshotSuccess) {
        ElMessage.warning('Canvas快照保存失败')
        return
      }
    }

    // 获取Canvas快照文件并上传
    const annotatedImage = imageStore.getLatestAnnotatedImage()
    if (annotatedImage && annotatedImage.file) {
      try {
        // 调用上传接口
        const result = await reqUploadImage(annotatedImage.file)
        const canvasImageId = typeof result === 'string' ? result : result.data

        if (canvasImageId) {
          // 保存Canvas快照的imageId到ImageStore
          imageStore.setCanvasSnapshotImageId(canvasImageId)
          ElMessage.success(`已保存 ${lesions.length} 个病灶并上传Canvas快照`)
        } else {
          throw new Error('上传返回的imageId为空')
        }
      } catch (uploadError) {
        ElMessage.error('Canvas快照上传失败，请重试')
        return
      }
    } else {
      ElMessage.warning('没有Canvas快照可上传')
      return
    }

    // 跳转到数据采集页面
    setTimeout(() => {
      router.push('/screen/collection')
    }, 1000)
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  }
}

// 导出病灶处理
const handleLesionExported = () => {
  // 调用ImageStore的导出方法
  const lesionId = imageStore.exportCurrentLesion()

  if (lesionId) {
    ElMessage.success(`病灶导出成功！已添加到标注信息列表`)

    // 获取导出的病灶信息（包含颜色）
    const exportedLesions = imageStore.getExportedLesions()
    const currentLesion = exportedLesions.find((lesion) => lesion.id === lesionId)

    // 创建闭合区域
    if (currentLesion && currentLesion.points.length >= 3) {
      // 调用ImageWorkspace的创建闭合区域方法
      if (imageWorkspaceRef.value) {
        imageWorkspaceRef.value.createClosedRegion(currentLesion.points, currentLesion.color)

        // 等待Canvas渲染完成后保存快照
        setTimeout(() => {
          imageWorkspaceRef.value.saveCanvasSnapshot()
        }, 100) // 给Canvas一点时间完成渲染
      }
    }

    // 清空当前病灶输入框
    lesionData.value = ''

    // 清空当前标注数据
    annotations.value = []
  } else {
    ElMessage.error('导出失败：请确保已填写病灶信息且至少标注3个点')
  }
}

const initializeFromStore = () => {
  // 清理过期的图像数据
  imageStore.cleanupExpiredImage()

  // 恢复当前病灶信息
  const savedLesionDescription = imageStore.getCurrentLesionDescription()
  if (savedLesionDescription) {
    lesionData.value = savedLesionDescription
  }

  // 检查是否有Canvas快照（优先加载）
  if (imageStore.hasCanvasSnapshot()) {
    const latestAnnotatedImage = imageStore.getLatestAnnotatedImage()

    if (latestAnnotatedImage) {
      // 将带标注的图像数据添加到上传列表
      const imageData = {
        id: latestAnnotatedImage.imageId,
        name: 'annotated-image.png',
        src: latestAnnotatedImage.previewUrl,
        size: 0,
        type: 'image/png',
        originalWidth: 720,
        originalHeight: 720,
        uploadTime: new Date(latestAnnotatedImage.timestamp).toISOString(),
      }

      handleFileSelected(imageData)

      // 恢复当前标注数据（从状态管理）
      const savedAnnotations = imageStore.getCurrentAnnotations()
      if (savedAnnotations.length > 0) {
        annotations.value = savedAnnotations
      }

      // 恢复已导出的病灶（重新创建闭合区域）
      const exportedLesions = imageStore.getExportedLesions()
      if (exportedLesions.length > 0) {
        ElMessage.success(`已恢复完整的标注图像：${exportedLesions.length}个病灶`)
      } else {
        ElMessage.success('已恢复标注图像')
      }

      return // 优先使用快照，不再加载原始图像
    }
  }

  // 如果没有快照，则检查是否有原始裁剪后的图像数据
  if (imageStore.hasOriginalCroppedImage()) {
    const originalImage = imageStore.getOriginalCroppedImage()

    if (originalImage) {
      // 将原始图像数据添加到上传列表（用于标注）
      handleFileSelected(originalImage)

      // 恢复当前标注数据（从状态管理）
      const savedAnnotations = imageStore.getCurrentAnnotations()
      if (savedAnnotations.length > 0) {
        annotations.value = savedAnnotations
        ElMessage.info(`恢复当前标注数据：${savedAnnotations.length}个标注点`)
      }

      ElMessage.success('已自动加载原始图像，可以开始标注了！')
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  initializeFromStore()
})
</script>

<style scoped>
.image-analysis-container {
  height: 90vh;
  background: var(--bg-secondary);
  overflow: auto;
  padding: 0px;
}

.main-container {
  height: 100%;
  background: transparent;
  overflow: visible;
  gap: 0;
}

/* 左侧面板 */
.left-aside {
  background: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  box-shadow: var(--shadow-light);
  overflow: auto;
  position: relative;
}

.left-aside::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  opacity: 0.8;
}

.panel-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0;
  overflow: hidden; /* 改为hidden，让子组件控制滚动 */
}

/* 右侧面板 */
.right-aside {
  background: var(--bg-primary);
  border-left: 1px solid var(--border-primary);
  box-shadow: var(--shadow-light);
  overflow: hidden; /* 改为hidden，让子组件控制滚动 */
  position: relative;
}

.right-aside::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  opacity: 0.8;
}

/* 主内容区域 */
.main-content {
  background: transparent;
  padding: 0;
  overflow: auto;
  min-height: 0;
}

/* 图像显示区域直接占据整个主内容区域 */
.image-display-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: var(--bg-primary);
  position: relative;
}

.image-display-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-aside {
    width: 280px !important;
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .left-aside {
    width: 100% !important;
    height: auto;
    max-height: 300px;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track, #f1f5f9);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb, #cbd5e1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover, #94a3b8);
}

::-webkit-scrollbar-corner {
  background: var(--scrollbar-track, #f1f5f9);
}

/* 全局样式增强 */
:deep(.el-card) {
  border-radius: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  /* transform: translateY(-1px); */
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}
</style>
