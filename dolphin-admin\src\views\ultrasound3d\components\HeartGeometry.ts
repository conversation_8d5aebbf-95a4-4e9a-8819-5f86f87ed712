// 真实医学心脏解剖几何体生成工具
import vtkPolyData from '@kitware/vtk.js/Common/DataModel/PolyData'
import vtkPoints from '@kitware/vtk.js/Common/Core/Points'
import vtkCellArray from '@kitware/vtk.js/Common/Core/CellArray'

// 心脏解剖结构的真实比例和位置参数
const HEART_ANATOMY = {
  // 左心室 - 最大的腔室
  leftVentricle: {
    position: [-0.3, -0.2, 0],
    size: [1.2, 1.5, 1.0],
    wallThickness: 0.15,
    color: [0.8, 0.2, 0.2], // 深红色
  },
  // 右心室 - 较小，位于前方
  rightVentricle: {
    position: [0.4, -0.1, 0.2],
    size: [1.0, 1.3, 0.8],
    wallThickness: 0.08,
    color: [0.9, 0.3, 0.3], // 较浅红色
  },
  // 左心房 - 位于后上方
  leftAtrium: {
    position: [-0.4, 0.8, -0.2],
    size: [0.8, 0.7, 0.8],
    wallThickness: 0.05,
    color: [0.7, 0.4, 0.4], // 浅红色
  },
  // 右心房 - 位于右上方
  rightAtrium: {
    position: [0.3, 0.7, 0.1],
    size: [0.9, 0.8, 0.7],
    wallThickness: 0.05,
    color: [0.6, 0.3, 0.6], // 紫红色
  },
  // 主动脉 - 从左心室出发
  aorta: {
    segments: [
      { start: [-0.2, 0.5, 0], end: [-0.1, 1.2, 0], radius: 0.12 },
      { start: [-0.1, 1.2, 0], end: [0.2, 1.4, -0.1], radius: 0.11 },
      { start: [0.2, 1.4, -0.1], end: [0.5, 1.3, -0.2], radius: 0.10 },
    ],
    color: [1.0, 0.5, 0.5], // 鲜红色（动脉血）
  },
  // 肺动脉 - 从右心室出发
  pulmonaryArtery: {
    segments: [
      { start: [0.3, 0.4, 0.2], end: [0.2, 1.0, 0.3], radius: 0.10 },
      { start: [0.2, 1.0, 0.3], end: [-0.1, 1.2, 0.4], radius: 0.09 },
      { start: [-0.1, 1.2, 0.4], end: [-0.4, 1.1, 0.5], radius: 0.08 },
    ],
    color: [0.4, 0.4, 0.8], // 蓝紫色（静脉血）
  },
  // 上腔静脉
  superiorVenaCava: {
    segments: [
      { start: [0.4, 1.5, 0.1], end: [0.3, 0.9, 0.1], radius: 0.08 },
    ],
    color: [0.3, 0.3, 0.7], // 深蓝色
  },
  // 下腔静脉
  inferiorVenaCava: {
    segments: [
      { start: [0.2, -1.2, 0], end: [0.3, 0.5, 0.1], radius: 0.09 },
    ],
    color: [0.3, 0.3, 0.7], // 深蓝色
  },
}

/**
 * 创建真实的医学解剖心脏几何体 - 左心室
 */
export function createLeftVentricleGeometry(): any {
  const anatomy = HEART_ANATOMY.leftVentricle
  return createChamberGeometry(anatomy.position, anatomy.size, anatomy.wallThickness, 'ventricle')
}

/**
 * 创建右心室几何体
 */
export function createRightVentricleGeometry(): any {
  const anatomy = HEART_ANATOMY.rightVentricle
  return createChamberGeometry(anatomy.position, anatomy.size, anatomy.wallThickness, 'ventricle')
}

/**
 * 创建左心房几何体
 */
export function createLeftAtriumGeometry(): any {
  const anatomy = HEART_ANATOMY.leftAtrium
  return createChamberGeometry(anatomy.position, anatomy.size, anatomy.wallThickness, 'atrium')
}

/**
 * 创建右心房几何体
 */
export function createRightAtriumGeometry(): any {
  const anatomy = HEART_ANATOMY.rightAtrium
  return createChamberGeometry(anatomy.position, anatomy.size, anatomy.wallThickness, 'atrium')
}

/**
 * 创建心脏腔室的通用几何体
 */
function createChamberGeometry(
  position: number[],
  size: number[],
  wallThickness: number,
  type: 'ventricle' | 'atrium'
): any {
  const points = vtkPoints.newInstance()
  const polys = vtkCellArray.newInstance()
  const polyData = vtkPolyData.newInstance()

  const chamberPoints: number[] = []
  const chamberCells: number[] = []

  const uResolution = type === 'ventricle' ? 24 : 20
  const vResolution = type === 'ventricle' ? 32 : 24

  // 创建外壁和内壁
  for (let wall = 0; wall < 2; wall++) {
    const isOuter = wall === 0
    const thickness = isOuter ? 1.0 : (1.0 - wallThickness)

    for (let u = 0; u < uResolution; u++) {
      for (let v = 0; v < vResolution; v++) {
        const uParam = (u / (uResolution - 1)) * Math.PI
        const vParam = (v / (vResolution - 1)) * 2 * Math.PI

        let x, y, z

        if (type === 'ventricle') {
          // 心室形状：椭球体，底部较尖
          const tapering = Math.pow(Math.sin(uParam), 1.5) // 底部收缩
          x = thickness * size[0] * tapering * Math.sin(uParam) * Math.cos(vParam) + position[0]
          y = thickness * size[1] * (Math.cos(uParam) - 0.3) + position[1] // 向下偏移
          z = thickness * size[2] * tapering * Math.sin(uParam) * Math.sin(vParam) + position[2]
        } else {
          // 心房形状：扁平的椭球体
          x = thickness * size[0] * Math.sin(uParam) * Math.cos(vParam) + position[0]
          y = thickness * size[1] * Math.cos(uParam) * 0.6 + position[1] // 较扁平
          z = thickness * size[2] * Math.sin(uParam) * Math.sin(vParam) + position[2]
        }

        chamberPoints.push(x, y, z)
      }
    }
  }

  // 生成三角形面片
  const pointsPerWall = uResolution * vResolution

  // 外壁面片
  for (let u = 0; u < uResolution - 1; u++) {
    for (let v = 0; v < vResolution - 1; v++) {
      const i0 = u * vResolution + v
      const i1 = (u + 1) * vResolution + v
      const i2 = (u + 1) * vResolution + ((v + 1) % vResolution)
      const i3 = u * vResolution + ((v + 1) % vResolution)

      chamberCells.push(3, i0, i1, i2)
      chamberCells.push(3, i0, i2, i3)
    }
  }

  // 内壁面片（法向量反向）
  for (let u = 0; u < uResolution - 1; u++) {
    for (let v = 0; v < vResolution - 1; v++) {
      const offset = pointsPerWall
      const i0 = offset + u * vResolution + v
      const i1 = offset + (u + 1) * vResolution + v
      const i2 = offset + (u + 1) * vResolution + ((v + 1) % vResolution)
      const i3 = offset + u * vResolution + ((v + 1) % vResolution)

      // 反向法向量
      chamberCells.push(3, i0, i2, i1)
      chamberCells.push(3, i0, i3, i2)
    }
  }

  points.setData(new Float32Array(chamberPoints))
  polys.setData(new Uint32Array(chamberCells))

  polyData.setPoints(points)
  polyData.setPolys(polys)

  return polyData
}

/**
 * 创建主动脉几何体
 */
export function createAortaGeometry(): any {
  return createVesselGeometry(HEART_ANATOMY.aorta.segments)
}

/**
 * 创建肺动脉几何体
 */
export function createPulmonaryArteryGeometry(): any {
  return createVesselGeometry(HEART_ANATOMY.pulmonaryArtery.segments)
}

/**
 * 创建上腔静脉几何体
 */
export function createSuperiorVenaCavaGeometry(): any {
  return createVesselGeometry(HEART_ANATOMY.superiorVenaCava.segments)
}

/**
 * 创建下腔静脉几何体
 */
export function createInferiorVenaCavaGeometry(): any {
  return createVesselGeometry(HEART_ANATOMY.inferiorVenaCava.segments)
}

/**
 * 创建血管几何体的通用函数
 */
function createVesselGeometry(segments: Array<{ start: number[]; end: number[]; radius: number }>): any {
  const points = vtkPoints.newInstance()
  const polys = vtkCellArray.newInstance()
  const polyData = vtkPolyData.newInstance()

  const vesselPoints: number[] = []
  const vesselCells: number[] = []

  const radialResolution = 12 // 圆周分辨率
  let pointIndex = 0

  segments.forEach((segment) => {
    const { start, end, radius } = segment
    const segmentLength = Math.sqrt(
      Math.pow(end[0] - start[0], 2) +
      Math.pow(end[1] - start[1], 2) +
      Math.pow(end[2] - start[2], 2)
    )

    const lengthResolution = Math.max(8, Math.floor(segmentLength * 10))

    for (let i = 0; i <= lengthResolution; i++) {
      const t = i / lengthResolution

      // 沿血管轴线的点
      const centerX = start[0] + t * (end[0] - start[0])
      const centerY = start[1] + t * (end[1] - start[1])
      const centerZ = start[2] + t * (end[2] - start[2])

      // 计算垂直于血管轴线的两个向量
      const direction = [end[0] - start[0], end[1] - start[1], end[2] - start[2]]
      const length = Math.sqrt(direction[0] ** 2 + direction[1] ** 2 + direction[2] ** 2)
      direction[0] /= length
      direction[1] /= length
      direction[2] /= length

      // 找到垂直向量
      let perpendicular1 = [0, 0, 0]
      if (Math.abs(direction[0]) < 0.9) {
        perpendicular1 = [1, 0, 0]
      } else {
        perpendicular1 = [0, 1, 0]
      }

      // 叉积得到真正的垂直向量
      const perpendicular2 = [
        direction[1] * perpendicular1[2] - direction[2] * perpendicular1[1],
        direction[2] * perpendicular1[0] - direction[0] * perpendicular1[2],
        direction[0] * perpendicular1[1] - direction[1] * perpendicular1[0]
      ]

      // 归一化
      const len2 = Math.sqrt(perpendicular2[0] ** 2 + perpendicular2[1] ** 2 + perpendicular2[2] ** 2)
      perpendicular2[0] /= len2
      perpendicular2[1] /= len2
      perpendicular2[2] /= len2

      // 第三个垂直向量
      const perpendicular3 = [
        direction[1] * perpendicular2[2] - direction[2] * perpendicular2[1],
        direction[2] * perpendicular2[0] - direction[0] * perpendicular2[2],
        direction[0] * perpendicular2[1] - direction[1] * perpendicular2[0]
      ]

      // 创建圆形截面
      for (let j = 0; j < radialResolution; j++) {
        const angle = (j / radialResolution) * 2 * Math.PI
        const cosAngle = Math.cos(angle)
        const sinAngle = Math.sin(angle)

        const x = centerX + radius * (cosAngle * perpendicular2[0] + sinAngle * perpendicular3[0])
        const y = centerY + radius * (cosAngle * perpendicular2[1] + sinAngle * perpendicular3[1])
        const z = centerZ + radius * (cosAngle * perpendicular2[2] + sinAngle * perpendicular3[2])

        vesselPoints.push(x, y, z)
      }

      // 生成面片（除了最后一层）
      if (i < lengthResolution) {
        for (let j = 0; j < radialResolution; j++) {
          const current = pointIndex + j
          const next = pointIndex + ((j + 1) % radialResolution)
          const currentNext = pointIndex + radialResolution + j
          const nextNext = pointIndex + radialResolution + ((j + 1) % radialResolution)

          // 创建四边形的两个三角形
          vesselCells.push(3, current, next, currentNext)
          vesselCells.push(3, next, nextNext, currentNext)
        }
      }

      pointIndex += radialResolution
    }
  })

  points.setData(new Float32Array(vesselPoints))
  polys.setData(new Uint32Array(vesselCells))

  polyData.setPoints(points)
  polyData.setPolys(polys)

  return polyData
}

/**
 * 创建完整的医学解剖心脏模型（主要用于兼容性）
 * 实际使用中建议分别创建各个解剖结构
 */
export function createSimpleHeartGeometry(): any {
  // 返回左心室作为主要结构
  return createLeftVentricleGeometry()
}

/**
 * 获取心脏解剖结构的颜色配置
 */
export function getHeartAnatomyColors() {
  return {
    leftVentricle: HEART_ANATOMY.leftVentricle.color,
    rightVentricle: HEART_ANATOMY.rightVentricle.color,
    leftAtrium: HEART_ANATOMY.leftAtrium.color,
    rightAtrium: HEART_ANATOMY.rightAtrium.color,
    aorta: HEART_ANATOMY.aorta.color,
    pulmonaryArtery: HEART_ANATOMY.pulmonaryArtery.color,
    superiorVenaCava: HEART_ANATOMY.superiorVenaCava.color,
    inferiorVenaCava: HEART_ANATOMY.inferiorVenaCava.color,
  }
}

/**
 * 创建心室腔室几何体
 */
export function createVentricleGeometry(isLeft: boolean = true): any {
  const points = vtkPoints.newInstance()
  const polys = vtkCellArray.newInstance()
  const polyData = vtkPolyData.newInstance()

  const ventriclePoints: number[] = []
  const ventricleCells: number[] = []

  const uResolution = 20
  const vResolution = 20
  const scale = isLeft ? 1.0 : 0.8 // 左心室比右心室大

  for (let u = 0; u < uResolution; u++) {
    for (let v = 0; v < vResolution; v++) {
      const uParam = (u / (uResolution - 1)) * Math.PI
      const vParam = (v / (vResolution - 1)) * 2 * Math.PI

      // 椭球体方程，模拟心室形状
      const x = scale * 0.6 * Math.sin(uParam) * Math.cos(vParam)
      const y = scale * 0.8 * Math.cos(uParam) - 0.3 // 向下偏移
      const z = scale * 0.5 * Math.sin(uParam) * Math.sin(vParam)

      ventriclePoints.push(x, y, z)
    }
  }

  // 生成三角形面片
  for (let u = 0; u < uResolution - 1; u++) {
    for (let v = 0; v < vResolution - 1; v++) {
      const i0 = u * vResolution + v
      const i1 = (u + 1) * vResolution + v
      const i2 = (u + 1) * vResolution + ((v + 1) % vResolution)
      const i3 = u * vResolution + ((v + 1) % vResolution)

      ventricleCells.push(3, i0, i1, i2)
      ventricleCells.push(3, i0, i2, i3)
    }
  }

  points.setData(new Float32Array(ventriclePoints))
  polys.setData(new Uint32Array(ventricleCells))

  polyData.setPoints(points)
  polyData.setPolys(polys)

  return polyData
}

/**
 * 创建心房几何体
 */
export function createAtriumGeometry(): any {
  const points = vtkPoints.newInstance()
  const polys = vtkCellArray.newInstance()
  const polyData = vtkPolyData.newInstance()

  const atriumPoints: number[] = []
  const atriumCells: number[] = []

  const uResolution = 16
  const vResolution = 16

  for (let u = 0; u < uResolution; u++) {
    for (let v = 0; v < vResolution; v++) {
      const uParam = (u / (uResolution - 1)) * Math.PI
      const vParam = (v / (vResolution - 1)) * 2 * Math.PI

      // 扁平的椭球体，模拟心房
      const x = 0.8 * Math.sin(uParam) * Math.cos(vParam)
      const y = 0.4 * Math.cos(uParam) + 0.6 // 向上偏移
      const z = 0.6 * Math.sin(uParam) * Math.sin(vParam)

      atriumPoints.push(x, y, z)
    }
  }

  // 生成三角形面片
  for (let u = 0; u < uResolution - 1; u++) {
    for (let v = 0; v < vResolution - 1; v++) {
      const i0 = u * vResolution + v
      const i1 = (u + 1) * vResolution + v
      const i2 = (u + 1) * vResolution + ((v + 1) % vResolution)
      const i3 = u * vResolution + ((v + 1) % vResolution)

      atriumCells.push(3, i0, i1, i2)
      atriumCells.push(3, i0, i2, i3)
    }
  }

  points.setData(new Float32Array(atriumPoints))
  polys.setData(new Uint32Array(atriumCells))

  polyData.setPoints(points)
  polyData.setPolys(polys)

  return polyData
}
