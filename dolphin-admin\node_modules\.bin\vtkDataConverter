#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/DataGenerator/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/DataGenerator/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/DataGenerator/convert-cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/@kitware+vtk.js@34.5.0_@bab_7142d5967661f9acad200e24e836be2a/node_modules/@kitware/vtk.js/Utilities/DataGenerator/convert-cli.js" "$@"
fi
