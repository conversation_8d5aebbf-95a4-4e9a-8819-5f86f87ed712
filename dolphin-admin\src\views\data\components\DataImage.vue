<template>
  <div class="data-image">
    <!-- 图像显示 -->
    <el-image
      v-if="imageUrl"
      :src="imageUrl"
      :preview-src-list="previewUrls"
      fit="cover"
      :preview-teleported="true"
      class="image-display"
      @error="handleImageError"
    >
      <template #error>
        <div class="image-error">
          <el-icon><Picture /></el-icon>
          <span>加载失败</span>
        </div>
      </template>
    </el-image>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="image-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
    </div>

    <!-- 无图像状态 -->
    <div v-else class="image-empty">
      <el-icon><Picture /></el-icon>
      <span>无图像</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import { Picture, Loading } from '@element-plus/icons-vue'
import { reqGetImage } from '@/api/data'

// 定义组件名称
defineOptions({
  name: 'DataImage',
})

// 全局图像缓存，避免重复加载相同图像
const globalImageCache = new Map<string, Promise<string>>()

// 定义组件属性
interface Props {
  /** 主图像ID */
  imageId?: string
  /** 预览图像ID列表 */
  previewImageIds?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  imageId: '',
  previewImageIds: () => [],
})

// 响应式数据
const loading = ref(false)
const imageUrl = ref('')
const previewUrls = ref<string[]>([])
const imageObjectUrls = ref<string[]>([]) // 用于清理URL对象

// 加载单个图像（使用全局缓存）
const loadImage = async (imageId: string): Promise<string> => {
  // 检查全局缓存
  if (globalImageCache.has(imageId)) {
    return await globalImageCache.get(imageId)!
  }

  // 创建加载 Promise 并添加到缓存
  const loadPromise = (async () => {
    try {
      const response = await reqGetImage(imageId)
      // 响应拦截器应该返回 Blob，但类型推断可能有问题，所以做类型断言
      const imageBlob = response as unknown as Blob
      const url = URL.createObjectURL(imageBlob)
      imageObjectUrls.value.push(url) // 记录URL用于清理
      return url
    } catch (error) {
      // 如果加载失败，从缓存中移除
      globalImageCache.delete(imageId)
      console.error('加载图像失败:', error)
      throw error
    }
  })()

  globalImageCache.set(imageId, loadPromise)
  return await loadPromise
}

// 加载主图像
const loadMainImage = async () => {
  if (!props.imageId) {
    imageUrl.value = ''
    return
  }

  try {
    loading.value = true
    const url = await loadImage(props.imageId)
    imageUrl.value = url
  } catch (error) {
    console.error('加载主图像失败:', error)
    imageUrl.value = ''
  } finally {
    loading.value = false
  }
}

// 加载预览图像列表
const loadPreviewImages = async () => {
  if (!props.previewImageIds || props.previewImageIds.length === 0) {
    previewUrls.value = []
    return
  }

  try {
    // 使用全局缓存，自动避免重复加载
    const urls = await Promise.all(props.previewImageIds.map((id) => loadImage(id)))
    previewUrls.value = urls
  } catch (error) {
    console.error('加载预览图像失败:', error)
    previewUrls.value = []
  }
}

// 处理图像加载错误
const handleImageError = () => {
  console.error('图像显示错误')
}

// 清理URL对象
const cleanupUrls = () => {
  imageObjectUrls.value.forEach((url) => {
    URL.revokeObjectURL(url)
  })
  imageObjectUrls.value = []
}

// 监听属性变化
watch(
  () => props.imageId,
  () => {
    cleanupUrls()
    loadMainImage()
  },
  { immediate: true },
)

watch(
  () => props.previewImageIds,
  () => {
    loadPreviewImages()
  },
  { immediate: true, deep: true },
)

// 注意：不需要 onMounted，因为 watch 的 immediate: true 已经处理了初始化

// 组件卸载时清理URL对象
onUnmounted(() => {
  cleanupUrls()
})
</script>

<style scoped lang="scss">
.data-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  .image-display {
    width: 100%;
    height: 100%;
    border: 1px solid #e4e7ed;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }
  }

  .image-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    .el-icon {
      font-size: 16px;
      margin-bottom: 4px;
    }
  }

  .image-error,
  .image-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    .el-icon {
      font-size: 16px;
      margin-bottom: 4px;
    }

    span {
      font-size: 10px;
    }
  }
}
</style>
