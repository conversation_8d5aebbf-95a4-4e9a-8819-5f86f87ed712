<script setup lang="ts">
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'DataSearchBar',
})

// 定义props
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

// 定义emits
interface Emits {
  search: [params: { classification: string; diagnoseId: string }]
  reset: []
}

const emit = defineEmits<Emits>()

// 搜索表单
const searchForm = reactive({
  classification: '',
  diagnoseId: '',
})

// 搜索处理
const handleSearch = () => {
  emit('search', {
    classification: searchForm.classification,
    diagnoseId: searchForm.diagnoseId,
  })
}

// 重置搜索
const handleReset = () => {
  searchForm.classification = ''
  searchForm.diagnoseId = ''
  emit('reset')
  ElMessage.success('已重置搜索条件')
}
</script>

<template>
  <div class="search-bar">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-select v-model="searchForm.classification" placeholder="诊断分类" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="正常" value="normal"></el-option>
          <el-option label="良性" value="benign"></el-option>
          <el-option label="恶性" value="malignant"></el-option>
          <el-option label="未定" value="uncertain"></el-option>
          <el-option label="其他" value="other"></el-option>
        </el-select>
      </el-col>
      <el-col :span="10">
        <el-input v-model="searchForm.diagnoseId" placeholder="请输入诊断记录ID" clearable>
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
      </el-col>
      <el-col :span="4">
        <el-button type="info" @click="handleReset" :disabled="loading">重置</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--bg-primary);
  border-radius: 4px;
  border: 1px solid var(--border-primary);
}
</style>
