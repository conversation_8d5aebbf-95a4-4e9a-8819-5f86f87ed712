<template>
  <div class="ultrasound3d-container">
    <!-- 案例卡片列表 -->
    <div class="cases-grid">
      <div v-for="caseItem in heartCases" :key="caseItem.id" class="case-card" @click="openViewer(caseItem)">
        <div class="card-image">
          <img src="/src/assets/images/cancer/image.png" :alt="caseItem.title" class="case-image" />
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ caseItem.title }}</h3>
          <p class="card-description">{{ caseItem.description }}</p>
          <div class="card-meta">
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              {{ caseItem.date }}
            </span>
            <span class="meta-item">
              <el-icon><User /></el-icon>
              {{ caseItem.patient }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 3D查看器对话框 -->
    <el-dialog
      v-model="viewerVisible"
      :title="currentCase?.title"
      width="95%"
      :before-close="closeViewer"
      class="viewer-dialog"
      top="5vh"
    >
      <div class="viewer-container" ref="viewerContainer">
        <HeartViewer v-if="viewerVisible && showViewer" :case-data="currentCase" ref="heartViewerRef" />

        <div v-if="!showViewer" class="viewer-placeholder">
          <el-icon class="placeholder-icon"><View /></el-icon>
          <p>VTK.js 3D心脏超声查看器</p>
          <p class="placeholder-text">{{ currentCase?.title }}</p>
          <el-button type="primary" @click="initViewer">
            <el-icon><VideoPlay /></el-icon>
            启动3D查看器
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="viewer-controls">
          <el-button-group v-if="showViewer">
            <el-button @click="resetView">
              <el-icon><Refresh /></el-icon>
              重置视图
            </el-button>
            <el-button @click="togglePlay">
              <el-icon><VideoPlay /></el-icon>
              播放/暂停
            </el-button>
            <el-button @click="takeScreenshot">
              <el-icon><Camera /></el-icon>
              截图
            </el-button>
          </el-button-group>
          <el-button @click="closeViewer">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { View, VideoPlay, Calendar, User, Refresh, Camera } from '@element-plus/icons-vue'
import HeartViewer from './components/HeartViewer.vue'

// 定义案例数据类型
interface HeartCase {
  id: string
  title: string
  description: string
  date: string
  patient: string
  type: string
}

// 响应式数据
const viewerVisible = ref(false)
const currentCase = ref<HeartCase | null>(null)
const viewerContainer = ref<HTMLElement>()
const showViewer = ref(false)
const heartViewerRef = ref()

// 模拟心脏超声案例数据
const heartCases = reactive<HeartCase[]>([
  {
    id: '1',
    title: '3D心脏模型',
    description: '基于VTK.js的三维心脏可视化模型',
    date: '2024-01-15',
    patient: '演示案例',
    type: 'normal',
  },
])

// 打开3D查看器
const openViewer = (caseItem: HeartCase) => {
  currentCase.value = caseItem
  viewerVisible.value = true
  ElMessage.success(`正在加载 ${caseItem.title}`)
}

// 关闭查看器
const closeViewer = () => {
  viewerVisible.value = false
  showViewer.value = false
  currentCase.value = null
}

// 初始化VTK.js查看器
const initViewer = () => {
  ElMessage.info('正在启动3D心脏查看器...')
  showViewer.value = true
}

// 重置视图
const resetView = () => {
  if (heartViewerRef.value) {
    heartViewerRef.value.resetCamera()
  }
}

// 播放/暂停
const togglePlay = () => {
  if (heartViewerRef.value) {
    heartViewerRef.value.toggleAnimation()
  }
}

// 截图功能
const takeScreenshot = () => {
  ElMessage.success('截图功能开发中...')
  // TODO: 实现VTK.js截图功能
}

// 组件挂载
onMounted(() => {
  console.log('超声3D组件已挂载')
})
</script>

<style scoped lang="scss">
.ultrasound3d-container {
  padding: 20px;
  background: var(--bg-secondary); /* 使用主题变量 */
  min-height: calc(100vh - 120px);
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0;
  justify-content: start;
}

.case-card {
  background: var(--bg-primary); /* 使用主题变量 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-medium); /* 使用主题阴影 */
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--border-primary); /* 添加边框适应暗色模式 */

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy); /* 使用主题阴影 */
  }
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 12px 12px 0 0; /* 只有顶部圆角 */

  .case-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.card-content {
  padding: 20px;
  background: var(--medical-blue); /* 亮色模式：医疗蓝色 */
  border-radius: 0 0 12px 12px; /* 只有底部圆角 */
  position: relative;

  /* 暗色模式下的优化 */
  .dark-theme & {
    background: linear-gradient(135deg, var(--menu-bg) 0%, #2d4a6b 100%); /* 暗色模式：使用菜单背景色的渐变 */
  }

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff; /* 白色文字 */
    margin-bottom: 8px;
  }

  .card-description {
    color: rgba(255, 255, 255, 0.9); /* 半透明白色 */
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
  }

  .card-meta {
    display: flex;
    justify-content: space-between;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8); /* 半透明白色 */
    }
  }
}

// 暗色模式适配
.dark-theme {
  .case-card {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;

    &:hover {
      box-shadow: var(--shadow-heavy) !important;
    }
  }
}

.viewer-dialog {
  .viewer-container {
    height: 70vh;
    min-height: 500px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .viewer-placeholder {
      text-align: center;
      color: #6c757d;

      .placeholder-icon {
        font-size: 64px;
        margin-bottom: 20px;
        color: #dee2e6;
      }

      .placeholder-text {
        font-size: 18px;
        font-weight: 500;
        margin: 10px 0 20px;
        color: #495057;
      }
    }
  }

  .viewer-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }
}

// VTK.js 对话框特殊样式
:deep(.el-dialog__body) {
  padding: 10px !important;
}

:deep(.el-dialog) {
  margin-top: 5vh !important;
}

// 响应式设计
@media (max-width: 768px) {
  .cases-grid {
    grid-template-columns: 1fr;
    padding: 0 10px;
  }

  .ultrasound3d-container {
    padding: 15px;
  }
}
</style>
