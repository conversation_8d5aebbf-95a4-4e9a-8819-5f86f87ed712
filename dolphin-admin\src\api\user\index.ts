/**
 * 用户认证和用户信息相关接口
 *
 * API设计规范：
 * - 采用RESTful风格
 * - 版本化管理：/v1/模块/功能
 * - 统一响应格式
 * - 完整的类型定义
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import request from '@/utils/request'
import type {
  loginForm,
  loginResponse,
  logoutForm,
  logoutResponse,
  refreshTokenForm,
  refreshTokenResponse,
  userResponseData,
  updateProfileForm,
  editUserInfoForm,
  changePasswordForm
} from './type'

/**
 * API接口地址枚举
 *
 * 接口命名规范：
 * - v1: API版本号
 * - auth: 认证模块
 * - user: 用户信息模块
 * - 使用RESTful动词：GET/POST/PUT/DELETE
 */
enum API {
  // ==================== 认证模块 (v1/auth) ====================
  /** 用户登录 */
  LOGIN_URL = '/v1/auth/login',
  /** 用户登出 */
  LOGOUT_URL = '/v1/auth/logout',
  /** 刷新访问令牌 */
  REFRESH_TOKEN_URL = '/v1/auth/refresh',
  /** 验证令牌有效性 */
  VERIFY_TOKEN_URL = '/v1/auth/verify',

  // ==================== 用户信息模块 (v1/user) ====================
  /** 获取当前用户信息 */
  USER_INFO_URL = '/v1/user/info',
  /** 更新用户信息 */
  UPDATE_PROFILE_URL = '/v1/user/profile',
  /** 修改用户信息 */
  EDIT_USER_INFO_URL = '/v1/user/info/edit',
  /** 修改密码 */
  CHANGE_PASSWORD_URL = '/v1/user/password',
  /** 上传头像 */
  UPLOAD_AVATAR_URL = '/v1/user/avatar/upload',
  /** 获取头像 */
  GET_AVATAR_URL = '/v1/user/avatar',
}

// ==================== 认证相关接口 ====================

/**
 * 用户登录
 * POST /v1/auth/login
 *
 * @param data 登录表单数据
 * @returns Promise<loginResponse> 登录响应数据
 *
 * @example
 * ```typescript
 * const loginData = {
 *   username: 'admin',
 *   password: '123456',
 *   rememberMe: true
 * }
 * const result = await reqLogin(loginData)
 * console.log(result.accessToken) // 访问令牌
 * ```
 */
export const reqLogin = (data: loginForm) =>
  request.post<loginResponse>(API.LOGIN_URL, data)

/**
 * 用户登出
 * POST /v1/auth/logout
 *
 * @param data 登出请求数据（可选）
 * @returns Promise<logoutResponse> 登出响应数据
 */
export const reqLogout = (data?: logoutForm) =>
  request.post<logoutResponse>(API.LOGOUT_URL, data || {})

/**
 * 刷新访问令牌
 * POST /v1/auth/refresh
 *
 * @param data 刷新令牌数据
 * @returns Promise<refreshTokenResponse> 新的令牌数据
 */
export const reqRefreshToken = (data: refreshTokenForm) =>
  request.post<refreshTokenResponse>(API.REFRESH_TOKEN_URL, data)

/**
 * 验证令牌有效性
 * GET /v1/auth/verify
 *
 * @returns Promise<{valid: boolean}> 令牌验证结果
 */
export const reqVerifyToken = () =>
  request.get<{ valid: boolean }>(API.VERIFY_TOKEN_URL)

// ==================== 用户信息相关接口 ====================

/**
 * 获取当前用户信息
 * GET /v1/user/info
 *
 * @returns Promise<userResponseData> 用户信息数据
 */
export const reqUserInfo = () =>
  request.get<userResponseData>(API.USER_INFO_URL)

/**
 * 更新用户信息
 * PUT /v1/user/profile
 *
 * @param data 更新的用户信息
 * @returns Promise<userResponseData> 更新后的用户信息
 */
export const reqUpdateProfile = (data: updateProfileForm) =>
  request.put<userResponseData>(API.UPDATE_PROFILE_URL, data)

/**
 * 修改用户信息
 * POST /v1/user/info/edit (实际请求: /api/v1/user/info/edit)
 *
 * @param data 修改的用户信息（包含用户ID）
 * @returns Promise<userResponseData> 修改后的用户信息
 *
 * @example
 * ```typescript
 * const updateData = {
 *   id: 123,
 *   nickname: '新昵称',
 *   sex: 1,
 *   phone: '13800138000',
 *   email: '<EMAIL>'
 * }
 * const result = await reqEditUserInfo(updateData)
 * ```
 */
export const reqEditUserInfo = (data: editUserInfoForm) =>
  request.post<userResponseData>(API.EDIT_USER_INFO_URL, data)

/**
 * 修改密码
 * PUT /v1/user/password
 *
 * @param data 密码修改数据
 * @returns Promise<{message: string}> 操作结果
 */
export const reqChangePassword = (data: changePasswordForm) =>
  request.put<{ message: string }>(API.CHANGE_PASSWORD_URL, data)

/**
 * 上传头像
 * POST /v1/user/avatar/upload (实际请求: /api/v1/user/avatar/upload)
 *
 * @param file 头像文件
 * @param userId 用户ID
 * @returns Promise<string> 头像ID
 *
 * @example
 * ```typescript
 * const file = new File([...], 'avatar.jpg', { type: 'image/jpeg' })
 * const avatarId = await reqUploadAvatar(file, 123)
 * console.log(avatarId) // 头像ID: "1943221845562101760"
 * ```
 */
export const reqUploadAvatar = (file: File, userId: number | string) => {
  const formData = new FormData()
  formData.append('avatar', file)
  formData.append('userId', userId.toString())
  return request.post<string>(API.UPLOAD_AVATAR_URL, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取当前用户头像
 * GET /v1/user/avatar (实际请求: /api/v1/user/avatar)
 *
 * @returns Promise<Blob> 头像图片数据
 *
 * @example
 * ```typescript
 * const avatarBlob = await reqGetAvatar()
 * const avatarUrl = URL.createObjectURL(avatarBlob)
 * ```
 */
export const reqGetAvatar = () =>
  request.get<Blob>(API.GET_AVATAR_URL, {
    responseType: 'blob',
    // 静默请求，不显示成功提示
    headers: {
      'X-Silent-Request': 'true'
    }
  })
