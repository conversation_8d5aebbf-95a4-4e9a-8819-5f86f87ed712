{"name": "dolphin_admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint src", "fix": "eslint --config ./eslint.config.js src --fix", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\"", "lint:eslint": "eslint src/**/*.{ts,vue} --cache --fix", "lint:style": "stylelint src/**/*.{css,scss,vue} --cache --fix", "prepare": "husky install", "commitlint": "commitlint --config commitlint.config.cjs -e -V", "preinstall": "node ./scripts/preinstall.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kitware/vtk.js": "^34.5.0", "@types/three": "^0.178.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "fabric": "^6.7.0", "nprogress": "^0.2.0", "pinia": "^3.0.3", "three": "^0.178.0", "vue": "^3.5.13", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/eslint-parser": "^7.27.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "husky": "^8.0.0", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "sass": "^1.89.2", "sass-loader": "^16.0.5", "stylelint": "^16.21.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^7.0.0", "stylelint-scss": "^6.12.1", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.8"}}