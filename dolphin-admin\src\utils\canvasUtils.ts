/**
 * Canvas相关工具函数
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * 将DataURL转换为File对象
 * @param dataUrl Canvas导出的DataURL
 * @param fileName 文件名
 * @returns File对象
 */
export function dataURLtoFile(dataUrl: string, fileName: string): File {
  // 解析DataURL
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png'
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  return new File([u8arr], fileName, { type: mime })
}

/**
 * 将Canvas导出为带标注的图片File对象
 * @param canvas Fabric.js Canvas实例
 * @param fileName 文件名（可选）
 * @param quality 图片质量（0-1，默认1.0）
 * @returns Promise<File> 导出的File对象
 */
export function exportCanvasAsFile(
  canvas: any,
  fileName?: string,
  quality: number = 1.0
): Promise<File> {
  return new Promise((resolve, reject) => {
    try {
      if (!canvas) {
        throw new Error('Canvas实例不存在')
      }

      // 生成文件名
      const finalFileName = fileName || `annotated_image_${Date.now()}.png`

      // 导出Canvas为DataURL
      const dataUrl = canvas.toDataURL('image/png', quality)

      // 转换为File对象
      const file = dataURLtoFile(dataUrl, finalFileName)



      resolve(file)
    } catch (error) {
      console.error('❌ Canvas导出失败:', error)
      reject(error)
    }
  })
}

/**
 * 获取Canvas的预览DataURL
 * @param canvas Fabric.js Canvas实例
 * @param quality 图片质量（0-1，默认0.8）
 * @returns string DataURL字符串
 */
export function getCanvasPreviewDataURL(canvas: any, quality: number = 0.8): string {
  if (!canvas) {
    throw new Error('Canvas实例不存在')
  }

  return canvas.toDataURL('image/png', quality)
}

/**
 * 验证Canvas是否包含标注内容
 * @param canvas Fabric.js Canvas实例
 * @returns boolean 是否包含标注
 */
export function hasAnnotations(canvas: any): boolean {
  if (!canvas) return false

  const objects = canvas.getObjects()

  // 过滤掉背景图像，检查是否有其他对象（标注）
  const annotations = objects.filter((obj: any) =>
    obj.annotationType === 'point' ||
    obj.annotationType === 'region' ||
    obj.annotationType === 'rect' ||
    obj.annotationType === 'circle'
  )

  return annotations.length > 0
}

/**
 * 获取Canvas中的标注统计信息
 * @param canvas Fabric.js Canvas实例
 * @returns object 标注统计信息
 */
export function getAnnotationStats(canvas: any): {
  totalAnnotations: number
  pointCount: number
  regionCount: number
  rectCount: number
  circleCount: number
} {
  if (!canvas) {
    return {
      totalAnnotations: 0,
      pointCount: 0,
      regionCount: 0,
      rectCount: 0,
      circleCount: 0
    }
  }

  const objects = canvas.getObjects()

  const stats = {
    totalAnnotations: 0,
    pointCount: 0,
    regionCount: 0,
    rectCount: 0,
    circleCount: 0
  }

  objects.forEach((obj: any) => {
    switch (obj.annotationType) {
      case 'point':
        stats.pointCount++
        stats.totalAnnotations++
        break
      case 'region':
        stats.regionCount++
        stats.totalAnnotations++
        break
      case 'rect':
        stats.rectCount++
        stats.totalAnnotations++
        break
      case 'circle':
        stats.circleCount++
        stats.totalAnnotations++
        break
    }
  })

  return stats
}
