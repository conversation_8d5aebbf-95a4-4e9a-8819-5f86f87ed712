<template>
  <div class="tag-section">
    <div class="section-header">
      <el-button :type="buttonType" size="small" @click="showTagInput" :disabled="disabled">
        <el-icon><Plus /></el-icon>
        添加{{ title }}
      </el-button>
    </div>

    <div class="tags-container" :class="{ empty: modelValue.length === 0 }">
      <el-tag
        v-for="(tag, index) in modelValue"
        :key="`${type}-${index}`"
        :type="tagType"
        closable
        @close="removeTag(index)"
        :class="`${type}-tag`"
      >
        {{ tag }}
      </el-tag>
    </div>

    <div v-if="tagInputVisible" class="tag-input-group">
      <el-input
        ref="tagInputRef"
        v-model="tagInputValue"
        :placeholder="placeholder"
        maxlength="50"
        @keyup.enter="confirmTag"
        @keyup.esc="cancelTag"
      />
      <el-button :type="buttonType" @click="confirmTag">
        <el-icon><Check /></el-icon>
      </el-button>
      <el-button @click="cancelTag">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>

    <div v-if="presetTags && presetTags.length > 0" class="preset-tags">
      <span class="preset-label">常用{{ title }}：</span>
      <el-button
        v-for="preset in presetTags"
        :key="preset"
        size="small"
        plain
        @click="addPresetTag(preset)"
        :disabled="disabled"
      >
        {{ preset }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Check, Close } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'TagManager',
})

// Props 定义
interface Props {
  modelValue: string[]
  type: 'features' | 'thinking' | 'basis'
  title: string
  placeholder: string
  presetTags?: string[]
  tagType?: 'success' | 'primary' | 'warning' | 'danger' | 'info'
  buttonType?: 'success' | 'primary' | 'warning' | 'danger' | 'info'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  presetTags: () => [],
  tagType: 'primary',
  buttonType: 'primary',
  disabled: false,
})

// Emits 定义
interface Emits {
  'update:modelValue': [tags: string[]]
}

const emit = defineEmits<Emits>()

// 响应式数据
const tagInputRef = ref()
const tagInputVisible = ref(false)
const tagInputValue = ref('')

// 显示标签输入框
const showTagInput = async () => {
  tagInputVisible.value = true
  await nextTick()
  tagInputRef.value?.focus()
}

// 确认添加标签
const confirmTag = () => {
  const value = tagInputValue.value.trim()
  if (!value) return

  // 检查重复
  if (props.modelValue.includes(value)) {
    ElMessage.warning('该词条已存在')
    return
  }

  const newTags = [...props.modelValue, value]
  emit('update:modelValue', newTags)

  tagInputValue.value = ''
  tagInputVisible.value = false
}

// 取消添加标签
const cancelTag = () => {
  tagInputValue.value = ''
  tagInputVisible.value = false
}

// 删除标签
const removeTag = (index: number) => {
  const newTags = [...props.modelValue]
  newTags.splice(index, 1)
  emit('update:modelValue', newTags)
}

// 添加预设标签
const addPresetTag = (tag: string) => {
  if (!props.modelValue.includes(tag)) {
    const newTags = [...props.modelValue, tag]
    emit('update:modelValue', newTags)
  } else {
    ElMessage.warning('该词条已存在')
  }
}
</script>

<style scoped>
.tag-section {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.tags-container {
  min-height: 50px;
  padding: 14px;
  background: #fafbfc;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  transition: border-color 0.2s ease;
}

.tags-container:hover {
  border-color: #c0c4cc;
}

.tags-container.empty {
  background: #fafbfc;
  border-style: dashed;
  border-color: #d3d4d6;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-style: italic;
  font-size: 14px;
}

.tags-container.empty::before {
  content: '暂无标签，点击上方按钮添加';
}

.features-tag {
  background: #67c23a;
  border: 1px solid #67c23a;
  color: white;
}

.thinking-tag {
  background: #409eff;
  border: 1px solid #409eff;
  color: white;
}

.basis-tag {
  background: #e6a23c;
  border: 1px solid #e6a23c;
  color: white;
}

.tag-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 16px;
}

.tag-input-group .el-input {
  flex: 1;
}

.preset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  padding: 14px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.preset-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-right: 12px;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .preset-tags {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
  }

  .preset-label {
    margin-right: 0;
    margin-bottom: 6px;
  }

  .tag-input-group {
    flex-direction: column;
    gap: 10px;
  }

  .tag-input-group .el-input {
    width: 100%;
  }

  .tags-container {
    min-height: 45px;
    padding: 12px;
  }
}

/* 按钮样式优化 */
:deep(.el-button--small) {
  height: 32px;
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 4px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  margin: 2px;
  border-radius: 4px;
  font-size: 13px;
  height: 28px;
  line-height: 26px;
  padding: 0 10px;
  font-weight: 500;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
}
</style>
