/**
 * API配置文件
 * 
 * 用于配置各种API相关的开关和设置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * API功能开关配置
 */
export const API_CONFIG = {
  /**
   * 登出接口是否可用
   * 当后端登出接口实现后，将此值改为 true
   */
  LOGOUT_API_ENABLED: false,
  
  /**
   * 是否启用token自动刷新
   * 当后端刷新token接口稳定后，可以启用此功能
   */
  AUTO_REFRESH_TOKEN: false,
  
  /**
   * 是否启用接口调用日志
   * 开发环境建议开启，生产环境建议关闭
   */
  ENABLE_API_LOGS: import.meta.env.NODE_ENV === 'development',
  
  /**
   * 请求超时时间（毫秒）
   */
  REQUEST_TIMEOUT: 5000,
  
  /**
   * Token过期前多少秒开始刷新（仅在AUTO_REFRESH_TOKEN为true时生效）
   */
  TOKEN_REFRESH_BUFFER: 60,
}

/**
 * API状态码配置
 */
export const API_STATUS_CODES = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  
  // 业务状态码
  TOKEN_EXPIRED: 4001,
  TOKEN_INVALID: 4002,
  PERMISSION_DENIED: 3001,
}

/**
 * 错误消息配置
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接异常，请检查网络连接和后端服务状态',
  UNAUTHORIZED: '未授权，请重新登录',
  FORBIDDEN: '拒绝访问',
  NOT_FOUND: '请求地址不存在',
  SERVER_ERROR: '服务器内部错误',
  TIMEOUT: '请求超时，请稍后重试',
  
  // 业务错误消息
  TOKEN_EXPIRED: '登录状态已过期，请重新登录',
  TOKEN_INVALID: '登录状态无效，请重新登录',
  PERMISSION_DENIED: '无权限访问',
}
