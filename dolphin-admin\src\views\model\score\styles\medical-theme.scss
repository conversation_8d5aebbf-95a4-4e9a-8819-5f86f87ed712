/* 医疗专业配色变量 */
:root {
  --medical-blue: #2E7CE6;
  --medical-green: #00D4AA;
  --medical-orange: #FF8C00;
  --medical-red: #FF4757;
  --medical-bg: #F8FAFC;
  --medical-card: #FFFFFF;
  --medical-text-dark: #2C3E50;
  --medical-text-light: #7F8C8D;
}

/* 通用卡片样式 */
.medical-card {
  background: var(--medical-card);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.medical-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 医疗主题按钮 */
.medical-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.medical-btn:hover {
  transform: translateY(-2px);
}

.medical-btn-primary {
  background: var(--medical-blue);
  border-color: var(--medical-blue);
}

.medical-btn-success {
  background: var(--medical-green);
  border-color: var(--medical-green);
}

.medical-btn-warning {
  background: var(--medical-orange);
  border-color: var(--medical-orange);
}

.medical-btn-danger {
  background: var(--medical-red);
  border-color: var(--medical-red);
}

/* 医疗主题图标 */
.medical-icon {
  color: var(--medical-blue);
  transition: color 0.3s ease;
}

.medical-icon-success {
  color: var(--medical-green);
}

.medical-icon-warning {
  color: var(--medical-orange);
}

.medical-icon-danger {
  color: var(--medical-red);
}

/* 医疗主题文本 */
.medical-text-primary {
  color: var(--medical-text-dark);
}

.medical-text-secondary {
  color: var(--medical-text-light);
}

/* 医疗主题边框 */
.medical-border-left {
  border-left: 4px solid var(--medical-blue);
}

.medical-border-left-success {
  border-left: 4px solid var(--medical-green);
}

.medical-border-left-warning {
  border-left: 4px solid var(--medical-orange);
}

.medical-border-left-danger {
  border-left: 4px solid var(--medical-red);
}

/* 医疗主题背景 */
.medical-bg-light {
  background: rgba(46, 124, 230, 0.04);
}

.medical-bg-success-light {
  background: rgba(0, 212, 170, 0.04);
}

.medical-bg-warning-light {
  background: rgba(255, 140, 0, 0.04);
}

.medical-bg-danger-light {
  background: rgba(255, 71, 87, 0.04);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--medical-text-dark);
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background: var(--medical-blue);
  border-color: var(--medical-blue);
}

:deep(.el-button--success) {
  background: var(--medical-green);
  border-color: var(--medical-green);
}

:deep(.el-tag--success) {
  background: rgba(0, 212, 170, 0.1);
  // border-color: var(--medical-green);
  color: var(--medical-green);
}

:deep(.el-tag--danger) {
  background: rgba(255, 71, 87, 0.1);
  // border-color: var(--medical-red);
  color: var(--medical-red);
}

:deep(.el-tag--warning) {
  background: rgba(255, 140, 0, 0.1);
  // border-color: var(--medical-orange);
  color: var(--medical-orange);
}

:deep(.el-tag--info) {
  background: rgba(46, 124, 230, 0.1);
  // border-color: var(--medical-blue);
  color: var(--medical-blue);
}
