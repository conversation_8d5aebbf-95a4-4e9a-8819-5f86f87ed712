/**
 * 数据管理相关类型定义
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// ==================== 诊断数据相关类型定义 ====================

/**
 * 诊断数据请求参数 (DiagnoseDto)
 */
export interface DiagnoseDto {
  /** 诊断联想 */
  associate: string[]
  /** 诊断依据 */
  basis: string[]
  /** 特征分析 */
  caption: string[]
  /** 诊断分类 */
  classification: string
  /** 详细诊断 */
  detail: string
  /** 病灶信息 (来自用户输入) */
  focus?: string
  /** 标注位置 (来自标注) */
  location?: number[]
  /** 检查部位 */
  part?: string
  /** 切面类型 */
  section?: string
  /** 图像数据 (上传后的单张图像ID) */
  image?: string
  /** 图像文件 */
  imageFile?: File
  /** 图像URL */
  imageUrl?: string
  /** 图像ID (已废弃，请使用images) */
  imageId?: string
  [property: string]: any
}

// ==================== 数据列表相关类型定义 ====================

/**
 * 数据列表项 (DataItem)
 */
export interface DataItem {
  /** 记录ID */
  id: string
  /** 图像数据 (单个图像ID) */
  image?: string
  /** 图像数据 (图像ID数组，用于向后兼容) */
  images?: string[] | null
  /** 特征分析 */
  caption: string[]
  /** 诊断联想 */
  associate: string[]
  /** 诊断依据 */
  basis: string[]
  /** 诊断分类 */
  classification: string
  /** 详细诊断 */
  detail: string
  /** 检查部位 */
  part?: string
  /** 切面类型 */
  section?: string
  /** 病灶信息 */
  focus?: string
  /** 位置 */
  location?: string
  /** 备注 */
  remark?: string
  /** 报告 */
  report?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/**
 * 分页查询参数
 */
export interface PageQuery {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 诊断分类筛选 */
  classification?: string
  /** 关键词搜索 */
  keyword?: string
}

/**
 * 搜索查询参数
 */
export interface SearchQuery {
  /** 页码 */
  pageNumber: number
  /** 每页大小 */
  pageSize: number
  /** 诊断分类筛选 */
  classification?: string
  /** 诊断记录ID */
  diagnoseId?: string
}

/**
 * 导出数据参数
 */
export interface ExportDataParams {
  /** 数据集名称 */
  datasetName: string
  /** 数据集描述 */
  datasetDescription: string
  /** 起始时间 (时间戳) */
  startTime: number
  /** 终止时间 (时间戳) */
  endTime: number
  /** 备注 */
  remark: string
}

/**
 * 分页响应数据
 */
export interface PageResponse<T> {
  /** 数据列表 */
  records: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 总页数 */
  pages: number
}
