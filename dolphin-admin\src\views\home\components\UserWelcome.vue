<template>
  <div class="user-welcome-container">
    <!-- 用户信息区域 -->
    <div class="user-info-section">
      <div class="user-avatar">
        <UserAvatar :avatar="userStore.avatar" :size="64" :clickable="false" class="welcome-avatar" />
      </div>
      <div class="welcome-content">
        <div class="greeting">
          <span class="greeting-text">{{ greeting }}，{{ username }}，祝你开心每一天！</span>
        </div>
        <div class="user-tags">
          <el-tag size="small" type="info">数据管理员</el-tag>
          <el-tag size="small" type="success">医学影像专家</el-tag>
          <el-tag size="small" type="warning">质量评估师</el-tag>
          <el-tag size="small" type="primary">UFO</el-tag>
        </div>
      </div>
    </div>

    <!-- 统计信息区域 -->
    <div class="stats-info-section">
      <div class="stat-item">
        <div class="stat-label">项目数</div>
        <div class="stat-value">56</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">团队排名</div>
        <div class="stat-value">8/24</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">项目访问</div>
        <div class="stat-value">2,223</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import useUserStore from '@/store/modules/user'

// 获取用户信息
const userStore = useUserStore()

// 计算属性
const username = computed(() => userStore.username || 'Serati Ma')

// 根据时间动态生成问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) {
    return '凌晨好'
  } else if (hour < 9) {
    return '早安'
  } else if (hour < 12) {
    return '上午好'
  } else if (hour < 14) {
    return '中午好'
  } else if (hour < 18) {
    return '下午好'
  } else if (hour < 22) {
    return '晚上好'
  } else {
    return '夜深了'
  }
}

const greeting = computed(() => getGreeting())
</script>

<style scoped lang="scss">
.user-welcome-container {
  background: linear-gradient(135deg, #5a9cf8 0%, #79bbff 100%);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(90, 156, 248, 0.25);
}

.user-welcome-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.15)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.8" fill="rgba(255,255,255,0.12)"/><circle cx="10" cy="60" r="1.2" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.user-info-section {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1;
  position: relative;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar :deep(.user-avatar-el) {
  border-radius: 50% !important;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.greeting {
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.greeting-text {
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.user-tags :deep(.el-tag) {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.stats-info-section {
  display: flex;
  gap: 32px;
  z-index: 1;
  position: relative;
}

.stat-item {
  text-align: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(90, 156, 248, 0.25);
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

// 响应式设计
@media (max-width: 768px) {
  .user-welcome-container {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    text-align: center;
  }

  .user-info-section {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-info-section {
    gap: 16px;
    justify-content: center;
  }

  .stat-item {
    padding: 8px 12px;
  }

  .greeting {
    font-size: 18px;
  }

  .stat-value {
    font-size: 16px;
  }
}
</style>
