<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Loading, Warning } from '@element-plus/icons-vue'
import { Canvas, FabricImage, Rect, Circle, Polygon } from 'fabric'
import useLayoutSettingStore from '@/store/modules/setting'

// 定义组件名称
defineOptions({
  name: 'MedicalImageCanvas',
})

// 定义 Props
const props = defineProps({
  // 图像相关
  imageUrl: {
    type: String,
    default: '',
  },
  imageData: {
    type: Object,
    default: () => ({}),
  },

  // 画布配置
  canvasWidth: {
    type: Number,
    default: 720,
  },
  canvasHeight: {
    type: Number,
    default: 720,
  },

  // 交互模式
  mode: {
    type: String,
    default: 'draw-point',
    validator: (value) => ['select', 'draw-rect', 'draw-circle', 'draw-point'].includes(value),
  },

  // 标注数据
  annotations: {
    type: Array,
    default: () => [],
  },

  // 样式配置
  annotationStyle: {
    type: Object,
    default: () => ({
      stroke: '#ff0000',
      strokeWidth: 2,
      fill: 'transparent',
    }),
  },

  // 功能开关
  readonly: {
    type: Boolean,
    default: false,
  },
})

// 定义 Events
const emit = defineEmits([
  'canvas-ready',
  'image-loaded',
  'annotation-created',
  'annotation-updated',
  'annotation-deleted',
  'annotation-selected',
  'mode-changed',
  'canvas-clicked',
  'error',
])

// 响应式数据
const canvasRef = ref(null)
const fabricCanvas = ref(null)
const backgroundImage = ref(null)
const imageScale = ref(1)
const imageOffset = ref({ x: 0, y: 0 })
const canvasReady = ref(false)
const imageLoaded = ref(false)
const error = ref('')
const currentMode = ref('select')
const selectedAnnotation = ref(null)

// 暗色模式状态管理
const settingStore = useLayoutSettingStore()

// 交互模式常量
const INTERACTION_MODES = {
  SELECT: 'select',
  DRAW_RECT: 'draw-rect',
  DRAW_CIRCLE: 'draw-circle',
  DRAW_POINT: 'draw-point',
}

// 绘制状态
const drawingState = ref({
  isDrawing: false,
  startPoint: null,
  drawingObject: null,
})

// 获取当前主题对应的背景色
const getCanvasBackgroundColor = () => {
  return settingStore.darkMode ? '#1a1a1a' : '#ffffff'
}

// 1. Fabric.js Canvas 初始化
const initFabricCanvas = () => {
  try {
    const canvas = new Canvas(canvasRef.value, {
      width: props.canvasWidth,
      height: props.canvasHeight,
      backgroundColor: getCanvasBackgroundColor(),

      // 选择相关配置
      selection: true,
      selectionColor: 'rgba(100, 100, 255, 0.3)',
      selectionLineWidth: 2,

      // 控制点样式
      controlsAboveOverlay: true,
      centeredScaling: false,
      centeredRotation: true,

      // 性能优化
      renderOnAddRemove: true,
      skipTargetFind: false,

      // 交互配置
      allowTouchScrolling: false,
      stopContextMenu: true,
    })

    // 设置控制点样式
    canvas.set({
      transparentCorners: false,
      cornerColor: '#2196F3',
      cornerStyle: 'circle',
      cornerSize: 8,
      borderColor: '#2196F3',
      borderScaleFactor: 2,
    })

    // 绑定基础事件
    canvas.on('selection:created', handleSelectionCreated)
    canvas.on('selection:updated', handleSelectionUpdated)
    canvas.on('selection:cleared', handleSelectionCleared)
    canvas.on('object:modified', handleObjectModified)
    canvas.on('mouse:down', handleCanvasMouseDown)

    fabricCanvas.value = canvas
    canvasReady.value = true

    emit('canvas-ready', canvas)

    // 设置初始交互模式
    setInteractionMode(props.mode)

    // 如果有初始图像，加载它
    if (props.imageUrl) {
      loadMedicalImage(props.imageUrl)
    }
  } catch (err) {
    error.value = '画布初始化失败: ' + err.message
    emit('error', err)
  }
}

// 2. 图像加载功能
const loadMedicalImage = async (imageUrl) => {
  if (!fabricCanvas.value || !imageUrl) return

  try {
    // 清除之前的背景图像
    if (backgroundImage.value) {
      fabricCanvas.value.remove(backgroundImage.value)
      backgroundImage.value = null
    }

    // 使用 FabricImage.fromURL (v6 返回 Promise)
    const fabricImage = await FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' })

    // 计算适配容器的缩放比例
    const containerSize = Math.min(props.canvasWidth, props.canvasHeight)
    const scaleX = containerSize / fabricImage.width
    const scaleY = containerSize / fabricImage.height
    const scale = Math.min(scaleX, scaleY) // 保持比例

    // 设置图像属性
    fabricImage.set({
      scaleX: scale,
      scaleY: scale,
      selectable: false, // 背景图不可选择
      evented: false, // 背景图不响应事件
      originX: 'center',
      originY: 'center',
    })

    // 添加到画布并居中
    fabricCanvas.value.add(fabricImage)
    fabricCanvas.value.centerObject(fabricImage)
    fabricCanvas.value.sendObjectToBack(fabricImage)

    // 保存引用和元数据
    backgroundImage.value = fabricImage
    imageScale.value = scale
    imageOffset.value = {
      x: fabricImage.left,
      y: fabricImage.top,
    }
    imageLoaded.value = true

    emit('image-loaded', {
      originalWidth: fabricImage.width,
      originalHeight: fabricImage.height,
      scale: scale,
      offset: imageOffset.value,
    })

    // 图像加载完成后，确保交互模式设置正确
    setInteractionMode(props.mode)
  } catch (err) {
    error.value = '图像加载失败: ' + err.message
    emit('error', err)
  }
}

// 3. 交互模式管理
const setInteractionMode = (mode) => {
  if (!fabricCanvas.value) return

  // 清理当前模式状态
  cleanupCurrentMode()

  // 设置新模式
  currentMode.value = mode

  // 配置 Canvas 交互
  switch (mode) {
    case INTERACTION_MODES.SELECT:
      fabricCanvas.value.selection = true
      fabricCanvas.value.defaultCursor = 'default'
      fabricCanvas.value.hoverCursor = 'move'
      break

    case INTERACTION_MODES.DRAW_RECT:
    case INTERACTION_MODES.DRAW_CIRCLE:
      fabricCanvas.value.selection = false
      fabricCanvas.value.defaultCursor = 'crosshair'
      setupDrawingMode(mode)
      break

    case INTERACTION_MODES.DRAW_POINT:
      fabricCanvas.value.selection = false
      fabricCanvas.value.defaultCursor = 'crosshair'
      setupPointMode()
      break
  }

  emit('mode-changed', mode)
}

// 清理当前模式状态
const cleanupCurrentMode = () => {
  if (!fabricCanvas.value) return

  // 清理绘制状态
  if (drawingState.value.isDrawing && drawingState.value.drawingObject) {
    fabricCanvas.value.remove(drawingState.value.drawingObject)
  }

  drawingState.value = {
    isDrawing: false,
    startPoint: null,
    drawingObject: null,
  }

  // 移除绘制模式的事件监听（只有在绘制模式下才需要移除）
  try {
    fabricCanvas.value.off('mouse:move', onDrawingMouseMove)
    fabricCanvas.value.off('mouse:up', onDrawingMouseUp)
  } catch (error) {
    // 忽略移除事件监听时的错误
  }
}

// 设置绘制模式
const setupDrawingMode = (drawMode) => {
  fabricCanvas.value.on('mouse:move', onDrawingMouseMove)
  fabricCanvas.value.on('mouse:up', onDrawingMouseUp)
}

// 设置点标注模式
const setupPointMode = () => {
  // 点标注模式不需要额外的鼠标移动和松开事件
  // 只需要在 handleCanvasMouseDown 中处理点击事件
}

// 创建点标注
const createPointAnnotation = (event) => {
  if (!fabricCanvas.value) return

  const pointer = fabricCanvas.value.getPointer(event.e)

  // 创建点标注（小圆点）
  const pointAnnotation = new Circle({
    left: pointer.x,
    top: pointer.y,
    radius: 4, // 小圆点半径
    fill: '#ff4444', // 红色填充
    stroke: '#ffffff', // 白色边框
    strokeWidth: 2,
    selectable: !props.readonly,
    evented: !props.readonly,
    originX: 'center',
    originY: 'center',
  })

  // 生成唯一ID
  pointAnnotation.id = 'point_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
  pointAnnotation.annotationType = 'point'

  // 添加到画布
  fabricCanvas.value.add(pointAnnotation)
  fabricCanvas.value.renderAll()

  // 发送标注创建事件
  emit('annotation-created', convertToAnnotationData(pointAnnotation))
}

// 创建标注区域（连接点形成透明区域）
const createAnnotationRegion = (points, color = '#3b82f6') => {
  if (!fabricCanvas.value || points.length < 3) {
    return
  }

  // 创建多边形路径
  const polygonPoints = points.map((point) => ({
    x: point.x,
    y: point.y,
  }))

  // 将颜色转换为rgba格式
  const hexToRgba = (hex, alpha = 0.2) => {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  // 创建Fabric.js多边形对象
  const polygon = new Polygon(polygonPoints, {
    fill: hexToRgba(color, 0.2), // 半透明填充
    stroke: color, // 边框颜色
    strokeWidth: 2,
    selectable: false, // 区域不可选择，避免干扰点的操作
    evented: false, // 不响应事件
    opacity: 0.8,
  })

  // 生成唯一ID
  polygon.id = 'region_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
  polygon.annotationType = 'region'

  // 添加到画布，放在点的下方
  fabricCanvas.value.add(polygon)
  fabricCanvas.value.sendObjectToBack(polygon)

  // 确保背景图像在最底层
  if (backgroundImage.value) {
    fabricCanvas.value.sendObjectToBack(backgroundImage.value)
  }

  fabricCanvas.value.renderAll()
}

// 删除指定颜色的闭合区域和相关标注点
const deleteAnnotationRegion = (color, lesionPoints = []) => {
  if (!fabricCanvas.value) {
    return false
  }

  // 获取所有对象
  const objects = fabricCanvas.value.getObjects()

  // 查找匹配颜色的区域对象
  const regionsToDelete = objects.filter((obj) => {
    return obj.annotationType === 'region' && obj.stroke === color
  })

  // 查找需要删除的标注点
  const pointsToDelete = []
  if (lesionPoints && lesionPoints.length > 0) {
    // 根据坐标匹配标注点
    lesionPoints.forEach((lesionPoint) => {
      const matchingPoint = objects.find((obj) => {
        if (obj.annotationType === 'point') {
          // 允许一定的坐标误差（±5像素）
          const tolerance = 5
          return Math.abs(obj.left - lesionPoint.x) <= tolerance && Math.abs(obj.top - lesionPoint.y) <= tolerance
        }
        return false
      })

      if (matchingPoint) {
        pointsToDelete.push(matchingPoint)
      }
    })
  }

  const totalToDelete = regionsToDelete.length + pointsToDelete.length

  if (totalToDelete === 0) {
    return false
  }

  // 删除找到的区域
  regionsToDelete.forEach((region) => {
    fabricCanvas.value.remove(region)
  })

  // 删除找到的标注点
  pointsToDelete.forEach((point) => {
    fabricCanvas.value.remove(point)
  })

  fabricCanvas.value.renderAll()

  return true
}

// 导出Canvas为图像
const exportCanvasAsImage = (format = 'image/png', quality = 1.0) => {
  if (!fabricCanvas.value) {
    return null
  }

  try {
    const dataURL = fabricCanvas.value.toDataURL({
      format: format,
      quality: quality,
      multiplier: 1, // 保持原始分辨率
    })

    return dataURL
  } catch (error) {
    return null
  }
}

// 绘制模式鼠标移动处理
const onDrawingMouseMove = (event) => {
  if (!drawingState.value.isDrawing || !drawingState.value.drawingObject) return

  const pointer = fabricCanvas.value.getPointer(event.e)
  updateDrawingObject(drawingState.value.drawingObject, drawingState.value.startPoint, pointer, currentMode.value)
  fabricCanvas.value.renderAll()
}

// 绘制模式鼠标松开处理
const onDrawingMouseUp = () => {
  if (!drawingState.value.isDrawing) return

  drawingState.value.isDrawing = false

  // 检查绘制对象是否有效
  if (isValidDrawingObject(drawingState.value.drawingObject)) {
    finalizeDrawingObject(drawingState.value.drawingObject)
    emit('annotation-created', convertToAnnotationData(drawingState.value.drawingObject))
  } else {
    fabricCanvas.value.remove(drawingState.value.drawingObject)
  }

  drawingState.value.drawingObject = null
  drawingState.value.startPoint = null
}

// 画布鼠标按下处理
const handleCanvasMouseDown = (event) => {
  // 如果是选择模式，直接返回让 Fabric 处理
  if (currentMode.value === INTERACTION_MODES.SELECT) {
    emit('canvas-clicked', event)
    return
  }

  // 如果点击在已有对象上，不开始绘制
  if (event.target && event.target !== backgroundImage.value) {
    return
  }

  // 处理点标注
  if (currentMode.value === INTERACTION_MODES.DRAW_POINT) {
    createPointAnnotation(event)
    return
  }

  // 开始绘制
  if (currentMode.value === INTERACTION_MODES.DRAW_RECT || currentMode.value === INTERACTION_MODES.DRAW_CIRCLE) {
    startDrawing(event)
  }
}

// 开始绘制
const startDrawing = (event) => {
  drawingState.value.isDrawing = true
  drawingState.value.startPoint = fabricCanvas.value.getPointer(event.e)

  // 创建绘制对象
  drawingState.value.drawingObject = createDrawingObject(currentMode.value, drawingState.value.startPoint)
  fabricCanvas.value.add(drawingState.value.drawingObject)
}

// 创建绘制对象
const createDrawingObject = (mode, startPoint) => {
  const commonProps = {
    left: startPoint.x,
    top: startPoint.y,
    fill: props.annotationStyle.fill,
    stroke: props.annotationStyle.stroke,
    strokeWidth: props.annotationStyle.strokeWidth,
    selectable: false, // 绘制时不可选择
  }

  switch (mode) {
    case INTERACTION_MODES.DRAW_RECT:
      return new Rect({
        ...commonProps,
        width: 0,
        height: 0,
      })

    case INTERACTION_MODES.DRAW_CIRCLE:
      return new Circle({
        ...commonProps,
        radius: 0,
      })

    default:
      return null
  }
}

// 更新绘制对象
const updateDrawingObject = (obj, startPoint, currentPoint, mode) => {
  switch (mode) {
    case INTERACTION_MODES.DRAW_RECT:
      const width = Math.abs(currentPoint.x - startPoint.x)
      const height = Math.abs(currentPoint.y - startPoint.y)
      obj.set({
        width: width,
        height: height,
        left: Math.min(startPoint.x, currentPoint.x),
        top: Math.min(startPoint.y, currentPoint.y),
      })
      break

    case INTERACTION_MODES.DRAW_CIRCLE:
      const radius =
        Math.sqrt(Math.pow(currentPoint.x - startPoint.x, 2) + Math.pow(currentPoint.y - startPoint.y, 2)) / 2
      obj.set({
        radius: Math.max(0, radius),
        left: startPoint.x - radius,
        top: startPoint.y - radius,
      })
      break
  }
}

// 检查绘制对象是否有效
const isValidDrawingObject = (obj) => {
  if (!obj) return false

  if (obj.type === 'rect') {
    return obj.width > 5 && obj.height > 5
  } else if (obj.type === 'circle') {
    return obj.radius > 3
  }

  return false
}

// 完成绘制对象
const finalizeDrawingObject = (obj) => {
  obj.set({
    selectable: !props.readonly,
    evented: !props.readonly,
  })

  // 生成唯一ID
  obj.id = 'annotation_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// 选择事件处理
const handleSelectionCreated = (event) => {
  const selectedObject = event.selected[0]
  if (selectedObject && selectedObject !== backgroundImage.value) {
    selectedAnnotation.value = selectedObject
    emit('annotation-selected', convertToAnnotationData(selectedObject))
  }
}

const handleSelectionUpdated = (event) => {
  const selectedObject = event.selected[0]
  if (selectedObject && selectedObject !== backgroundImage.value) {
    selectedAnnotation.value = selectedObject
    emit('annotation-selected', convertToAnnotationData(selectedObject))
  }
}

const handleSelectionCleared = () => {
  selectedAnnotation.value = null
}

const handleObjectModified = (event) => {
  const modifiedObject = event.target
  if (modifiedObject && modifiedObject !== backgroundImage.value) {
    emit('annotation-updated', convertToAnnotationData(modifiedObject))
  }
}

// 转换为标注数据格式
const convertToAnnotationData = (fabricObject) => {
  if (!fabricObject || fabricObject === backgroundImage.value) return null

  const baseData = {
    id: fabricObject.id,
    type: fabricObject.type,
  }

  if (fabricObject.type === 'rect') {
    return {
      ...baseData,
      coordinates: {
        x: fabricObject.left,
        y: fabricObject.top,
        width: fabricObject.width * fabricObject.scaleX,
        height: fabricObject.height * fabricObject.scaleY,
      },
    }
  } else if (fabricObject.type === 'circle') {
    // 区分点标注和圆形标注
    if (fabricObject.annotationType === 'point') {
      return {
        ...baseData,
        annotationType: 'point',
        coordinates: {
          x: fabricObject.left,
          y: fabricObject.top,
        },
      }
    } else {
      // 普通圆形标注
      return {
        ...baseData,
        coordinates: {
          x: fabricObject.left + fabricObject.radius,
          y: fabricObject.top + fabricObject.radius,
          radius: fabricObject.radius * fabricObject.scaleX,
        },
      }
    }
  }

  return baseData
}

// 监听暗色模式变化，更新画布背景色
watch(
  () => settingStore.darkMode,
  () => {
    if (fabricCanvas.value) {
      fabricCanvas.value.setBackgroundColor(
        getCanvasBackgroundColor(),
        fabricCanvas.value.renderAll.bind(fabricCanvas.value),
      )
    }
  },
)

// 生命周期和监听器
onMounted(async () => {
  await nextTick()
  initFabricCanvas()
})

onUnmounted(() => {
  if (fabricCanvas.value) {
    fabricCanvas.value.dispose()
    fabricCanvas.value = null
  }
})

// 监听 props 变化
watch(
  () => props.imageUrl,
  (newUrl) => {
    if (newUrl && fabricCanvas.value) {
      loadMedicalImage(newUrl)
    }
  },
  { immediate: true },
)

watch(
  () => props.mode,
  (newMode) => {
    if (fabricCanvas.value) {
      setInteractionMode(newMode)
    }
  },
  { immediate: false },
)

// 暴露给父组件的方法
defineExpose({
  // Canvas 实例
  getCanvas: () => fabricCanvas.value,

  // 图像相关
  loadImage: loadMedicalImage,
  getImageInfo: () => ({
    scale: imageScale.value,
    offset: imageOffset.value,
    loaded: imageLoaded.value,
  }),

  // 交互模式
  setMode: setInteractionMode,
  getCurrentMode: () => currentMode.value,

  // 标注管理
  getSelectedAnnotation: () => selectedAnnotation.value,
  clearSelection: () => {
    if (fabricCanvas.value) {
      fabricCanvas.value.discardActiveObject()
      fabricCanvas.value.renderAll()
    }
  },

  // 画布操作
  clearCanvas: () => {
    if (fabricCanvas.value) {
      const objects = fabricCanvas.value.getObjects().filter((obj) => obj !== backgroundImage.value)
      objects.forEach((obj) => fabricCanvas.value.remove(obj))
      fabricCanvas.value.renderAll()
    }
  },

  // 清除所有标注（保留背景图片）
  clearAllAnnotations: () => {
    if (fabricCanvas.value) {
      const objects = fabricCanvas.value.getObjects()
      // 过滤出需要删除的标注对象（点标注和区域标注）
      const annotationsToDelete = objects.filter((obj) => {
        return (
          obj !== backgroundImage.value &&
          (obj.annotationType === 'point' ||
            obj.annotationType === 'region' ||
            obj.type === 'rect' ||
            obj.type === 'circle')
        )
      })

      // 删除所有标注对象
      annotationsToDelete.forEach((obj) => fabricCanvas.value.remove(obj))
      fabricCanvas.value.renderAll()
    }
  },

  // 标注区域
  createAnnotationRegion: createAnnotationRegion,
  deleteAnnotationRegion: deleteAnnotationRegion,

  // Canvas导出
  exportCanvasAsImage: exportCanvasAsImage,
})
</script>
<template>
  <div class="medical-image-canvas-container">
    <!-- 加载状态 -->
    <div v-if="!canvasReady" class="canvas-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>初始化画布...</span>
    </div>

    <!-- Fabric Canvas -->
    <canvas ref="canvasRef" class="medical-canvas" :class="{ 'canvas-ready': canvasReady }" />

    <!-- 错误状态 -->
    <div v-if="error" class="canvas-error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
    </div>
  </div>
</template>



<style scoped>
.medical-image-canvas-container {
  position: relative;
  width: 720px;
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  border: 2px solid var(--border-primary);
  overflow: hidden;
}

.canvas-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.canvas-loading .el-icon {
  font-size: 1.5rem;
  color: var(--medical-blue);
}

.medical-canvas {
  display: block;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.medical-canvas.canvas-ready {
  opacity: 1;
}

.canvas-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--medical-red);
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
}

.canvas-error .el-icon {
  font-size: 1.5rem;
}
</style>
