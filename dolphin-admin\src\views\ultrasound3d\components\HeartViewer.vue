<template>
  <div class="heart-viewer-container">
    <!-- VTK.js 渲染容器 -->
    <div ref="vtkContainer" class="vtk-container"></div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <label>视图模式:</label>
        <el-radio-group v-model="viewMode" @change="changeViewMode">
          <el-radio-button value="surface">表面渲染</el-radio-button>
          <el-radio-button value="wireframe">线框模式</el-radio-button>
          <el-radio-button value="points">点云模式</el-radio-button>
        </el-radio-group>
      </div>

      <div class="control-group">
        <label>透明度:</label>
        <el-slider v-model="opacity" :min="0" :max="1" :step="0.1" @change="updateOpacity" style="width: 150px" />
      </div>

      <div class="control-group">
        <label>颜色:</label>
        <el-color-picker v-model="heartColor" @change="updateColor" />
      </div>

      <div class="control-group">
        <label>解剖结构显示:</label>
        <div class="anatomy-controls">
          <el-checkbox v-model="anatomyVisibility.leftVentricle" @change="toggleAnatomyVisibility('leftVentricle')">
            左心室
          </el-checkbox>
          <el-checkbox v-model="anatomyVisibility.rightVentricle" @change="toggleAnatomyVisibility('rightVentricle')">
            右心室
          </el-checkbox>
          <el-checkbox v-model="anatomyVisibility.leftAtrium" @change="toggleAnatomyVisibility('leftAtrium')">
            左心房
          </el-checkbox>
          <el-checkbox v-model="anatomyVisibility.rightAtrium" @change="toggleAnatomyVisibility('rightAtrium')">
            右心房
          </el-checkbox>
          <el-checkbox v-model="anatomyVisibility.aorta" @change="toggleAnatomyVisibility('aorta')">主动脉</el-checkbox>
          <el-checkbox v-model="anatomyVisibility.pulmonaryArtery" @change="toggleAnatomyVisibility('pulmonaryArtery')">
            肺动脉
          </el-checkbox>
          <el-checkbox
            v-model="anatomyVisibility.superiorVenaCava"
            @change="toggleAnatomyVisibility('superiorVenaCava')"
          >
            上腔静脉
          </el-checkbox>
          <el-checkbox
            v-model="anatomyVisibility.inferiorVenaCava"
            @change="toggleAnatomyVisibility('inferiorVenaCava')"
          >
            下腔静脉
          </el-checkbox>
        </div>
      </div>

      <div class="control-group">
        <el-button @click="resetCamera" size="small">
          <el-icon><Refresh /></el-icon>
          重置视图
        </el-button>
        <el-button @click="toggleAnimation" size="small">
          <el-icon><VideoPlay /></el-icon>
          {{ isAnimating ? '停止' : '播放' }}
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
      </div>
      <p>正在加载3D心脏模型...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, VideoPlay, Loading } from '@element-plus/icons-vue'

// VTK.js 导入
import '@kitware/vtk.js/Rendering/Profiles/Geometry'
import vtkFullScreenRenderWindow from '@kitware/vtk.js/Rendering/Misc/FullScreenRenderWindow'
import vtkActor from '@kitware/vtk.js/Rendering/Core/Actor'
import vtkMapper from '@kitware/vtk.js/Rendering/Core/Mapper'
import vtkSphereSource from '@kitware/vtk.js/Filters/Sources/SphereSource'
import vtkCylinderSource from '@kitware/vtk.js/Filters/Sources/CylinderSource'

// 导入真实医学心脏解剖几何体
import {
  createLeftVentricleGeometry,
  createRightVentricleGeometry,
  createLeftAtriumGeometry,
  createRightAtriumGeometry,
  createAortaGeometry,
  createPulmonaryArteryGeometry,
  createSuperiorVenaCavaGeometry,
  createInferiorVenaCavaGeometry,
  getHeartAnatomyColors,
} from './HeartGeometry'

// 组件属性
interface Props {
  caseData?: any
}

const props = defineProps<Props>()

// 响应式数据
const vtkContainer = ref<HTMLElement>()
const loading = ref(true)
const viewMode = ref('surface')
const opacity = ref(0.8)
const heartColor = ref('#ff6b6b')
const isAnimating = ref(false)

// 解剖结构可见性控制
const anatomyVisibility = ref({
  leftVentricle: true,
  rightVentricle: true,
  leftAtrium: true,
  rightAtrium: true,
  aorta: true,
  pulmonaryArtery: true,
  superiorVenaCava: true,
  inferiorVenaCava: true,
})

// VTK.js 相关变量
let fullScreenRenderer: any = null
let renderer: any = null
let renderWindow: any = null
let heartActor: any = null
let animationId: number | null = null

// 存储所有心脏解剖结构的actors
const heartActors = {
  leftVentricle: null as any,
  rightVentricle: null as any,
  leftAtrium: null as any,
  rightAtrium: null as any,
  aorta: null as any,
  pulmonaryArtery: null as any,
  superiorVenaCava: null as any,
  inferiorVenaCava: null as any,
}

// 初始化 VTK.js
const initVTK = async () => {
  try {
    loading.value = true

    if (!vtkContainer.value) {
      throw new Error('VTK容器未找到')
    }

    // 创建全屏渲染窗口
    fullScreenRenderer = vtkFullScreenRenderWindow.newInstance({
      container: vtkContainer.value,
      background: [0.1, 0.1, 0.2], // 深蓝色背景
    })

    renderer = fullScreenRenderer.getRenderer()
    renderWindow = fullScreenRenderer.getRenderWindow()

    // 创建心脏模型（使用多个几何体组合）
    await createHeartModel()

    // 设置相机
    setupCamera()

    loading.value = false
    ElMessage.success('3D心脏模型加载完成')
  } catch (error) {
    console.error('VTK初始化失败:', error)
    ElMessage.error('3D渲染器初始化失败')
    loading.value = false
  }
}

// 创建真实医学解剖心脏模型
const createHeartModel = async () => {
  const colors = getHeartAnatomyColors()

  // 1. 创建左心室
  const leftVentricleGeometry = createLeftVentricleGeometry()
  const leftVentricleMapper = vtkMapper.newInstance()
  leftVentricleMapper.setInputData(leftVentricleGeometry)

  const leftVentricleActor = vtkActor.newInstance()
  leftVentricleActor.setMapper(leftVentricleMapper)
  leftVentricleActor.getProperty().setColor(colors.leftVentricle[0], colors.leftVentricle[1], colors.leftVentricle[2])
  leftVentricleActor.getProperty().setOpacity(opacity.value)
  leftVentricleActor.getProperty().setSpecular(0.4)
  leftVentricleActor.getProperty().setSpecularPower(40)
  heartActors.leftVentricle = leftVentricleActor

  // 2. 创建右心室
  const rightVentricleGeometry = createRightVentricleGeometry()
  const rightVentricleMapper = vtkMapper.newInstance()
  rightVentricleMapper.setInputData(rightVentricleGeometry)

  const rightVentricleActor = vtkActor.newInstance()
  rightVentricleActor.setMapper(rightVentricleMapper)
  rightVentricleActor
    .getProperty()
    .setColor(colors.rightVentricle[0], colors.rightVentricle[1], colors.rightVentricle[2])
  rightVentricleActor.getProperty().setOpacity(opacity.value)
  rightVentricleActor.getProperty().setSpecular(0.4)
  rightVentricleActor.getProperty().setSpecularPower(40)
  heartActors.rightVentricle = rightVentricleActor

  // 3. 创建左心房
  const leftAtriumGeometry = createLeftAtriumGeometry()
  const leftAtriumMapper = vtkMapper.newInstance()
  leftAtriumMapper.setInputData(leftAtriumGeometry)

  const leftAtriumActor = vtkActor.newInstance()
  leftAtriumActor.setMapper(leftAtriumMapper)
  leftAtriumActor.getProperty().setColor(colors.leftAtrium[0], colors.leftAtrium[1], colors.leftAtrium[2])
  leftAtriumActor.getProperty().setOpacity(opacity.value * 0.8)
  leftAtriumActor.getProperty().setSpecular(0.3)
  leftAtriumActor.getProperty().setSpecularPower(30)
  heartActors.leftAtrium = leftAtriumActor

  // 4. 创建右心房
  const rightAtriumGeometry = createRightAtriumGeometry()
  const rightAtriumMapper = vtkMapper.newInstance()
  rightAtriumMapper.setInputData(rightAtriumGeometry)

  const rightAtriumActor = vtkActor.newInstance()
  rightAtriumActor.setMapper(rightAtriumMapper)
  rightAtriumActor.getProperty().setColor(colors.rightAtrium[0], colors.rightAtrium[1], colors.rightAtrium[2])
  rightAtriumActor.getProperty().setOpacity(opacity.value * 0.8)
  rightAtriumActor.getProperty().setSpecular(0.3)
  rightAtriumActor.getProperty().setSpecularPower(30)
  heartActors.rightAtrium = rightAtriumActor

  // 5. 创建主动脉
  const aortaGeometry = createAortaGeometry()
  const aortaMapper = vtkMapper.newInstance()
  aortaMapper.setInputData(aortaGeometry)

  const aortaActor = vtkActor.newInstance()
  aortaActor.setMapper(aortaMapper)
  aortaActor.getProperty().setColor(colors.aorta[0], colors.aorta[1], colors.aorta[2])
  aortaActor.getProperty().setOpacity(opacity.value)
  aortaActor.getProperty().setSpecular(0.5)
  aortaActor.getProperty().setSpecularPower(50)
  heartActors.aorta = aortaActor

  // 6. 创建肺动脉
  const pulmonaryArteryGeometry = createPulmonaryArteryGeometry()
  const pulmonaryArteryMapper = vtkMapper.newInstance()
  pulmonaryArteryMapper.setInputData(pulmonaryArteryGeometry)

  const pulmonaryArteryActor = vtkActor.newInstance()
  pulmonaryArteryActor.setMapper(pulmonaryArteryMapper)
  pulmonaryArteryActor
    .getProperty()
    .setColor(colors.pulmonaryArtery[0], colors.pulmonaryArtery[1], colors.pulmonaryArtery[2])
  pulmonaryArteryActor.getProperty().setOpacity(opacity.value)
  pulmonaryArteryActor.getProperty().setSpecular(0.5)
  pulmonaryArteryActor.getProperty().setSpecularPower(50)
  heartActors.pulmonaryArtery = pulmonaryArteryActor

  // 7. 创建上腔静脉
  const superiorVenaCavaGeometry = createSuperiorVenaCavaGeometry()
  const superiorVenaCavaMapper = vtkMapper.newInstance()
  superiorVenaCavaMapper.setInputData(superiorVenaCavaGeometry)

  const superiorVenaCavaActor = vtkActor.newInstance()
  superiorVenaCavaActor.setMapper(superiorVenaCavaMapper)
  superiorVenaCavaActor
    .getProperty()
    .setColor(colors.superiorVenaCava[0], colors.superiorVenaCava[1], colors.superiorVenaCava[2])
  superiorVenaCavaActor.getProperty().setOpacity(opacity.value)
  superiorVenaCavaActor.getProperty().setSpecular(0.3)
  superiorVenaCavaActor.getProperty().setSpecularPower(30)
  heartActors.superiorVenaCava = superiorVenaCavaActor

  // 8. 创建下腔静脉
  const inferiorVenaCavaGeometry = createInferiorVenaCavaGeometry()
  const inferiorVenaCavaMapper = vtkMapper.newInstance()
  inferiorVenaCavaMapper.setInputData(inferiorVenaCavaGeometry)

  const inferiorVenaCavaActor = vtkActor.newInstance()
  inferiorVenaCavaActor.setMapper(inferiorVenaCavaMapper)
  inferiorVenaCavaActor
    .getProperty()
    .setColor(colors.inferiorVenaCava[0], colors.inferiorVenaCava[1], colors.inferiorVenaCava[2])
  inferiorVenaCavaActor.getProperty().setOpacity(opacity.value)
  inferiorVenaCavaActor.getProperty().setSpecular(0.3)
  inferiorVenaCavaActor.getProperty().setSpecularPower(30)
  heartActors.inferiorVenaCava = inferiorVenaCavaActor

  // 添加所有解剖结构到渲染器
  Object.values(heartActors).forEach((actor) => {
    if (actor) {
      renderer.addActor(actor)
    }
  })

  // 设置主要actor为左心室（用于兼容性）
  heartActor = leftVentricleActor
}

// 设置相机
const setupCamera = () => {
  const camera = renderer.getActiveCamera()
  camera.setPosition(3, 3, 3)
  camera.setFocalPoint(0, 0, 0)
  camera.setViewUp(0, 1, 0)
  renderer.resetCamera()
  renderWindow.render()
}

// 改变视图模式
const changeViewMode = (mode: string) => {
  if (!heartActor) return

  // 获取所有心脏解剖结构的actors
  const actors = Object.values(heartActors).filter((actor) => actor !== null)

  actors.forEach((actor) => {
    if (actor) {
      switch (mode) {
        case 'surface':
          actor.getProperty().setRepresentationToSurface()
          break
        case 'wireframe':
          actor.getProperty().setRepresentationToWireframe()
          break
        case 'points':
          actor.getProperty().setRepresentationToPoints()
          actor.getProperty().setPointSize(3)
          break
      }
    }
  })

  renderWindow.render()
}

// 更新透明度
const updateOpacity = (value: number) => {
  if (!heartActor) return

  // 获取所有心脏解剖结构的actors
  const actors = Object.values(heartActors).filter((actor) => actor !== null)

  actors.forEach((actor) => {
    if (actor) {
      actor.getProperty().setOpacity(value)
    }
  })

  renderWindow.render()
}

// 更新颜色
const updateColor = (color: string) => {
  if (!heartActor || !color) return

  // 将十六进制颜色转换为RGB
  const hex = color.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16) / 255
  const g = parseInt(hex.substring(2, 4), 16) / 255
  const b = parseInt(hex.substring(4, 6), 16) / 255

  heartActor.getProperty().setColor(r, g, b)
  renderWindow.render()
}

// 切换解剖结构可见性
const toggleAnatomyVisibility = (anatomyName: keyof typeof anatomyVisibility.value) => {
  const actor = heartActors[anatomyName]
  if (actor && renderer) {
    const isVisible = anatomyVisibility.value[anatomyName]
    actor.setVisibility(isVisible)
    renderWindow.render()
  }
}

// 重置相机
const resetCamera = () => {
  if (!renderer) return

  renderer.resetCamera()
  renderWindow.render()
  ElMessage.success('视图已重置')
}

// 切换动画
const toggleAnimation = () => {
  if (isAnimating.value) {
    stopAnimation()
  } else {
    startAnimation()
  }
}

// 开始动画
const startAnimation = () => {
  if (!heartActor) return

  isAnimating.value = true
  let angle = 0

  const animate = () => {
    if (!isAnimating.value) return

    angle += 2

    // 旋转所有心脏组件
    Object.values(heartActors).forEach((actor) => {
      if (actor) {
        actor.setRotationZ(angle)

        // 模拟心脏跳动
        const scale = 1 + 0.1 * Math.sin(angle * 0.1)
        actor.setScale(scale, scale, scale)
      }
    })

    renderWindow.render()
    animationId = requestAnimationFrame(animate)
  }

  animate()
}

// 停止动画
const stopAnimation = () => {
  isAnimating.value = false
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initVTK()
})

// 组件卸载
onUnmounted(() => {
  stopAnimation()
  if (fullScreenRenderer) {
    fullScreenRenderer.delete()
  }
})

// 暴露方法给父组件
defineExpose({
  resetCamera,
  toggleAnimation,
  updateOpacity,
  updateColor,
})
</script>

<style scoped lang="scss">
.heart-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.vtk-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 280px;
  max-width: 320px;

  .control-group {
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .anatomy-controls {
      display: flex;
      flex-direction: column;
      gap: 6px;
      max-height: 200px;
      overflow-y: auto;

      .el-checkbox {
        font-size: 12px;
        margin: 0;

        :deep(.el-checkbox__label) {
          font-size: 12px !important;
          color: #333 !important;
          font-weight: 400 !important;
          padding-left: 8px;
        }

        :deep(.el-checkbox__input) {
          .el-checkbox__inner {
            border-color: #dcdfe6;
            background-color: #fff;
          }

          &.is-checked .el-checkbox__inner {
            background-color: #409eff;
            border-color: #409eff;
          }
        }
      }
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .loading-spinner {
    font-size: 32px;
    color: #409eff;

    .is-loading {
      animation: rotating 2s linear infinite;
    }
  }

  p {
    margin-top: 15px;
    color: #666;
    font-size: 14px;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .control-panel {
    position: static;
    margin: 10px;
    min-width: auto;
  }
}
</style>
