<template>
  <div class="heart-viewer-container">
    <!-- Three.js 渲染容器 -->
    <div ref="threeContainer" class="three-container"></div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <label>视图模式:</label>
        <el-radio-group v-model="viewMode" @change="changeViewMode">
          <el-radio-button value="surface">表面渲染</el-radio-button>
          <el-radio-button value="wireframe">线框模式</el-radio-button>
          <el-radio-button value="points">点云模式</el-radio-button>
        </el-radio-group>
      </div>

      <div class="control-group">
        <label>透明度:</label>
        <el-slider v-model="opacity" :min="0" :max="1" :step="0.1" @change="updateOpacity" style="width: 150px" />
      </div>

      <div class="control-group">
        <label>颜色:</label>
        <el-color-picker v-model="heartColor" @change="updateColor" />
      </div>



      <div class="control-group">
        <el-button @click="resetCamera" size="small">
          <el-icon><Refresh /></el-icon>
          重置视图
        </el-button>
        <el-button @click="toggleAnimation" size="small">
          <el-icon><VideoPlay /></el-icon>
          {{ isAnimating ? '停止' : '播放' }}
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
      </div>
      <p>正在加载3D心脏模型...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, VideoPlay, Loading } from '@element-plus/icons-vue'

// Three.js 导入
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

// 组件属性
interface Props {
  caseData?: any
}

const props = defineProps<Props>()

// 响应式数据
const threeContainer = ref<HTMLElement>()
const loading = ref(true)
const viewMode = ref('surface')
const opacity = ref(0.8)
const heartColor = ref('#ff6b6b')
const isAnimating = ref(false)

// Three.js 相关变量
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let heartModel: THREE.Group | null = null
let animationId: number | null = null
let controls: any = null

// 导入GLB模型URL
import heartModelUrl from '@/assets/models/Human-heart.glb'

// 初始化 Three.js
const initThreeJS = async () => {
  try {
    loading.value = true

    if (!threeContainer.value) {
      throw new Error('Three.js容器未找到')
    }

    // 创建场景
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0x1a1a33) // 深蓝色背景

    // 创建相机
    const containerRect = threeContainer.value.getBoundingClientRect()
    camera = new THREE.PerspectiveCamera(
      75,
      containerRect.width / containerRect.height,
      0.1,
      1000
    )
    camera.position.set(5, 5, 5)
    camera.lookAt(0, 0, 0)

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(containerRect.width, containerRect.height)
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    threeContainer.value.appendChild(renderer.domElement)

    // 添加轨道控制器
    const { OrbitControls } = await import('three/examples/jsm/controls/OrbitControls.js')
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05

    // 添加光照
    setupLighting()

    // 加载GLB心脏模型
    await loadHeartModel()

    // 开始渲染循环
    animate()

    loading.value = false
    ElMessage.success('3D心脏模型加载完成')
  } catch (error) {
    console.error('Three.js初始化失败:', error)
    ElMessage.error('3D渲染器初始化失败')
    loading.value = false
  }
}

// 设置光照
const setupLighting = () => {
  if (!scene) return

  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  // 主光源
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // 补充光源
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.3)
  fillLight.position.set(-10, -10, -5)
  scene.add(fillLight)
}

// 加载GLB心脏模型
const loadHeartModel = async () => {
  if (!scene) return

  try {
    const loader = new GLTFLoader()
    const gltf = await new Promise<any>((resolve, reject) => {
      loader.load(
        heartModelUrl,
        (gltf) => resolve(gltf),
        (progress) => {
          console.log('加载进度:', (progress.loaded / progress.total * 100) + '%')
        },
        (error) => reject(error)
      )
    })

    heartModel = gltf.scene

    // 设置模型属性
    heartModel.traverse((child: any) => {
      if (child.isMesh) {
        child.castShadow = true
        child.receiveShadow = true

        // 设置材质属性
        if (child.material) {
          child.material.transparent = true
          child.material.opacity = opacity.value
        }
      }
    })

    // 调整模型大小和位置
    const box = new THREE.Box3().setFromObject(heartModel)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())

    // 居中模型
    heartModel.position.sub(center)

    // 缩放模型以适应视图
    const maxDim = Math.max(size.x, size.y, size.z)
    const scale = 3 / maxDim
    heartModel.scale.setScalar(scale)

    scene.add(heartModel)

    ElMessage.success('心脏模型加载成功')
  } catch (error) {
    console.error('GLB模型加载失败:', error)
    ElMessage.error('心脏模型加载失败')
  }
}

// 渲染循环
const animate = () => {
  if (!renderer || !scene || !camera) return

  // 更新控制器
  if (controls) {
    controls.update()
  }

  // 渲染场景
  renderer.render(scene, camera)

  // 继续动画循环
  animationId = requestAnimationFrame(animate)
}

// 改变视图模式
const changeViewMode = (mode: string) => {
  if (!heartModel) return

  heartModel.traverse((child: any) => {
    if (child.isMesh && child.material) {
      switch (mode) {
        case 'surface':
          child.material.wireframe = false
          child.material.transparent = true
          child.material.opacity = opacity.value
          break
        case 'wireframe':
          child.material.wireframe = true
          child.material.transparent = false
          child.material.opacity = 1
          break
        case 'points':
          // Three.js中点云模式需要特殊处理
          child.material.wireframe = false
          child.material.transparent = true
          child.material.opacity = opacity.value
          break
      }
    }
  })
}

// 更新透明度
const updateOpacity = (value: number) => {
  if (!heartModel) return

  heartModel.traverse((child: any) => {
    if (child.isMesh && child.material) {
      child.material.opacity = value
      child.material.transparent = value < 1
    }
  })
}

// 更新颜色
const updateColor = (color: string) => {
  if (!heartModel || !color) return

  const threeColor = new THREE.Color(color)

  heartModel.traverse((child: any) => {
    if (child.isMesh && child.material) {
      child.material.color = threeColor
    }
  })
}

// 重置相机
const resetCamera = () => {
  if (!camera || !controls) return

  // 重置相机位置
  camera.position.set(5, 5, 5)
  camera.lookAt(0, 0, 0)

  // 重置控制器
  controls.reset()

  ElMessage.success('视图已重置')
}

// 切换动画
const toggleAnimation = () => {
  if (isAnimating.value) {
    stopAnimation()
  } else {
    startAnimation()
  }
}

// 开始动画
const startAnimation = () => {
  if (!heartModel) return

  isAnimating.value = true
  let angle = 0

  const animateHeart = () => {
    if (!isAnimating.value || !heartModel) return

    angle += 0.02

    // 旋转心脏模型
    heartModel.rotation.y = angle

    // 模拟心脏跳动
    const scale = 1 + 0.05 * Math.sin(angle * 5)
    heartModel.scale.setScalar(scale * 3 / Math.max(
      heartModel.userData.originalSize?.x || 1,
      heartModel.userData.originalSize?.y || 1,
      heartModel.userData.originalSize?.z || 1
    ))

    // 继续动画
    requestAnimationFrame(animateHeart)
  }

  animateHeart()
}

// 停止动画
const stopAnimation = () => {
  isAnimating.value = false
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initThreeJS()
})

// 组件卸载
onUnmounted(() => {
  stopAnimation()

  // 清理Three.js资源
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (renderer) {
    renderer.dispose()
  }

  if (controls) {
    controls.dispose()
  }

  // 清理场景中的对象
  if (scene) {
    scene.clear()
  }
})

// 暴露方法给父组件
defineExpose({
  resetCamera,
  toggleAnimation,
  updateOpacity,
  updateColor,
})
</script>

<style scoped lang="scss">
.heart-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 280px;
  max-width: 320px;

  .control-group {
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .anatomy-controls {
      display: flex;
      flex-direction: column;
      gap: 6px;
      max-height: 200px;
      overflow-y: auto;

      .el-checkbox {
        font-size: 12px;
        margin: 0;

        :deep(.el-checkbox__label) {
          font-size: 12px !important;
          color: #333 !important;
          font-weight: 400 !important;
          padding-left: 8px;
        }

        :deep(.el-checkbox__input) {
          .el-checkbox__inner {
            border-color: #dcdfe6;
            background-color: #fff;
          }

          &.is-checked .el-checkbox__inner {
            background-color: #409eff;
            border-color: #409eff;
          }
        }
      }
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .loading-spinner {
    font-size: 32px;
    color: #409eff;

    .is-loading {
      animation: rotating 2s linear infinite;
    }
  }

  p {
    margin-top: 15px;
    color: #666;
    font-size: 14px;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .control-panel {
    position: static;
    margin: 10px;
    min-width: auto;
  }
}
</style>
