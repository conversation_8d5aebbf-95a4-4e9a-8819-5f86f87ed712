<template>
  <el-dialog 
    :model-value="visible" 
    @update:model-value="updateVisible"
    title="数据预览" 
    width="600px"
  >
    <div class="preview-content">
      <div class="preview-section">
        <h4><el-icon><Picture /></el-icon> 上传图像</h4>
        <p><strong>文件名：</strong>{{ formData.fileName }}</p>
        <img v-if="formData.imagePreview" :src="formData.imagePreview" class="preview-image" />
      </div>

      <div class="preview-section">
        <h4><el-icon><Location /></el-icon> 检查信息</h4>
        <p><strong>检查部位：</strong>{{ getExamPartText(formData.examPart) || '未选择' }}</p>
        <p><strong>切面类型：</strong>{{ getSliceTypeText(formData.sliceType) || '未选择' }}</p>
      </div>

      <div class="preview-section">
        <h4><el-icon><Search /></el-icon> 图像特征分析</h4>
        <div class="preview-tags">
          <el-tag v-for="tag in formData.caption" :key="tag" type="success" size="small">
            {{ tag }}
          </el-tag>
          <span v-if="formData.caption.length === 0" class="empty-text">暂无</span>
        </div>
      </div>

      <div class="preview-section">
        <h4><el-icon><UserFilled /></el-icon> 诊断联想</h4>
        <div class="preview-tags">
          <el-tag v-for="tag in formData.associate" :key="tag" type="primary" size="small">
            {{ tag }}
          </el-tag>
          <span v-if="formData.associate.length === 0" class="empty-text">暂无</span>
        </div>
      </div>

      <div class="preview-section">
        <h4><el-icon><DocumentChecked /></el-icon> 诊断依据</h4>
        <div class="preview-tags">
          <el-tag v-for="tag in formData.basis" :key="tag" type="warning" size="small">
            {{ tag }}
          </el-tag>
          <span v-if="formData.basis.length === 0" class="empty-text">暂无</span>
        </div>
      </div>

      <div class="preview-section">
        <h4><el-icon><Medal /></el-icon> 最终诊断</h4>
        <p><strong>分类：</strong>{{ diagnosisTypeText }}</p>
        <p><strong>详细诊断：</strong>{{ formData.detail }}</p>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { Picture, Search, UserFilled, DocumentChecked, Medal, Location } from '@element-plus/icons-vue'
import type { MedicalFormData } from '../types'

// 定义组件名称
defineOptions({
  name: 'DataPreview'
})

// Props 定义
interface Props {
  visible: boolean
  formData: MedicalFormData
  diagnosisTypeText: string
}

defineProps<Props>()

// Emits 定义
interface Emits {
  'update:visible': [visible: boolean]
}

const emit = defineEmits<Emits>()

// 更新显示状态
const updateVisible = (value: boolean) => {
  emit('update:visible', value)
}

// 获取检查部位文本
const getExamPartText = (value?: string) => {
  if (!value) return ''
  const options = {
    'thyroid': '甲状腺',
    'breast': '乳房',
    'liver': '肝脏',
    'gallbladder': '胆囊',
    'heart': '心脏',
    'pancreas': '胰腺',
    'spleen': '脾脏',
    'prostate': '前列腺',
  }
  return options[value as keyof typeof options] || value
}

// 获取切面类型文本
const getSliceTypeText = (value?: string) => {
  if (!value) return ''
  const options = {
    'sagittal': '矢状面',
    'coronal': '冠状面',
    'transverse': '横断面',
    'oblique': '斜切面',
    'longitudinal': '纵切面',
    'cross': '横切面',
  }
  return options[value as keyof typeof options] || value
}
</script>

<style scoped>
.preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.preview-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.preview-section:last-child {
  border-bottom: none;
}

.preview-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.preview-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 6px;
  margin-top: 8px;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.empty-text {
  color: #c0c4cc;
  font-style: italic;
}
</style>
