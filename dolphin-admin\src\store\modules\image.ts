import { defineStore } from 'pinia'
import type { ImageState, ImageData, AnnotatedImageData, AnnotationPoint, LesionAnnotation } from './types/types'

/**
 * 图像状态管理Store
 *
 * 功能：
 * - 保存原始裁剪后的图像数据（用于标注，始终不变）
 * - 保存最新的标注后图片数据（用于显示）
 * - 管理图像在不同页面间的传递
 * - 提供图像数据的清理机制
 */
const useImageStore = defineStore('Image', {
  state: (): ImageState => ({
    // 原始裁剪后的图像数据（用于标注，始终不变）
    originalCroppedImage: null,
    // 最新的标注后图片数据（用于显示）
    latestAnnotatedImage: null,
    // Canvas快照上传后的imageId（用于替换原始图片ID）
    canvasSnapshotImageId: null,
    // 是否来自裁剪流程
    fromCropping: false,
    // 创建时间戳
    timestamp: null,

    // 医学信息
    currentLesionDescription: '',
    examPart: '',
    sliceType: '',

    // 标注数据
    currentAnnotations: [],
    exportedLesions: [],
    selectedLesionId: null
  }),

  actions: {
    /**
     * 设置原始裁剪后的图像数据（用于标注）
     * @param imageData 图像数据
     */
    setOriginalCroppedImage(imageData: ImageData) {
      this.originalCroppedImage = imageData
      this.fromCropping = true
      this.timestamp = Date.now()
    },

    /**
     * 获取原始裁剪后的图像数据（用于标注）
     * @returns 图像数据或null
     */
    getOriginalCroppedImage(): ImageData | null {
      if (this.originalCroppedImage && this.fromCropping) {
        return this.originalCroppedImage
      }

      return null
    },

    /**
     * 设置最新的标注后图片数据（用于显示）
     * @param annotatedData 标注后图片数据
     */
    setLatestAnnotatedImage(annotatedData: AnnotatedImageData) {
      this.latestAnnotatedImage = annotatedData
    },

    /**
     * 更新Canvas快照（用于持久化显示）
     * @param canvasDataURL Canvas导出的base64图像数据
     */
    updateCanvasSnapshot(canvasDataURL: string) {
      if (this.latestAnnotatedImage) {
        // 如果已有标注后图片，创建新的文件对象以确保包含最新的标注内容
        // 将最新的Canvas快照转换为File对象
        const newCanvasFile = this.dataURLtoFile(canvasDataURL, `canvas_snapshot_${Date.now()}.png`)

        this.latestAnnotatedImage.previewUrl = canvasDataURL
        this.latestAnnotatedImage.file = newCanvasFile  // 更新文件对象
        this.latestAnnotatedImage.timestamp = Date.now()
      } else if (this.originalCroppedImage) {
        // 基于原始图像创建新的标注图像（仅在没有标注图片时）

        // 将Canvas快照转换为File对象
        const canvasFile = this.dataURLtoFile(canvasDataURL, `canvas_snapshot_${Date.now()}.png`)

        this.latestAnnotatedImage = {
          imageId: `annotated_${Date.now()}`,
          previewUrl: canvasDataURL,
          file: canvasFile,
          originalImageId: this.originalCroppedImage.id.toString(),
          timestamp: Date.now()
        }
      }

      this.timestamp = Date.now()
    },

    /**
     * 将DataURL转换为File对象
     * @param dataURL base64数据URL
     * @param fileName 文件名
     * @returns File对象
     */
    dataURLtoFile(dataURL: string, fileName: string): File {
      const arr = dataURL.split(',')
      const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png'
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], fileName, { type: mime })
    },

    /**
     * 检查是否有Canvas快照
     * @returns 是否存在快照
     */
    hasCanvasSnapshot(): boolean {
      return this.latestAnnotatedImage !== null
    },

    /**
     * 设置Canvas快照上传后的imageId
     * @param imageId Canvas快照上传后返回的imageId
     */
    setCanvasSnapshotImageId(imageId: string) {
      this.canvasSnapshotImageId = imageId
    },

    /**
     * 清除Canvas快照imageId（用于重新标注时确保上传新图像）
     */
    clearCanvasSnapshotImageId() {
      this.canvasSnapshotImageId = null
    },

    /**
     * 获取Canvas快照的imageId（用于提交数据）
     * @returns Canvas快照的imageId，如果没有则返回原始图片ID
     */
    getImageIdForSubmit(): string | null {
      // 优先返回Canvas快照的imageId
      if (this.canvasSnapshotImageId) {
        return this.canvasSnapshotImageId
      }

      // 如果没有Canvas快照，返回原始图片ID
      if (this.originalCroppedImage?.id) {
        return this.originalCroppedImage.id.toString()
      }

      console.warn('⚠️ 没有可用的imageId')
      return null
    },

    /**
     * 获取最新的标注后图片数据（用于显示）
     * @returns 标注后图片数据或null
     */
    getLatestAnnotatedImage(): AnnotatedImageData | null {
      if (this.latestAnnotatedImage) {
        return this.latestAnnotatedImage
      }

      return null
    },

    /**
     * 清理所有图像数据（保留医学信息和标注数据）
     */
    clearAllImages() {
      this.originalCroppedImage = null
      this.latestAnnotatedImage = null
      this.fromCropping = false
      this.timestamp = null
    },

    // ==================== 当前病灶信息管理 ====================

    /**
     * 设置当前病灶描述信息
     * @param description 病灶描述
     */
    setCurrentLesionDescription(description: string) {
      this.currentLesionDescription = description
    },

    /**
     * 获取当前病灶描述信息
     * @returns 病灶描述
     */
    getCurrentLesionDescription(): string {
      return this.currentLesionDescription || ''
    },

    /**
     * 清空当前病灶描述
     */
    clearCurrentLesionDescription() {
      this.currentLesionDescription = ''
    },

    /**
     * 设置检查信息
     * @param examPart 检查部位
     * @param sliceType 切面类型
     */
    setExamInfo(examPart: string, sliceType: string) {
      this.examPart = examPart
      this.sliceType = sliceType
    },

    /**
     * 获取检查信息
     * @returns 检查信息对象
     */
    getExamInfo(): { examPart: string; sliceType: string } {
      return {
        examPart: this.examPart || '',
        sliceType: this.sliceType || ''
      }
    },

    // ==================== 当前标注管理 ====================

    /**
     * 添加当前标注点
     * @param annotation 标注点数据
     */
    addCurrentAnnotation(annotation: AnnotationPoint) {
      this.currentAnnotations.push(annotation)
    },

    /**
     * 获取当前标注数据
     * @returns 当前标注点数组
     */
    getCurrentAnnotations(): AnnotationPoint[] {
      return this.currentAnnotations || []
    },

    /**
     * 撤销最后一个当前标注点
     * @returns 被撤销的标注点或null
     */
    undoLastCurrentAnnotation(): AnnotationPoint | null {
      if (this.currentAnnotations.length === 0) {
        return null
      }

      const lastAnnotation = this.currentAnnotations.pop()
      return lastAnnotation || null
    },

    /**
     * 清空当前标注数据
     */
    clearCurrentAnnotations() {
      this.currentAnnotations = []
    },

    // ==================== 多病灶管理 ====================

    /**
     * 导出当前标注为病灶
     * @returns 导出的病灶ID
     */
    exportCurrentLesion(): string | null {
      if (this.currentAnnotations.length < 3) {
        return null
      }

      if (!this.currentLesionDescription.trim()) {
        return null
      }

      // 生成病灶ID
      const lesionId = `lesion_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      // 生成颜色（简单的颜色循环）
      const colors = ['#ff4757', '#2ed573', '#3742fa', '#ffa502', '#ff6b81', '#70a1ff']
      const color = colors[this.exportedLesions.length % colors.length]

      // 创建病灶对象
      const lesion: LesionAnnotation = {
        id: lesionId,
        description: this.currentLesionDescription.trim(),
        points: [...this.currentAnnotations], // 深拷贝标注点
        color,
        createdAt: Date.now(),
        isVisible: true
      }

      // 添加到已导出列表
      this.exportedLesions.push(lesion)

      // 清空当前数据
      this.currentLesionDescription = ''
      this.currentAnnotations = []

      // 触发闭合区域事件（通过事件总线或者直接调用画布方法）
      // 这里我们返回lesionId，让调用方处理闭合逻辑
      return lesionId
    },

    /**
     * 获取所有已导出的病灶
     * @returns 病灶列表
     */
    getExportedLesions(): LesionAnnotation[] {
      return this.exportedLesions || []
    },

    /**
     * 根据ID获取病灶
     * @param lesionId 病灶ID
     * @returns 病灶对象或null
     */
    getLesionById(lesionId: string): LesionAnnotation | null {
      return this.exportedLesions.find(lesion => lesion.id === lesionId) || null
    },

    /**
     * 删除病灶
     * @param lesionId 病灶ID
     * @returns 是否删除成功
     */
    deleteLesion(lesionId: string): boolean {
      const index = this.exportedLesions.findIndex(lesion => lesion.id === lesionId)
      if (index === -1) {
        return false
      }

      this.exportedLesions.splice(index, 1)
      return true
    },

    /**
     * 设置选中的病灶
     * @param lesionId 病灶ID
     */
    setSelectedLesion(lesionId: string | null) {
      this.selectedLesionId = lesionId
    },

    /**
     * 获取选中的病灶
     * @returns 选中的病灶对象或null
     */
    getSelectedLesion(): LesionAnnotation | null {
      if (!this.selectedLesionId) return null
      return this.getLesionById(this.selectedLesionId)
    },

    /**
     * 清空所有已导出的病灶
     */
    clearAllExportedLesions() {
      this.exportedLesions = []
      this.selectedLesionId = null
    },

    /**
     * 清除所有标注信息（保留图片数据和Canvas快照）
     */
    clearAnnotationsOnly() {
      // 清理医学信息
      this.currentLesionDescription = ''
      this.examPart = ''
      this.sliceType = ''

      // 清理标注数据
      this.currentAnnotations = []
      this.exportedLesions = []
      this.selectedLesionId = null
      this.canvasSnapshotImageId = null

      // 保留图像数据和Canvas快照
      // this.originalCroppedImage - 保留
      // this.latestAnnotatedImage - 保留
      // this.fromCropping - 保留
      // this.timestamp - 保留
    },

    // ==================== 完整数据管理 ====================

    /**
     * 清理所有数据（图像+医学信息+标注）
     */
    clearAllData() {
      // 清理图像数据
      this.originalCroppedImage = null
      this.latestAnnotatedImage = null
      this.fromCropping = false
      this.timestamp = null

      // 清理医学信息
      this.currentLesionDescription = ''
      this.examPart = ''
      this.sliceType = ''

      // 清理标注数据
      this.currentAnnotations = []
      this.exportedLesions = []
      this.selectedLesionId = null
    },

    /**
     * 获取完整的医学数据（用于提交）
     * @returns 完整的医学数据对象
     */
    getMedicalData() {
      return {
        currentLesionDescription: this.currentLesionDescription,
        examPart: this.examPart,
        sliceType: this.sliceType,
        exportedLesions: this.exportedLesions,
        lesionCount: this.exportedLesions.length,
        hasLesions: this.exportedLesions.length > 0
      }
    },

    /**
     * 获取所有病灶的标注点（用于数据采集页面）
     * @returns 所有标注点的坐标数组
     */
    getAllAnnotationPoints(): AnnotationPoint[] {
      const allPoints: AnnotationPoint[] = []
      this.exportedLesions.forEach(lesion => {
        allPoints.push(...lesion.points)
      })
      return allPoints
    },

    /**
     * 获取合并的病灶描述（用于数据采集页面）
     * @returns 合并的病灶描述字符串
     */
    getCombinedLesionDescription(): string {
      if (this.exportedLesions.length === 0) return ''

      return this.exportedLesions
        .map((lesion, index) => `病灶${index + 1}: ${lesion.description}`)
        .join('\n')
    },

    /**
     * 检查是否有有效的原始裁剪图像数据
     * @returns 是否有有效数据
     */
    hasOriginalCroppedImage(): boolean {
      return !!(this.originalCroppedImage && this.fromCropping)
    },

    /**
     * 检查是否有标注后图片数据
     * @returns 是否有标注后图片数据
     */
    hasAnnotatedImage(): boolean {
      return !!this.latestAnnotatedImage
    },



    /**
     * 检查图像数据是否过期（超过5分钟）
     * @returns 是否过期
     */
    isImageExpired(): boolean {
      if (!this.timestamp) return true

      const now = Date.now()
      const expireTime = 5 * 60 * 1000 // 5分钟

      return (now - this.timestamp) > expireTime
    },

    /**
     * 清理过期的图像数据
     */
    cleanupExpiredImage() {
      if (this.hasOriginalCroppedImage() && this.isImageExpired()) {
        this.clearAllImages()
      }
    },

    // 兼容性方法，保持向后兼容
    /**
     * @deprecated 使用 setOriginalCroppedImage 替代
     */
    setCroppedImage(imageData: ImageData) {
      this.setOriginalCroppedImage(imageData)
    },

    /**
     * @deprecated 使用 getOriginalCroppedImage 替代
     */
    getCroppedImage(): ImageData | null {
      return this.getOriginalCroppedImage()
    },

    /**
     * @deprecated 使用 hasOriginalCroppedImage 替代
     */
    hasCroppedImage(): boolean {
      return this.hasOriginalCroppedImage()
    }
  },

  getters: {
    /**
     * 获取原始图像数据的基本信息
     */
    originalImageInfo: (state) => {
      if (!state.originalCroppedImage) return null

      return {
        name: state.originalCroppedImage.name,
        size: state.originalCroppedImage.size,
        type: state.originalCroppedImage.type,
        dimensions: `${state.originalCroppedImage.originalWidth}x${state.originalCroppedImage.originalHeight}`,
        uploadTime: state.originalCroppedImage.uploadTime,
        fromCropping: state.fromCropping,
        timestamp: state.timestamp
      }
    },

    /**
     * 获取标注后图片的基本信息
     */
    annotatedImageInfo: (state) => {
      if (!state.latestAnnotatedImage) return null

      return {
        imageId: state.latestAnnotatedImage.imageId,
        previewUrl: state.latestAnnotatedImage.previewUrl,
        fileName: state.latestAnnotatedImage.file.name,
        fileSize: state.latestAnnotatedImage.file.size,
        originalImageId: state.latestAnnotatedImage.originalImageId,
        timestamp: state.latestAnnotatedImage.timestamp
      }
    },

    /**
     * 检查是否可以进行标注
     */
    canAnnotate: (state) => {
      return state.originalCroppedImage &&
        state.fromCropping &&
        state.originalCroppedImage.originalWidth === 720 &&
        state.originalCroppedImage.originalHeight === 720
    },

    /**
     * 获取用于显示的图片信息（优先显示标注后的图片）
     */
    displayImageInfo: (state) => {
      // 如果有标注后的图片，优先返回标注后的图片信息
      if (state.latestAnnotatedImage) {
        return {
          imageId: state.latestAnnotatedImage.imageId,
          previewUrl: state.latestAnnotatedImage.previewUrl,
          fileName: state.latestAnnotatedImage.file.name,
          isAnnotated: true
        }
      }

      // 否则返回原始图片信息
      if (state.originalCroppedImage) {
        return {
          imageId: String(state.originalCroppedImage.id),
          previewUrl: state.originalCroppedImage.src,
          fileName: state.originalCroppedImage.name,
          isAnnotated: false
        }
      }

      return null
    }
  }
})

export default useImageStore
