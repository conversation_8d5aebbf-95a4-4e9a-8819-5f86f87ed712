/**
 * 认证调试工具
 * 用于监控和调试token状态变化
 */

import { GET_TOKEN, GET_REFRESH_TOKEN, GET_USER_INFO } from '@/utils/token'

export interface AuthDebugInfo {
  timestamp: string
  hasToken: boolean
  hasRefreshToken: boolean
  hasUserInfo: boolean
  tokenLength?: number
  username?: string
  action: string
  location: string
}

class AuthDebugger {
  private logs: AuthDebugInfo[] = []
  private maxLogs = 50

  /**
   * 记录认证状态
   */
  log(action: string, location: string = '') {
    const token = GET_TOKEN()
    const refreshToken = GET_REFRESH_TOKEN()
    const userInfo = GET_USER_INFO()

    const debugInfo: AuthDebugInfo = {
      timestamp: new Date().toISOString(),
      hasToken: !!token,
      hasRefreshToken: !!refreshToken,
      hasUserInfo: !!userInfo,
      tokenLength: token?.length,
      username: userInfo?.username,
      action,
      location
    }

    this.logs.push(debugInfo)
    
    // 保持日志数量在限制内
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // 在开发环境下输出到控制台
    if (import.meta.env.NODE_ENV === 'development') {
      console.log(`🔐 [AuthDebug] ${action}:`, debugInfo)
    }

    // 保存到localStorage用于调试
    localStorage.setItem('authDebugLogs', JSON.stringify(this.logs))
  }

  /**
   * 获取所有日志
   */
  getLogs(): AuthDebugInfo[] {
    return [...this.logs]
  }

  /**
   * 获取最近的日志
   */
  getRecentLogs(count: number = 10): AuthDebugInfo[] {
    return this.logs.slice(-count)
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
    localStorage.removeItem('authDebugLogs')
  }

  /**
   * 从localStorage恢复日志
   */
  restoreLogs() {
    try {
      const savedLogs = localStorage.getItem('authDebugLogs')
      if (savedLogs) {
        this.logs = JSON.parse(savedLogs)
      }
    } catch (error) {
      console.warn('恢复认证调试日志失败:', error)
    }
  }

  /**
   * 检查认证状态异常
   */
  checkAuthAnomalies(): string[] {
    const anomalies: string[] = []
    const recentLogs = this.getRecentLogs(5)

    // 检查token突然消失
    for (let i = 1; i < recentLogs.length; i++) {
      const prev = recentLogs[i - 1]
      const curr = recentLogs[i]

      if (prev.hasToken && !curr.hasToken) {
        anomalies.push(`Token在${curr.timestamp}突然消失，上一次操作：${prev.action}`)
      }

      if (prev.hasUserInfo && !curr.hasUserInfo) {
        anomalies.push(`用户信息在${curr.timestamp}丢失，上一次操作：${prev.action}`)
      }
    }

    return anomalies
  }

  /**
   * 生成调试报告
   */
  generateReport(): string {
    const current = {
      hasToken: !!GET_TOKEN(),
      hasRefreshToken: !!GET_REFRESH_TOKEN(),
      hasUserInfo: !!GET_USER_INFO(),
      userInfo: GET_USER_INFO()
    }

    const anomalies = this.checkAuthAnomalies()
    const recentLogs = this.getRecentLogs(10)

    return `
=== 认证状态调试报告 ===
生成时间: ${new Date().toISOString()}

当前状态:
- Token: ${current.hasToken ? '✅ 存在' : '❌ 不存在'}
- RefreshToken: ${current.hasRefreshToken ? '✅ 存在' : '❌ 不存在'}
- 用户信息: ${current.hasUserInfo ? '✅ 存在' : '❌ 不存在'}
- 用户名: ${current.userInfo?.username || '无'}

异常检测:
${anomalies.length > 0 ? anomalies.map(a => `⚠️ ${a}`).join('\n') : '✅ 未发现异常'}

最近操作记录:
${recentLogs.map(log => 
  `${log.timestamp} | ${log.action} | Token:${log.hasToken ? '✅' : '❌'} | User:${log.hasUserInfo ? '✅' : '❌'} | ${log.location}`
).join('\n')}
    `.trim()
  }
}

// 创建全局实例
export const authDebugger = new AuthDebugger()

// 页面加载时恢复日志
authDebugger.restoreLogs()

// 在window对象上暴露调试工具（仅开发环境）
if (import.meta.env.NODE_ENV === 'development') {
  (window as any).authDebugger = authDebugger
}

export default authDebugger
