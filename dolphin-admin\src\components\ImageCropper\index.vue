<template>
  <el-dialog
    v-model="visible"
    title="图像裁剪"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    @close="handleCancel"
  >
    <div class="image-cropper-container">
      <!-- 裁剪区域 -->
      <div class="cropper-wrapper">
        <Cropper
          ref="cropperRef"
          :src="imageUrl"
          :stencil-props="{ aspectRatio: 1 }"
          :canvas="{
            width: 720,
            height: 720,
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high',
          }"
          :auto-zoom="true"
          :check-orientation="true"
          :transitions="true"
          @change="handleChange"
          @ready="handleReady"
          @error="handleError"
        />
      </div>

      <!-- 预览区域 -->
      <div class="preview-wrapper">
        <div class="preview-title">裁剪预览</div>
        <div class="preview-container">
          <div class="preview-box" :style="previewStyle">
            <img v-if="previewImg" :src="previewImg" alt="预览图像" />
            <div v-else class="preview-placeholder">
              <el-icon><Picture /></el-icon>
              <span>预览区域</span>
            </div>
          </div>
        </div>
        <div class="preview-info">
          <p>输出尺寸：720 × 720 像素</p>
          <p>文件格式：PNG</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="processing">确认裁剪</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { Cropper } from 'vue-advanced-cropper'
import 'vue-advanced-cropper/dist/style.css'

// 定义组件名称
defineOptions({
  name: 'ImageCropper',
})

// Props 定义
interface Props {
  modelValue: boolean
  imageUrl: string
  fileName?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  imageUrl: '',
  fileName: '',
})

// Events 定义
interface Emits {
  'update:modelValue': [value: boolean]
  confirm: [croppedFile: File, croppedDataUrl: string]
  cancel: []
}

const emit = defineEmits<Emits>()

// 响应式数据
const cropperRef = ref()
const previewImg = ref('')
const processing = ref(false)
const cropperReady = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})

const previewStyle = computed(() => ({
  width: '200px',
  height: '200px',
  border: '2px solid #e2e8f0',
  borderRadius: '8px',
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f8fafc',
}))

// 裁剪器变化处理（实时预览）
const handleChange = (result: any) => {
  if (result.canvas) {
    previewImg.value = result.canvas.toDataURL('image/png')
  }
}

// 裁剪器准备就绪
const handleReady = () => {
  cropperReady.value = true
}

// 裁剪器错误处理
const handleError = (error: any) => {
  console.error('裁剪器加载错误:', error)
  ElMessage.error('图像加载失败')
}

// 确认裁剪
const handleConfirm = async () => {
  if (!cropperRef.value || !cropperReady.value) {
    ElMessage.error('裁剪器未初始化')
    return
  }

  try {
    processing.value = true

    // 获取裁剪结果
    const result = cropperRef.value.getResult()

    if (!result.canvas) {
      ElMessage.error('无法获取裁剪结果')
      processing.value = false
      return
    }

    // canvas 已经是 720x720，直接转换为 blob
    result.canvas.toBlob(
      (blob: Blob | null) => {
        if (blob) {
          const fileName = props.fileName || `cropped_image_${Date.now()}.png`
          const croppedFile = new File([blob], fileName, { type: 'image/png' })
          const croppedDataUrl = result.canvas.toDataURL('image/png')

          emit('confirm', croppedFile, croppedDataUrl)
          visible.value = false
        } else {
          ElMessage.error('图像处理失败')
        }
        processing.value = false
      },
      'image/png',
      1.0,
    )
  } catch (error) {
    console.error('裁剪失败:', error)
    ElMessage.error('裁剪失败，请重试')
    processing.value = false
  }
}

// 取消裁剪
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

// 重置裁剪器
const resetCropper = async () => {
  await nextTick()
  if (cropperRef.value) {
    cropperRef.value.refresh()
  }
}

// 暴露方法给父组件
defineExpose({
  resetCropper,
})
</script>

<style scoped>
.image-cropper-container {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

.cropper-wrapper {
  flex: 1;
  min-height: 400px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: #f8fafc;
}

.preview-wrapper {
  width: 240px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

.preview-container {
  display: flex;
  justify-content: center;
}

.preview-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #9ca3af;
  font-size: 14px;
}

.preview-placeholder .el-icon {
  font-size: 32px;
}

.preview-info {
  background: #f3f4f6;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  color: #6b7280;
}

.preview-info p {
  margin: 4px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 深度选择器调整vue-advanced-cropper样式 */
:deep(.vue-advanced-cropper) {
  height: 400px !important;
  background: #f8fafc;
}

:deep(.vue-advanced-cropper__image) {
  max-height: 400px;
}

:deep(.vue-advanced-cropper__stencil) {
  border: 2px solid #3b82f6 !important;
}

:deep(.vue-rectangle-stencil__handler) {
  background: #3b82f6 !important;
  border: 1px solid #ffffff !important;
}

:deep(.vue-rectangle-stencil__line) {
  border-color: #3b82f6 !important;
}
</style>
