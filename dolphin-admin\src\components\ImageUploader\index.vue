<template>
  <el-upload
    ref="uploadRef"
    class="image-uploader"
    :show-file-list="false"
    :before-upload="beforeUpload"
    :on-success="handleUploadSuccess"
    :on-error="handleUploadError"
    :accept="accept"
    :disabled="disabled || !!imagePreview"
    drag
  >
    <div v-if="!imagePreview" class="upload-placeholder">
      <SvgIcon name="down" class="upload-icon" width="64px" height="64px" />
      <div class="upload-text">
        {{ uploading ? '正在上传...' : '点击或拖拽上传医学图像' }}
      </div>
    </div>
    <div v-else class="image-preview">
      <img :src="imagePreview" alt="预览图像" />
      <div class="image-overlay">
        <div class="action-buttons">
          <!-- 标注图标按钮 -->
          <el-button
            v-if="showAnnotationButton"
            :type="hasAnnotationData ? 'warning' : 'primary'"
            size="small"
            circle
            class="annotation-btn"
            @click.stop="handleAnnotateImage"
            :title="hasAnnotationData ? '重新标注' : '图像标注'"
          >
            <el-icon><EditPen /></el-icon>
          </el-button>
          <!-- 删除图标按钮 -->
          <el-button type="danger" size="small" circle class="delete-btn" @click.stop="removeImage" title="删除图像">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </el-upload>

  <!-- 图像裁剪弹窗 -->
  <image-cropper
    v-model="showCropper"
    :image-url="originalImageUrl"
    :file-name="originalFileName"
    @confirm="handleCropConfirm"
    @cancel="handleCropCancel"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, type UploadProps } from 'element-plus'
import { Plus, Delete, EditPen } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { reqUploadImage } from '@/api/data'
import useImageStore from '@/store/modules/image'
import { annotationStorage } from '@/utils/annotationStorage'

// 定义组件名称
defineOptions({
  name: 'ImageUploader',
})

// Props 定义
interface Props {
  modelValue?: File | null
  imagePreview?: string
  fileName?: string
  accept?: string
  maxSize?: number // MB
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  imagePreview: '',
  fileName: '',
  accept: '.jpg,.jpeg,.png,.dcm,.dicom,.avi',
  maxSize: 50,
  disabled: false,
})

// Emits 定义
interface Emits {
  'update:modelValue': [file: File | null]
  'update:imagePreview': [preview: string]
  'update:fileName': [name: string]
  'upload-success': [file: File, result: any]
  'upload-error': [error: Error]
}

const emit = defineEmits<Emits>()

// 路由和状态管理
const router = useRouter()
const imageStore = useImageStore()

// 响应式数据
const uploadRef = ref()

// 上传状态
const uploading = ref(false)

// 裁剪相关状态
const showCropper = ref(false)
const originalImageUrl = ref('')
const originalFileName = ref('')
const pendingFile = ref<File | null>(null)

// 裁剪后的图像数据
const croppedImageData = ref<any>(null)

// 标注数据状态
const hasAnnotationData = ref(false)

// 计算属性：是否显示标注按钮
const showAnnotationButton = computed(() => {
  // 如果有图片预览，检查是否有原始图像数据可用于标注
  if (!props.imagePreview) return false

  // 检查是否有 croppedImageData 或者状态管理中有原始图像数据
  return !!(croppedImageData.value || imageStore.hasOriginalCroppedImage())
})

// 检查标注数据状态
const checkAnnotationDataStatus = (imageId: string) => {
  if (!imageId) {
    hasAnnotationData.value = false
    return
  }

  try {
    const exists = annotationStorage.exists(imageId)
    hasAnnotationData.value = exists
  } catch (error) {
    console.error('❌ 检查标注数据状态失败:', error)
    hasAnnotationData.value = false
  }
}

// 文件上传前的检查
const beforeUpload: UploadProps['beforeUpload'] = async (file) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'video/avi']
  const allowedExtensions = ['.dcm', '.dicom']

  const isValidType =
    allowedTypes.includes(file.type) || allowedExtensions.some((ext) => file.name.toLowerCase().endsWith(ext))

  if (!isValidType) {
    ElMessage.error('请上传支持的文件格式：JPG, PNG, DICOM, AVI')
    return false
  }

  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过${props.maxSize}MB`)
    return false
  }

  // 如果是图片文件，启动裁剪流程
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      originalImageUrl.value = e.target?.result as string
      originalFileName.value = file.name
      pendingFile.value = file
      showCropper.value = true
    }
    reader.readAsDataURL(file)
  } else {
    // 非图片文件直接处理
    emit('update:modelValue', file)
    emit('update:fileName', file.name)

    // 开始上传文件到服务器
    try {
      uploading.value = true
      const imageId = await reqUploadImage(file)
      emit('upload-success', file, { imageId })
    } catch (error) {
      emit('upload-error', error as Error)
    } finally {
      uploading.value = false
    }
  }

  return false // 阻止Element Plus的自动上传
}

// 上传成功回调（已在beforeUpload中处理，这里保留用于兼容）
const handleUploadSuccess = () => {
  ElMessage.success('文件上传成功')
}

// 上传失败回调
const handleUploadError = (error: Error) => {
  ElMessage.error('文件上传失败')
  emit('upload-error', error)
}

// 删除图像
const removeImage = () => {
  emit('update:modelValue', null)
  emit('update:fileName', '')
  emit('update:imagePreview', '')
}

// 裁剪确认处理
const handleCropConfirm = async (croppedFile: File, croppedDataUrl: string) => {
  try {
    // 更新组件状态
    emit('update:modelValue', croppedFile)
    emit('update:fileName', croppedFile.name)
    emit('update:imagePreview', croppedDataUrl)

    // 开始上传裁剪后的文件到服务器
    uploading.value = true
    const response = await reqUploadImage(croppedFile)
    const imageId = response
    emit('upload-success', croppedFile, { imageId })

    // 保存裁剪后的图像数据，用于标注
    croppedImageData.value = {
      id: imageId || Date.now() + Math.random(),
      name: croppedFile.name,
      src: croppedDataUrl,
      size: croppedFile.size,
      type: croppedFile.type,
      originalWidth: 720,
      originalHeight: 720,
      uploadTime: new Date().toLocaleString(),
      file: croppedFile,
    }

    // 立即保存原始图像数据到状态管理，用于标注
    imageStore.setOriginalCroppedImage(croppedImageData.value)

    // 检查是否有该图像的标注数据
    if (imageId) {
      checkAnnotationDataStatus(String(imageId))
    }

    ElMessage.success('图像裁剪并上传成功！')
  } catch (error) {
    emit('upload-error', error as Error)
    ElMessage.error('图像上传失败，请重试')
  } finally {
    uploading.value = false
    // 清理临时数据
    originalImageUrl.value = ''
    originalFileName.value = ''
    pendingFile.value = null
  }
}

// 处理图像标注
const handleAnnotateImage = () => {
  // 首先尝试恢复状态
  if (!croppedImageData.value) {
    restoreImageState()
  }

  // 检查是否有可用的原始图像数据
  if (!croppedImageData.value && !imageStore.hasOriginalCroppedImage()) {
    ElMessage.warning('没有可用的图像数据，请重新上传图片')
    return
  }

  // 确保状态管理中有原始图像数据
  if (croppedImageData.value && !imageStore.hasOriginalCroppedImage()) {
    imageStore.setOriginalCroppedImage(croppedImageData.value)
  }

  // 导航到图像分析页面
  router.push('/analysis/main')

  ElMessage.success('正在前往图像标注页面...')
}

// 状态恢复逻辑
const restoreImageState = () => {
  // 如果已经有图片预览，检查是否需要恢复 croppedImageData
  if (props.imagePreview && !croppedImageData.value) {
    // 从状态管理获取原始图像数据
    const storedImage = imageStore.getOriginalCroppedImage()
    if (storedImage) {
      croppedImageData.value = storedImage

      // 检查标注数据状态
      if (storedImage.id) {
        checkAnnotationDataStatus(String(storedImage.id))
      }
      return
    }

    // 如果没有找到原始图像数据，检查是否是标注后的图片
    const annotatedImage = imageStore.getLatestAnnotatedImage()
    if (annotatedImage && annotatedImage.previewUrl === props.imagePreview) {
      // 通过原始图像ID恢复原始图像数据
      const originalImage = imageStore.getOriginalCroppedImage()
      if (originalImage && String(originalImage.id) === annotatedImage.originalImageId) {
        croppedImageData.value = originalImage

        // 检查标注数据状态
        checkAnnotationDataStatus(annotatedImage.originalImageId)
      }
    }
  }
}

// 监听 imagePreview 变化，当外部设置图片预览时恢复状态
watch(
  () => props.imagePreview,
  (newPreview) => {
    if (newPreview && !croppedImageData.value) {
      restoreImageState()
    }
  },
  { immediate: true },
)

// 组件挂载时检查状态
onMounted(() => {
  restoreImageState()
})

// 裁剪取消处理
const handleCropCancel = () => {
  // 清理临时数据
  originalImageUrl.value = ''
  originalFileName.value = ''
  pendingFile.value = null
  ElMessage.info('已取消图像裁剪')
}
</script>

<style scoped>
.image-uploader {
  width: 720px;
  height: 720px;
}

/* 去掉Element Plus上传组件的默认边框 */
.image-uploader :deep(.el-upload) {
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
}

.image-uploader :deep(.el-upload-dragger) {
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  padding: 0 !important;
  width: 100% !important;
  height: auto !important;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 720px;
  height: 720px;
  background: var(--bg-primary);
  border: 2px dashed var(--border-primary);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.upload-placeholder:hover {
  background: var(--bg-hover);
  border-color: var(--medical-blue);
}

.upload-icon {
  font-size: 3rem;
  color: var(--text-tertiary);
  margin-bottom: 12px;
}

.upload-text {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.image-preview {
  position: relative;
  width: 720px;
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-primary);
  border-radius: 8px;
  overflow: hidden;
  cursor: default;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.annotation-btn {
  background: #409eff;
  border-color: #409eff;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.annotation-btn:hover {
  transform: translateY(-2px);
  background: #66b1ff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.delete-btn {
  background: #f56c6c;
  border-color: #f56c6c;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.delete-btn:hover {
  transform: translateY(-2px);
  background: #f78989;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.delete-icon:hover {
  transform: scale(1.1);
}
</style>
