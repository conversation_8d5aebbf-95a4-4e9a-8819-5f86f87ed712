// 首页相关类型定义

// 统计卡片数据
export interface StatCardData {
  title: string
  value: number | string
  icon: string
  iconType?: 'svg' | 'element'  // 图标类型：svg 或 element-plus
  color: string
  background?: string  // 可选的背景样式
  trend?: {
    value: number
    isUp: boolean
  }
}

// 图表数据
export interface ChartData {
  name: string
  value: number
  color?: string
}

// 分类分布数据
export interface ClassificationData {
  benign: number      // 良性
  malignant: number   // 恶性
  normal: number      // 正常
  undetermined: number // 未定
  other: number       // 其他
}

// 趋势数据
export interface TrendData {
  date: string
  count: number
}

// 最新数据项
export interface RecentDataItem {
  id: string
  classification: string
  detail: string
  createTime: string
  hasImage: boolean
}

// 首页统计概览
export interface DashboardStats {
  totalData: number
  todayNew: number
  pendingProcess: number
  modelAccuracy: number
}

// API响应数据模拟
export interface MockApiResponse<T> {
  code: number
  message: string
  data: T
}
