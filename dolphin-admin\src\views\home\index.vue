<template>
  <div class="dashboard-container">
    <!-- 用户欢迎区域 -->
    <UserWelcome />

    <!-- 顶部统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col v-for="(card, index) in statCards" :key="index" :xs="24" :sm="12" :md="6">
          <StatCard :data="card" />
        </el-col>
      </el-row>
    </div>

    <!-- 中间图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <ClassificationChart :data="classificationChartData" :loading="loading" @refresh="refreshData" />
        </el-col>
        <el-col :xs="24" :lg="12">
          <HotSearchCloud @refresh="refreshData" />
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图表区域 -->
    <div class="trend-section">
      <el-row :gutter="20">
        <el-col :xs="24">
          <TrendChart :data="trendData" :loading="loading" @refresh="refreshData" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import { UserWelcome, StatCard, ClassificationChart, TrendChart, HotSearchCloud } from './components'
import { useDashboardData } from './composables/useDashboardData'

// 使用数据管理 composable
const { loading, trendData, getStatCards, getClassificationChartData, refreshData } = useDashboardData()

// 计算属性
const statCards = computed(() => getStatCards())
const classificationChartData = computed(() => getClassificationChartData())
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 0;
  background: transparent;
  min-height: auto;
}

.stats-section {
  margin-bottom: 20px;
}

.charts-section {
  margin-bottom: 20px;
}

.trend-section {
  margin-bottom: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0;
  }

  .stats-section,
  .charts-section,
  .trend-section {
    margin-bottom: 15px;
  }
}
</style>
