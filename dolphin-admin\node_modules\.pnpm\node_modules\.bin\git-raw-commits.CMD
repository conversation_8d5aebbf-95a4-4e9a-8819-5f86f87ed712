@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\git-raw-commits@4.0.0\node_modules\git-raw-commits\node_modules;D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\git-raw-commits@4.0.0\node_modules;D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\git-raw-commits@4.0.0\node_modules\git-raw-commits\node_modules;D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\git-raw-commits@4.0.0\node_modules;D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\git-raw-commits\cli.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\git-raw-commits\cli.mjs" %*
)
