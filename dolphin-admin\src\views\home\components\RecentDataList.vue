<template>
  <el-card class="recent-data-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">最新数据</span>
        <el-button link size="small" @click="refreshData">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    
    <div v-loading="loading" class="data-list">
      <div 
        v-for="item in data" 
        :key="item.id" 
        class="data-item"
        @click="viewDetail(item)"
      >
        <div class="item-content">
          <div class="item-header">
            <el-tag 
              :type="getClassificationTagType(item.classification)" 
              size="small"
            >
              {{ getClassificationText(item.classification) }}
            </el-tag>
            <div class="item-time">
              {{ formatTime(item.createTime) }}
            </div>
          </div>
          <div class="item-detail">
            {{ item.detail }}
          </div>
          <div class="item-footer">
            <div class="item-id">ID: {{ item.id.slice(-8) }}</div>
            <el-icon v-if="item.hasImage" class="image-icon">
              <Picture />
            </el-icon>
          </div>
        </div>
      </div>
      
      <div v-if="!data.length && !loading" class="empty-state">
        <el-empty description="暂无数据" :image-size="80" />
      </div>
    </div>
    
    <div class="card-footer">
      <el-button type="primary" link @click="viewAll">
        查看全部
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { Refresh, Picture, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { RecentDataItem } from '../types'

interface Props {
  data: RecentDataItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  refresh: []
  viewDetail: [item: RecentDataItem]
  viewAll: []
}>()

const getClassificationTagType = (classification: string) => {
  const typeMap: Record<string, string> = {
    'benign': 'success',
    'malignant': 'danger',
    'normal': 'info',
    'undetermined': 'warning',
    'other': 'info'
  }
  return typeMap[classification] || 'info'
}

const getClassificationText = (classification: string) => {
  const textMap: Record<string, string> = {
    'benign': '良性',
    'malignant': '恶性',
    'normal': '正常',
    'undetermined': '未定',
    'other': '其他'
  }
  return textMap[classification] || classification
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const refreshData = () => {
  emit('refresh')
}

const viewDetail = (item: RecentDataItem) => {
  emit('viewDetail', item)
  ElMessage.info(`查看详情: ${item.id}`)
}

const viewAll = () => {
  emit('viewAll')
  ElMessage.info('跳转到数据管理页面')
}
</script>

<style scoped lang="scss">
.recent-data-card {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.data-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 4px;
}

.data-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409EFF;
    background-color: #F5F7FA;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.item-content {
  width: 100%;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-time {
  font-size: 12px;
  color: #909399;
}

.item-detail {
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-id {
  font-size: 12px;
  color: #C0C4CC;
  font-family: monospace;
}

.image-icon {
  color: #409EFF;
  font-size: 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.card-footer {
  padding: 16px 0 0 0;
  border-top: 1px solid #EBEEF5;
  text-align: center;
}
</style>
