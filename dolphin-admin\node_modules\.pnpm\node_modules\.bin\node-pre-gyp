#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@mapbox/node-pre-gyp/bin/node-pre-gyp" "$@"
else
  exec node  "$basedir/../@mapbox/node-pre-gyp/bin/node-pre-gyp" "$@"
fi
