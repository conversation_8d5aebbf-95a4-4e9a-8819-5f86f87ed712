import { ref } from 'vue'
import useImageStore from '@/store/modules/image'

export function useAnnotationLogic() {
  // 获取状态管理实例
  const imageStore = useImageStore()

  // 响应式数据
  const annotations = ref([]) // 已保存的标注
  const tempAnnotations = ref([]) // 临时标注（未保存）
  const selectedAnnotation = ref(null)

  // 添加临时标注
  const addTempAnnotation = (annotation) => {
    // 转换标注数据格式，确保坐标正确
    const annotationPoint = {
      id: annotation.id,
      index: tempAnnotations.value.length,
      x: annotation.coordinates?.x || annotation.x || 0,
      y: annotation.coordinates?.y || annotation.y || 0,
      originalAnnotation: annotation,
      fabricX: annotation.coordinates?.x || annotation.x || 0,
      fabricY: annotation.coordinates?.y || annotation.y || 0,
    }

    tempAnnotations.value.push(annotation)
    // 同步到状态管理（使用转换后的格式）
    imageStore.addCurrentAnnotation(annotationPoint)
  }

  // 保存标注
  const saveAnnotations = (annotationData) => {
    // 将转换后的标注添加到已保存的标注列表
    annotations.value.push(...annotationData)

    // 清空临时标注
    tempAnnotations.value = []
  }

  // 撤销上次标注
  const undoLastAnnotation = () => {
    if (tempAnnotations.value.length === 0) return null

    // 获取最后一个标注
    const lastAnnotation = tempAnnotations.value.pop()

    // 同步到状态管理
    imageStore.undoLastCurrentAnnotation()

    return lastAnnotation
  }

  // 清空所有标注
  const clearAnnotations = () => {
    annotations.value = []
    tempAnnotations.value = []
    selectedAnnotation.value = null
    // 同步到状态管理
    imageStore.clearCurrentAnnotations()
  }

  // 清空临时标注
  const clearTempAnnotations = () => {
    tempAnnotations.value = []
  }

  // 选择标注
  const selectAnnotation = (annotation) => {
    selectedAnnotation.value = annotation
  }

  // 取消选择
  const clearSelection = () => {
    selectedAnnotation.value = null
  }

  // 删除指定标注
  const removeAnnotation = (annotationId) => {
    const index = annotations.value.findIndex((a) => a.id === annotationId)
    if (index !== -1) {
      const removed = annotations.value.splice(index, 1)[0]

      return removed
    }
    return null
  }

  // 更新标注
  const updateAnnotation = (annotationId, updates) => {
    const index = annotations.value.findIndex((a) => a.id === annotationId)
    if (index !== -1) {
      annotations.value[index] = { ...annotations.value[index], ...updates }

      return annotations.value[index]
    }
    return null
  }

  // 获取标注统计信息
  const getAnnotationStats = () => {
    return {
      totalAnnotations: annotations.value.length,
      tempAnnotations: tempAnnotations.value.length,
      hasSelection: selectedAnnotation.value !== null,
    }
  }

  return {
    // 响应式数据
    annotations,
    tempAnnotations,
    selectedAnnotation,

    // 方法
    addTempAnnotation,
    saveAnnotations,
    undoLastAnnotation,
    clearAnnotations,
    clearTempAnnotations,
    selectAnnotation,
    clearSelection,
    removeAnnotation,
    updateAnnotation,
    getAnnotationStats,
  }
}
