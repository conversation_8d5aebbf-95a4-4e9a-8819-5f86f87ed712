<template>
  <div class="action-section">
    <el-button
      type="primary"
      size="large"
      :loading="isSubmitting"
      :disabled="!canSubmit"
      @click="handleSubmit"
    >
      <el-icon><Check /></el-icon>
      提交评分
    </el-button>

    <el-button
      size="large"
      @click="handleReset"
    >
      <el-icon><RefreshRight /></el-icon>
      重置表单
    </el-button>

    <el-button
      type="info"
      size="large"
      @click="handlePreview"
    >
      <el-icon><View /></el-icon>
      预览结果
    </el-button>
  </div>
</template>

<script setup lang="ts">
import {
  Check,
  RefreshRight,
  View
} from '@element-plus/icons-vue'

// Props
interface Props {
  canSubmit: boolean
  isSubmitting: boolean
}

defineProps<Props>()

// Events
interface Emits {
  (e: 'submit'): void
  (e: 'reset'): void
  (e: 'preview'): void
}

const emit = defineEmits<Emits>()

// 方法
const handleSubmit = () => {
  emit('submit')
}

const handleReset = () => {
  emit('reset')
}

const handlePreview = () => {
  emit('preview')
}
</script>

<style scoped>
@import '../styles/medical-theme.scss';

/* 操作按钮区域 - 水平布局 */
.action-section {
  display: flex;
  flex-direction: row;
  gap: 12px;
  padding: 20px;
  background: var(--medical-card);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-green);
}

:deep(.el-button) {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 0;
}

/* 主要按钮（提交）- 更突出 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--medical-blue), #0984e3);
  border: none;
  box-shadow: 0 4px 12px rgba(46, 124, 230, 0.3);
  flex: 1.2; /* 稍微宽一点 */
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #0984e3, var(--medical-blue));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 124, 230, 0.4);
}

:deep(.el-button--primary:disabled) {
  background: #C0C4CC;
  transform: none;
  box-shadow: none;
}

/* 辅助按钮（重置） */
:deep(.el-button--default) {
  background: var(--medical-card);
  border: 2px solid #E4E7ED;
  color: var(--medical-text-dark);
}

:deep(.el-button--default:hover) {
  border-color: var(--medical-blue);
  color: var(--medical-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 124, 230, 0.15);
}

/* 预览按钮 */
:deep(.el-button--info) {
  background:  #f08b72;
  border: none;

  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
}



/* 按钮图标 */
:deep(.el-button .el-icon) {
  margin-right: 6px;
  font-size: 16px;
}

/* 加载状态 */
:deep(.el-button.is-loading) {
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    padding: 16px;
    gap: 10px;
  }

  :deep(.el-button) {
    flex: none;
    height: 40px;
    font-size: 13px;
  }

  :deep(.el-button--primary) {
    flex: none;
  }

  :deep(.el-button .el-icon) {
    margin-right: 4px;
    font-size: 14px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .action-section {
    padding: 12px;
    gap: 8px;
  }

  :deep(.el-button) {
    height: 36px;
    font-size: 12px;
    padding: 0 12px;
  }

  :deep(.el-button .el-icon) {
    margin-right: 3px;
    font-size: 13px;
  }
}
</style>
