export function useCoordinateTransform(canvasHeight = 720) {
  // 将Fabric.js坐标（左上角原点）转换为显示坐标（左下角原点）
  const convertToDisplayCoordinates = (annotation) => {
    // 获取标注的坐标信息
    let x, y

    // 从 coordinates 对象中获取坐标
    if (annotation.coordinates) {
      x = annotation.coordinates.x
      y = annotation.coordinates.y
    } else if (annotation.left !== undefined && annotation.top !== undefined) {
      x = annotation.left
      y = annotation.top
    } else if (annotation.x !== undefined && annotation.y !== undefined) {
      x = annotation.x
      y = annotation.y
    } else {
      console.error('无法获取标注坐标:', annotation)
      x = 0
      y = 0
    }

    // Fabric.js 使用左上角为原点，需要转换为左下角为原点
    const convertedY = canvasHeight - y

    return {
      x: x,
      y: convertedY,
      // 保留原始Fabric.js坐标用于绘制区域
      fabricX: x,
      fabricY: y,
    }
  }

  // 将显示坐标（左下角原点）转换为Fabric.js坐标（左上角原点）
  const convertToCanvasCoordinates = (displayX, displayY) => {
    const fabricY = canvasHeight - displayY

    return {
      x: displayX,
      y: fabricY,
    }
  }

  // 批量转换坐标
  const convertMultipleToDisplayCoordinates = (annotations) => {
    return annotations.map((annotation, index) => {
      const coords = convertToDisplayCoordinates(annotation)

      return {
        id: annotation.id || `point_${index + 1}`,
        index: index + 1,
        x: Math.round(coords.x),
        y: Math.round(coords.y),
        originalAnnotation: annotation,
        fabricX: coords.fabricX,
        fabricY: coords.fabricY,
      }
    })
  }

  // 验证坐标是否在画布范围内
  const isCoordinateValid = (x, y, canvasWidth = 720) => {
    return x >= 0 && x <= canvasWidth && y >= 0 && y <= canvasHeight
  }

  // 限制坐标在画布范围内
  const clampCoordinate = (x, y, canvasWidth = 720) => {
    return {
      x: Math.max(0, Math.min(canvasWidth, x)),
      y: Math.max(0, Math.min(canvasHeight, y)),
    }
  }

  // 计算两点间距离
  const calculateDistance = (point1, point2) => {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 计算点到线段的距离
  const pointToLineDistance = (point, lineStart, lineEnd) => {
    const A = point.x - lineStart.x
    const B = point.y - lineStart.y
    const C = lineEnd.x - lineStart.x
    const D = lineEnd.y - lineStart.y

    const dot = A * C + B * D
    const lenSq = C * C + D * D

    if (lenSq === 0) return calculateDistance(point, lineStart)

    let param = dot / lenSq

    let xx, yy

    if (param < 0) {
      xx = lineStart.x
      yy = lineStart.y
    } else if (param > 1) {
      xx = lineEnd.x
      yy = lineEnd.y
    } else {
      xx = lineStart.x + param * C
      yy = lineStart.y + param * D
    }

    const dx = point.x - xx
    const dy = point.y - yy
    return Math.sqrt(dx * dx + dy * dy)
  }

  return {
    convertToDisplayCoordinates,
    convertToCanvasCoordinates,
    convertMultipleToDisplayCoordinates,
    isCoordinateValid,
    clampCoordinate,
    calculateDistance,
    pointToLineDistance,
  }
}
