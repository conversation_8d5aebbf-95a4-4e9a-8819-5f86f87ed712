hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.27.4)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.27.4)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.28.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.0(@babel/core@7.27.4)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    '@babel/preset-modules': private
  '@babel/runtime@7.22.11':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@commitlint/config-validator@19.8.1':
    '@commitlint/config-validator': private
  '@commitlint/ensure@19.8.1':
    '@commitlint/ensure': private
  '@commitlint/execute-rule@19.8.1':
    '@commitlint/execute-rule': private
  '@commitlint/format@19.8.1':
    '@commitlint/format': private
  '@commitlint/is-ignored@19.8.1':
    '@commitlint/is-ignored': private
  '@commitlint/lint@19.8.1':
    '@commitlint/lint': private
  '@commitlint/load@19.8.1(@types/node@24.0.3)(typescript@5.8.3)':
    '@commitlint/load': private
  '@commitlint/message@19.8.1':
    '@commitlint/message': private
  '@commitlint/parse@19.8.1':
    '@commitlint/parse': private
  '@commitlint/read@19.8.1':
    '@commitlint/read': private
  '@commitlint/resolve-extends@19.8.1':
    '@commitlint/resolve-extends': private
  '@commitlint/rules@19.8.1':
    '@commitlint/rules': private
  '@commitlint/to-lines@19.8.1':
    '@commitlint/to-lines': private
  '@commitlint/top-level@19.8.1':
    '@commitlint/top-level': private
  '@commitlint/types@19.8.1':
    '@commitlint/types': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@csstools/media-query-list-parser@4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/media-query-list-parser': private
  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    '@csstools/selector-specificity': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@dimforge/rapier3d-compat@0.12.0':
    '@dimforge/rapier3d-compat': private
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@keyv/serialize@1.0.3':
    '@keyv/serialize': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@msgpack/msgpack@2.8.0':
    '@msgpack/msgpack': private
  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    '@nicolo-ribaudo/eslint-scope-5-internals': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@oozcitak/dom@1.15.10':
    '@oozcitak/dom': private
  '@oozcitak/infra@1.0.8':
    '@oozcitak/infra': private
  '@oozcitak/url@1.0.4':
    '@oozcitak/url': private
  '@oozcitak/util@8.3.8':
    '@oozcitak/util': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@sindresorhus/is@0.7.0':
    '@sindresorhus/is': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@tweenjs/tween.js@23.1.3':
    '@tweenjs/tween.js': private
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/imagemin-gifsicle@7.0.4':
    '@types/imagemin-gifsicle': private
  '@types/imagemin-jpegtran@5.0.4':
    '@types/imagemin-jpegtran': private
  '@types/imagemin-mozjpeg@8.0.4':
    '@types/imagemin-mozjpeg': private
  '@types/imagemin-optipng@5.2.4':
    '@types/imagemin-optipng': private
  '@types/imagemin-svgo@10.0.5':
    '@types/imagemin-svgo': private
  '@types/imagemin-webp@7.0.3':
    '@types/imagemin-webp': private
  '@types/imagemin@7.0.1':
    '@types/imagemin': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.18':
    '@types/lodash': private
  '@types/minimatch@6.0.0':
    '@types/minimatch': private
  '@types/node@24.0.3':
    '@types/node': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/stats.js@0.17.4':
    '@types/stats.js': private
  '@types/svgo@2.6.4':
    '@types/svgo': private
  '@types/web-bluetooth@0.0.16':
    '@types/web-bluetooth': private
  '@types/webxr@0.5.22':
    '@types/webxr': private
  '@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.1':
    '@typescript-eslint/visitor-keys': private
  '@volar/language-core@2.4.14':
    '@volar/language-core': private
  '@volar/source-map@2.4.14':
    '@volar/source-map': private
  '@volar/typescript@2.4.14':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.10(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.17':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.17':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.17':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.17(vue@3.5.17(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  '@vueuse/core@9.13.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/metadata@9.13.0':
    '@vueuse/metadata': private
  '@vueuse/shared@9.13.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@webgpu/types@0.1.64':
    '@webgpu/types': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  JSONStream@1.3.5:
    JSONStream: private
  abab@2.0.6:
    abab: private
  abbrev@1.1.1:
    abbrev: private
  acorn-globals@7.0.1:
    acorn-globals: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  alien-signals@1.0.13:
    alien-signals: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  aproba@2.1.0:
    aproba: private
  arch@2.2.0:
    arch: private
  archive-type@4.0.0:
    archive-type: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  argparse@1.0.10:
    argparse: private
  argparse@2.0.1:
    argparse: private
  arr-diff@4.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-find-index@1.0.2:
    array-find-index: private
  array-ify@1.0.0:
    array-ify: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array-unique@0.3.2:
    array-unique: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assign-symbols@1.0.0:
    assign-symbols: private
  astral-regex@2.0.0:
    astral-regex: private
  async-function@1.0.0:
    async-function: private
  async-validator@4.2.5:
    async-validator: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.27.4):
    babel-plugin-polyfill-regenerator: private
  balanced-match@2.0.0:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base@0.11.2:
    base: private
  big.js@5.2.2:
    big.js: private
  bin-build@3.0.0:
    bin-build: private
  bin-check@4.1.0:
    bin-check: private
  bin-version-check@4.0.0:
    bin-version-check: private
  bin-version@3.1.0:
    bin-version: private
  bin-wrapper@4.1.0:
    bin-wrapper: private
  birpc@2.4.0:
    birpc: private
  bl@1.2.3:
    bl: private
  bluebird@3.7.2:
    bluebird: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  browserslist@4.25.1:
    browserslist: private
  buffer-alloc-unsafe@1.1.0:
    buffer-alloc-unsafe: private
  buffer-alloc@1.2.0:
    buffer-alloc: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-fill@1.0.0:
    buffer-fill: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  cache-base@1.0.1:
    cache-base: private
  cacheable-request@2.1.4:
    cacheable-request: private
  cacheable@1.10.0:
    cacheable: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-keys@2.1.0:
    camelcase-keys: private
  camelcase@2.1.1:
    camelcase: private
  caniuse-lite@1.0.30001724:
    caniuse-lite: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  canvas@2.11.2:
    canvas: private
  caw@2.0.1:
    caw: private
  chalk@4.1.2:
    chalk: private
  chokidar@4.0.3:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  class-utils@0.3.6:
    class-utils: private
  classnames@2.5.1:
    classnames: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.2:
    clone-response: private
  clone@2.1.2:
    clone: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  colord@2.9.3:
    colord: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  commander@7.2.0:
    commander: private
  commander@9.2.0:
    commander: private
  compare-func@2.0.0:
    compare-func: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  config-chain@1.1.13:
    config-chain: private
  console-control-strings@1.1.0:
    console-control-strings: private
  console-stream@0.1.1:
    console-stream: private
  content-disposition@0.5.4:
    content-disposition: private
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: private
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: private
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  core-js-compat@3.44.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig-typescript-loader@6.1.0(@types/node@24.0.3)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    cosmiconfig-typescript-loader: private
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-functions-list@3.2.3:
    css-functions-list: private
  css-select@4.3.0:
    css-select: private
  css-tree@3.1.0:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csso@4.2.0:
    csso: private
  cssom@0.5.0:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  currently-unhandled@0.4.1:
    currently-unhandled: private
  cwebp-bin@6.1.2:
    cwebp-bin: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  dargs@8.1.0:
    dargs: private
  data-urls@3.0.2:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dayjs@1.11.13:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debounce@1.2.1:
    debounce: private
  debug@3.2.7:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@4.2.1:
    decompress-response: private
  decompress-tar@4.1.1:
    decompress-tar: private
  decompress-tarbz2@4.1.1:
    decompress-tarbz2: private
  decompress-targz@4.1.1:
    decompress-targz: private
  decompress-unzip@4.0.1:
    decompress-unzip: private
  decompress@4.2.1:
    decompress: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  define-property@1.0.0:
    define-property: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  detect-libc@1.0.3:
    detect-libc: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domexception@4.0.0:
    domexception: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-prop@5.3.0:
    dot-prop: private
  download@6.2.5:
    download: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer3@0.1.5:
    duplexer3: private
  easy-bem@1.1.1:
    easy-bem: private
  electron-to-chromium@1.5.171:
    electron-to-chromium: private
  electron-to-chromium@1.5.187:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-windows-64@0.14.54:
    esbuild-windows-64: private
  esbuild@0.14.54:
    esbuild: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@9.29.0(jiti@2.4.2)):
    eslint-module-utils: private
  eslint-plugin-es@3.0.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-es: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-utils@2.1.0:
    eslint-utils: private
  eslint-visitor-keys@2.1.0:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  events@3.3.0:
    events: private
  exec-buffer@3.2.0:
    exec-buffer: private
  execa@5.1.1:
    execa: private
  executable@4.1.1:
    executable: private
  expand-brackets@2.1.4:
    expand-brackets: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extglob@2.0.4:
    extglob: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fast-xml-parser@4.5.3:
    fast-xml-parser: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fflate@0.7.3:
    fflate: private
  figures@1.7.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-type@12.4.2:
    file-type: private
  filename-reserved-regex@2.0.0:
    filename-reserved-regex: private
  filenamify@2.1.0:
    filenamify: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  find-versions@3.2.0:
    find-versions: private
  flat-cache@6.1.10:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  for-in@1.0.2:
    for-in: private
  form-data@4.0.3:
    form-data: private
  fraction.js@4.3.7:
    fraction.js: private
  fragment-cache@0.2.1:
    fragment-cache: private
  from2@2.3.0:
    from2: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@3.0.2:
    gauge: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-proxy@2.1.0:
    get-proxy: private
  get-stdin@4.0.1:
    get-stdin: private
  get-stream@4.1.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  gifsicle@5.2.0:
    gifsicle: private
  git-raw-commits@4.0.0:
    git-raw-commits: private
  gl-matrix@3.4.3:
    gl-matrix: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globalthis@1.0.3:
    globalthis: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  gopd@1.2.0:
    gopd: private
  got@7.1.0:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-ansi@2.0.0:
    has-ansi: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbol-support-x@1.4.2:
    has-symbol-support-x: private
  has-symbols@1.1.0:
    has-symbols: private
  has-to-string-tag-x@1.4.1:
    has-to-string-tag-x: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  hookified@1.9.1:
    hookified: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  html-encoding-sniffer@3.0.0:
    html-encoding-sniffer: private
  html-tags@3.3.1:
    html-tags: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-cache-semantics@3.8.1:
    http-cache-semantics: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@1.1.1:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-size@0.5.5:
    image-size: private
  imagemin-gifsicle@7.0.0:
    imagemin-gifsicle: private
  imagemin-jpegtran@7.0.0:
    imagemin-jpegtran: private
  imagemin-mozjpeg@9.0.0:
    imagemin-mozjpeg: private
  imagemin-optipng@8.0.0:
    imagemin-optipng: private
  imagemin-pngquant@9.0.2:
    imagemin-pngquant: private
  imagemin-svgo@9.0.0:
    imagemin-svgo: private
  imagemin-webp@6.1.0:
    imagemin-webp: private
  imagemin@7.0.1:
    imagemin: private
  immutable@5.1.3:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@3.1.0:
    import-lazy: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@2.1.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  interpret@1.4.0:
    interpret: private
  into-stream@3.1.0:
    into-stream: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-cwebp-readable@3.0.0:
    is-cwebp-readable: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-finite@1.1.0:
    is-finite: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-gif@3.0.0:
    is-gif: private
  is-glob@4.0.3:
    is-glob: private
  is-jpg@2.0.0:
    is-jpg: private
  is-map@2.0.3:
    is-map: private
  is-natural-number@4.0.1:
    is-natural-number: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-object@1.0.2:
    is-object: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-png@2.0.0:
    is-png: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-regex@1.2.1:
    is-regex: private
  is-retry-allowed@1.2.0:
    is-retry-allowed: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-svg@4.4.0:
    is-svg: private
  is-symbol@1.1.1:
    is-symbol: private
  is-text-path@2.0.0:
    is-text-path: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-utf8@0.2.1:
    is-utf8: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  isurl@1.0.0:
    isurl: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  jpegtran-bin@6.0.1:
    jpegtran-bin: private
  js-base64@2.6.4:
    js-base64: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdom@20.0.3(canvas@2.11.2):
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonparse@1.3.1:
    jsonparse: private
  junk@3.1.0:
    junk: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  known-css-properties@0.36.0:
    known-css-properties: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-json-file@1.1.0:
    load-json-file: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@1.4.2:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.snakecase@4.1.1:
    lodash.snakecase: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: private
  lodash@4.17.21:
    lodash: private
  logalot@2.1.0:
    logalot: private
  longest@1.0.1:
    longest: private
  loud-rejection@1.6.0:
    loud-rejection: private
  lowercase-keys@1.0.1:
    lowercase-keys: private
  lpad-align@1.1.2:
    lpad-align: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  map-cache@0.2.2:
    map-cache: private
  map-obj@1.0.1:
    map-obj: private
  map-visit@1.0.0:
    map-visit: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  mdn-data@2.21.0:
    mdn-data: private
  memoize-one@6.0.0:
    memoize-one: private
  meow@13.2.0:
    meow: private
  merge-options@1.0.1:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  meshoptimizer@0.18.1:
    meshoptimizer: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@2.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mkdirp@1.0.4:
    mkdirp: private
  mozjpeg@7.1.1:
    mozjpeg: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  nan@2.23.0:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  nanomatch@1.2.13:
    nanomatch: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  nice-try@1.0.5:
    nice-try: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@2.0.1:
    normalize-url: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  npm-conf@1.1.3:
    npm-conf: private
  npm-run-path@2.0.2:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  nth-check@2.1.1:
    nth-check: private
  nwsapi@2.2.20:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.7:
    object.assign: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.pick@1.3.0:
    object.pick: private
  object.values@1.2.1:
    object.values: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  optipng-bin@7.0.1:
    optipng-bin: private
  os-filter-obj@2.0.0:
    os-filter-obj: private
  ow@0.17.0:
    ow: private
  own-keys@1.0.1:
    own-keys: private
  p-cancelable@0.3.0:
    p-cancelable: private
  p-event@1.3.0:
    p-event: private
  p-finally@1.0.0:
    p-finally: private
  p-is-promise@1.1.0:
    p-is-promise: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map-series@1.0.0:
    p-map-series: private
  p-pipe@3.1.0:
    p-pipe: private
  p-reduce@1.0.0:
    p-reduce: private
  p-timeout@1.2.1:
    p-timeout: private
  pako@1.0.11:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  pascalcase@0.1.1:
    pascalcase: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-type@4.0.0:
    path-type: private
  pathe@0.2.0:
    pathe: private
  pend@1.2.0:
    pend: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pify@4.0.1:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  pngquant-bin@6.0.1:
    pngquant-bin: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: private
  postcss-prefix-selector@1.16.1(postcss@5.2.18):
    postcss-prefix-selector: private
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: private
  postcss-safe-parser@6.0.0(postcss@8.5.6):
    postcss-safe-parser: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-sorting@9.1.0(postcss@8.5.6):
    postcss-sorting: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  posthtml-parser@0.2.1:
    posthtml-parser: private
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: private
  posthtml-render@1.4.0:
    posthtml-render: private
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: private
  posthtml@0.9.2:
    posthtml: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prepend-http@1.0.4:
    prepend-http: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  proto-list@1.2.4:
    proto-list: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pseudomap@1.0.2:
    pseudomap: private
  psl@1.15.0:
    psl: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  query-string@4.3.4:
    query-string: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  read-pkg-up@1.0.1:
    read-pkg-up: private
  read-pkg@1.1.0:
    read-pkg: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  rechoir@0.6.2:
    rechoir: private
  redent@1.0.0:
    redent: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regex-not@1.0.2:
    regex-not: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpp@3.2.0:
    regexpp: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  repeating@2.0.1:
    repeating: private
  replace-ext@1.0.1:
    replace-ext: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve@1.22.10:
    resolve: private
  responselike@1.0.2:
    responselike: private
  ret@0.1.15:
    ret: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.44.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  schema-utils@4.3.2:
    schema-utils: private
  seedrandom@3.0.5:
    seedrandom: private
  seek-bzip@1.0.6:
    seek-bzip: private
  semver-regex@2.0.0:
    semver-regex: private
  semver-truncate@1.1.2:
    semver-truncate: private
  semver@6.3.1:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  set-value@2.0.1:
    set-value: private
  shader-loader@1.3.1:
    shader-loader: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shelljs@0.8.5:
    shelljs: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@3.1.1:
    simple-get: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2:
    snapdragon: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.6.1:
    source-map: private
  spark-md5@3.0.2:
    spark-md5: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  speakingurl@14.0.1:
    speakingurl: private
  split-string@3.1.0:
    split-string: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  squeak@1.3.0:
    squeak: private
  stable@0.1.8:
    stable: private
  static-extend@0.1.2:
    static-extend: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-browserify@3.0.0:
    stream-browserify: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-width@4.2.3:
    string-width: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-dirs@2.1.0:
    strip-dirs: private
  strip-eof@1.0.0:
    strip-eof: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@1.0.1:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-outer@1.0.1:
    strip-outer: private
  strnum@1.1.2:
    strnum: private
  stylelint-config-html@1.1.0(postcss-html@1.8.0)(stylelint@16.21.0(typescript@5.8.3)):
    stylelint-config-html: private
  stylelint-config-recommended-vue@1.6.0(postcss-html@1.8.0)(stylelint@16.21.0(typescript@5.8.3)):
    stylelint-config-recommended-vue: private
  stylelint-config-recommended@16.0.0(stylelint@16.21.0(typescript@5.8.3)):
    stylelint-config-recommended: private
  superjson@2.2.2:
    superjson: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-baker@1.7.0:
    svg-baker: private
  svg-tags@1.0.0:
    svg-tags: private
  svgo@2.8.0:
    svgo: private
  symbol-tree@3.2.4:
    symbol-tree: private
  table@6.9.0:
    table: private
  tapable@2.2.2:
    tapable: private
  tar-stream@1.6.2:
    tar-stream: private
  tar@6.2.1:
    tar: private
  temp-dir@1.0.0:
    temp-dir: private
  tempfile@2.0.0:
    tempfile: private
  terser-webpack-plugin@5.3.14(webpack@5.100.2):
    terser-webpack-plugin: private
  text-extensions@2.4.0:
    text-extensions: private
  through@2.3.8:
    through: private
  timed-out@4.0.1:
    timed-out: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-buffer@1.2.1:
    to-buffer: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@3.0.0:
    tr46: private
  traverse@0.6.11:
    traverse: private
  trim-newlines@1.0.0:
    trim-newlines: private
  trim-repeated@1.0.0:
    trim-repeated: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.3.0:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.11.0:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray.prototype.slice@1.0.5:
    typedarray.prototype.slice: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undici-types@7.8.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  union-value@1.0.1:
    union-value: private
  universalify@2.0.1:
    universalify: private
  unset-value@1.0.0:
    unset-value: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  urix@0.1.0:
    urix: private
  url-parse-lax@1.0.0:
    url-parse-lax: private
  url-parse@1.5.10:
    url-parse: private
  url-to-options@1.0.1:
    url-to-options: private
  use@3.1.1:
    use: private
  utif@3.1.0:
    utif: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@3.4.0:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vary@1.1.2:
    vary: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.17(typescript@5.8.3)):
    vue-demi: private
  vue-eslint-parser@10.1.3(eslint@9.29.0(jiti@2.4.2)):
    vue-eslint-parser: private
  w3c-xmlserializer@4.0.0:
    w3c-xmlserializer: private
  watchpack@2.4.4:
    watchpack: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.100.2:
    webpack: private
  webworker-promise@0.5.0:
    webworker-promise: private
  whatwg-encoding@2.0.0:
    whatwg-encoding: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@11.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wide-align@1.1.5:
    wide-align: private
  word-wrap@1.2.5:
    word-wrap: private
  worker-loader@3.0.8(webpack@5.100.2):
    worker-loader: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  wslink@2.3.4:
    wslink: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xmlbuilder2@3.0.2:
    xmlbuilder2: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zrender@5.6.1:
    zrender: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 29 Jul 2025 07:52:02 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.14.54'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - esbuild-android-64@0.14.54
  - esbuild-android-arm64@0.14.54
  - esbuild-darwin-64@0.14.54
  - esbuild-darwin-arm64@0.14.54
  - esbuild-freebsd-64@0.14.54
  - esbuild-freebsd-arm64@0.14.54
  - esbuild-linux-32@0.14.54
  - esbuild-linux-64@0.14.54
  - esbuild-linux-arm64@0.14.54
  - esbuild-linux-arm@0.14.54
  - esbuild-linux-mips64le@0.14.54
  - esbuild-linux-ppc64le@0.14.54
  - esbuild-linux-riscv64@0.14.54
  - esbuild-linux-s390x@0.14.54
  - esbuild-netbsd-64@0.14.54
  - esbuild-openbsd-64@0.14.54
  - esbuild-sunos-64@0.14.54
  - esbuild-windows-32@0.14.54
  - esbuild-windows-arm64@0.14.54
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Dolphin_AI\dolphin-ai\dolphin-admin\node_modules\.pnpm
virtualStoreDirMaxLength: 60
