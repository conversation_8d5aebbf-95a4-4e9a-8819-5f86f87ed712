<template>
  <div v-if="hasAnyScore" class="score-preview-card">
    <!-- 总分显示 -->
    <div class="overall-score-section">
      <div class="overall-score-circle" :class="scoreLevel.text">
        <div class="score-number">{{ overallScore.toFixed(1) }}</div>
        <div class="score-label">总分</div>
      </div>
      <div class="score-level" :style="{ color: scoreLevel.color }">
        <el-icon><TrendCharts /></el-icon>
        {{ scoreLevel.text }}
      </div>
    </div>

    <!-- 评分图表 -->
    <div class="score-chart">
      <div class="chart-header">
        <h3>各维度评分</h3>
      </div>
      <div v-for="dimension in dimensions" :key="dimension.id" class="score-item" :class="{ 'zero-score': !scores[dimension.id] }">
        <div class="score-info">
          <span class="score-label">{{ dimension.name }}</span>
          <span class="score-value" :class="{ 'zero-value': !scores[dimension.id] }">
            {{ scores[dimension.id] || 0 }}/5
            <span v-if="!scores[dimension.id]" class="zero-indicator">未评分</span>
          </span>
        </div>
        <div class="score-bar">
          <div
            class="score-fill"
            :style="{
              width: `${scores[dimension.id] ? (scores[dimension.id] / 5) * 100 : 4}%`,
              backgroundColor: getScoreColor(scores[dimension.id])
            }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 评分统计 -->
    <div class="score-stats">
      <div class="stat-item">
        <div class="stat-value">{{ completedCount }}</div>
        <div class="stat-label">已评分</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ averageScore.toFixed(1) }}</div>
        <div class="stat-label">平均分</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ highScoreCount }}</div>
        <div class="stat-label">优秀项</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { TrendCharts } from '@element-plus/icons-vue'
import type { ScoringDimension, ScoreData, ScoreLevel } from '../types'

// Props
interface Props {
  dimensions: ScoringDimension[]
  scores: ScoreData
  overallScore: number
  scoreLevel: ScoreLevel
}

const props = defineProps<Props>()

// 计算属性
const hasAnyScore = computed(() => {
  return Object.values(props.scores).some(score => score > 0)
})

const completedCount = computed(() => {
  return Object.values(props.scores).filter(score => score > 0).length
})

const averageScore = computed(() => {
  const validScores = Object.values(props.scores).filter(score => score > 0)
  if (validScores.length === 0) return 0
  return validScores.reduce((sum, score) => sum + score, 0) / validScores.length
})

const highScoreCount = computed(() => {
  return Object.values(props.scores).filter(score => score >= 4).length
})

// 方法
const getScoreColor = (score: number) => {
  if (score >= 4.5) return '#00D4AA'
  if (score >= 4) return '#2E7CE6'
  if (score >= 3) return '#FF8C00'
  if (score >= 2) return '#FF4757'
  if (score >= 1) return '#FF4757'
  return '#BDC3C7' // 0分时使用更明显的灰色
}
</script>

<style scoped>
@import '../styles/medical-theme.scss';

/* 评分结果预览 */
.score-preview-card {
  background: var(--medical-card);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-blue);
}

/* 总分显示区域 */
.overall-score-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #89bdee;
  border-radius: 12px;
  border: 1px solid #E4E7ED;
}

.overall-score-circle {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 16px;
  position: relative;
  transition: all 0.3s ease;
  border: 4px solid rgba(255, 255, 255, 0.3);
}

/* 根据等级设置不同颜色 */
.overall-score-circle.优秀 {
  background: linear-gradient(135deg, var(--medical-green), #00B894);
  box-shadow: 0 12px 32px rgba(0, 212, 170, 0.4), 0 4px 16px rgba(0, 212, 170, 0.2);
}

.overall-score-circle.良好 {
  background: linear-gradient(135deg, var(--medical-blue), #0984e3);
  box-shadow: 0 12px 32px rgba(46, 124, 230, 0.4), 0 4px 16px rgba(46, 124, 230, 0.2);
}

.overall-score-circle.合格 {
  background: linear-gradient(135deg, var(--medical-orange), #e17055);
  box-shadow: 0 12px 32px rgba(255, 140, 0, 0.4), 0 4px 16px rgba(255, 140, 0, 0.2);
}

.overall-score-circle.需改进,
.overall-score-circle.不合格 {
  background: linear-gradient(135deg, var(--medical-red), #d63031);
  box-shadow: 0 12px 32px rgba(255, 71, 87, 0.5), 0 4px 16px rgba(255, 71, 87, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.score-number {
  font-size: 42px;
  font-weight: 600;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.overall-score-circle .score-label {
  font-size: 16px;
  opacity: 0.95;
  margin-top: 6px;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.score-level {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* 评分图表 */
.score-chart {
  margin-bottom: 24px;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.score-item {
  margin-bottom: 20px;
  padding: 12px;
  background: #FAFBFC;
  border-radius: 8px;
  border: 1px solid #E4E7ED;
  transition: all 0.3s ease;
}

.score-item:hover {
  background: #F5F7FA;
  border-color: var(--medical-blue);
}

.score-item.zero-score {
  background: #F8F9FA;
  border-color: #BDC3C7;
  opacity: 0.8;
}

.score-item.zero-score:hover {
  background: #F1F3F4;
  border-color: #95A5A6;
}

.score-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.score-label {
  font-size: 15px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.score-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--medical-blue);
  padding: 2px 8px;
  background: rgba(46, 124, 230, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.score-value.zero-value {
  color: #7F8C8D;
  background: rgba(127, 140, 141, 0.1);
}

.zero-indicator {
  font-size: 11px;
  font-weight: 500;
  color: #95A5A6;
  background: #ECF0F1;
  padding: 1px 6px;
  border-radius: 8px;
}

.score-bar {
  height: 12px;
  background: #E4E7ED;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.score-fill {
  height: 100%;
  border-radius: 6px;
  transition: all 0.8s ease;
  position: relative;
  min-width: 2px;
}

.score-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 评分统计 */
.score-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 16px 16px;
  margin-top: 20px;
  background: linear-gradient(135deg, #F8FAFC, #EDF2F7);
  border-radius: 12px;
  border: 1px solid #E4E7ED;
}

.stat-item {
  text-align: center;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--medical-blue);
  line-height: 1;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 13px;
  color: var(--medical-text-dark);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .score-preview-card {
    padding: 16px;
  }

  .overall-score-section {
    padding: 12px;
  }

  .overall-score-circle {
    width: 120px;
    height: 120px;
    border-width: 3px;
  }

  .score-number {
    font-size: 36px;
  }

  .score-level {
    font-size: 18px;
    padding: 6px 12px;
  }

  .score-item {
    padding: 10px;
    margin-bottom: 16px;
  }

  .score-bar {
    height: 10px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 12px;
  }

  .score-stats {
    padding: 16px 12px 12px;
  }

  .stat-item {
    padding: 6px 8px;
  }
}
</style>
