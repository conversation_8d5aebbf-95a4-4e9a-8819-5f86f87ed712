<template>
  <div class="layout_container">
    <!-- 左侧菜单 -->
    <div class="layout_slide">
      <Logo></Logo>
      <!-- 左侧菜单滚动条 -->
      <el-scrollbar class="scrollbar">
        <!-- 菜单组件 - 医疗蓝色主题，支持暗色模式 -->
        <el-menu
          mode="vertical"
          :background-color="layoutSettingStore.darkMode ? '#1e3a5f' : '#2E7CE6'"
          :text-color="layoutSettingStore.darkMode ? '#ffffff' : '#FFFFFF'"
          :active-text-color="layoutSettingStore.darkMode ? '#1ae6c4' : '#00D4AA'"
          :default-active="$route.path"
          :collapse="layoutSettingStore.fold"
        >
          <Menu :menuList="userStore.menuRoutes"></Menu>
        </el-menu>
      </el-scrollbar>
    </div>
    <!-- 顶部导航 -->
    <div class="layout_tabbar" :class="{ fold: layoutSettingStore.fold }">
      <!-- Layout组件顶部导航Tabbar -->
      <Tabbar></Tabbar>
    </div>
    <!-- 内容展示区 -->
    <div class="layout_main" :class="{ fold: layoutSettingStore.fold }">
      <Main></Main>
    </div>
  </div>
</template>

<script setup lang="ts">
import Logo from './logo/index.vue'
import Menu from './menu/index.vue'
import Main from './main/index.vue'
import Tabbar from './tabbar/index.vue'
import useUserStore from '@/store/modules/user'
import { useRoute } from 'vue-router'
import useLayoutSettingStore from '@/store/modules/setting'

let layoutSettingStore = useLayoutSettingStore()

defineOptions({
  name: 'Layout',
})

let $route = useRoute()

let userStore = useUserStore()
</script>

<style scoped lang="scss">
.layout_container {
  width: 100%;
  height: 100vh;

  .layout_slide {
    width: $base-menu-width;
    height: 100vh;
    background-color: var(--menu-bg);
    transition: all 0.4s;

    .scrollbar {
      padding-top: 10px;
      height: calc(100vh - $base-logo-height);
      width: $base-menu-width;
      .el-menu {
        border-right: none;
        // 医疗风格的菜单项样式
        .el-menu-item {
          &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: #ffffff !important;
          }
          &.is-active {
            background-color: rgba(0, 212, 170, 0.2) !important;
            color: #00d4aa !important;
            border-right: 3px solid #00d4aa;
          }
        }
        .el-sub-menu {
          .el-sub-menu__title {
            &:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
              color: #ffffff !important;
            }
          }
        }
      }
    }

    // 选中父元素
    &.fold {
      width: $base-menu-min-width;
    }
  }

  .layout_tabbar {
    width: calc(100% - $base-menu-width);
    height: $base-tabbar-height;
    background-color: var(--tabbar-bg);
    position: fixed;
    top: 0%;
    left: $base-menu-width;
    transition: all 0.4s;
    &.fold {
      width: calc(100vw - $base-menu-min-width);
      left: $base-menu-min-width;
    }
  }

  .layout_main {
    position: absolute;
    top: $base-tabbar-height;
    left: $base-menu-width;
    width: calc(100% - $base-menu-width);
    height: calc(100vh - $base-tabbar-height);
    background-color: var(--bg-secondary);
    padding: 20px;
    overflow: auto; //内容区域加个滚动条，否则内容会超出整个区域滚动
    transition: all 0.4s;
    &.fold {
      width: calc(100vw - $base-menu-min-width);
      left: $base-menu-min-width;
    }
  }
}
</style>
