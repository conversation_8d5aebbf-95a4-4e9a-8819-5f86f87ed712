/**
 * Store状态类型定义
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import type { RouteRecordRaw } from 'vue-router'

/**
 * 用户状态接口
 */
export interface UserState {
  /** 访问令牌 */
  token: string | null
  /** 刷新令牌 */
  refreshToken: string | null
  /** 令牌过期时间 */
  tokenExpires: number | null
  /** 菜单路由 */
  menuRoutes: RouteRecordRaw[]
  /** 异步路由（权限路由） */
  asyncRoutes: RouteRecordRaw[]
  /** 用户ID */
  id: number | null
  /** 用户名 */
  username: string
  /** 头像 */
  avatar: string
  /** 昵称 */
  nickname: string
  /** 性别：1-男，0-女，-1-未知 */
  sex: number
  /** 邮箱 */
  email: string
  /** 手机号 */
  phone: string
  /** 用户角色 */
  roles: string[]
  /** 按钮权限 */
  buttons: string[]

  /** 账户状态 */
  status: number
}

/**
 * 图像数据接口
 */
export interface ImageData {
  /** 图像唯一标识 */
  id: string | number
  /** 图像名称 */
  name: string
  /** 图像数据URL */
  src: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 原始宽度 */
  originalWidth: number
  /** 原始高度 */
  originalHeight: number
  /** 上传时间 */
  uploadTime: string
  /** 文件对象（可选） */
  file?: File
}

/**
 * 标注后图片数据接口
 */
export interface AnnotatedImageData {
  /** 标注后的图片ID */
  imageId: string
  /** 标注后的图片预览URL */
  previewUrl: string
  /** 标注后的文件对象 */
  file: File
  /** 对应的原始图片ID */
  originalImageId: string
  /** 创建时间戳 */
  timestamp: number
}

/**
 * 标注点接口
 */
export interface AnnotationPoint {
  /** 标注点ID */
  id: string
  /** 标注点索引 */
  index: number
  /** X坐标 */
  x: number
  /** Y坐标 */
  y: number
  /** 原始标注数据（可选） */
  originalAnnotation?: any
  /** Fabric.js X坐标（可选） */
  fabricX?: number
  /** Fabric.js Y坐标（可选） */
  fabricY?: number
}

/**
 * 病灶标注接口
 */
export interface LesionAnnotation {
  /** 病灶ID */
  id: string
  /** 病灶描述 */
  description: string
  /** 标注点数组 */
  points: AnnotationPoint[]
  /** 显示颜色 */
  color: string
  /** 创建时间 */
  createdAt: number
  /** 是否可见 */
  isVisible: boolean
}

/**
 * 图像状态管理接口
 */
export interface ImageState {
  /** 原始裁剪后的图像数据（用于标注，始终不变） */
  originalCroppedImage: ImageData | null
  /** 最新的标注后图片数据（用于显示） */
  latestAnnotatedImage: AnnotatedImageData | null
  /** Canvas快照上传后的imageId（用于替换原始图片ID） */
  canvasSnapshotImageId: string | null
  /** 是否来自裁剪流程 */
  fromCropping: boolean
  /** 创建时间戳 */
  timestamp: number | null

  // 医学信息
  /** 当前输入的病灶描述信息 */
  currentLesionDescription: string
  /** 检查部位 */
  examPart: string
  /** 切面类型 */
  sliceType: string

  // 标注数据
  /** 当前临时标注点（未导出） */
  currentAnnotations: AnnotationPoint[]
  /** 已导出的病灶列表 */
  exportedLesions: LesionAnnotation[]
  /** 当前选中的病灶ID */
  selectedLesionId: string | null
}
