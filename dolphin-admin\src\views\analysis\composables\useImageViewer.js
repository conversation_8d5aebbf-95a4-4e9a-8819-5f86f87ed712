import { ref, computed } from 'vue'

export function useImageViewer() {
  // 响应式数据
  const zoomLevel = ref(1)
  const panOffset = ref({ x: 0, y: 0 })
  const isDragging = ref(false)
  const dragStart = ref({ x: 0, y: 0 })

  // 缩放限制
  const MIN_ZOOM = 0.1
  const MAX_ZOOM = 5
  const ZOOM_STEP = 0.2

  // 计算属性
  const zoomPercentage = computed(() => Math.round(zoomLevel.value * 100))

  const canZoomIn = computed(() => zoomLevel.value < MAX_ZOOM)
  const canZoomOut = computed(() => zoomLevel.value > MIN_ZOOM)

  // 缩放方法
  const zoomIn = () => {
    if (canZoomIn.value) {
      zoomLevel.value = Math.min(MAX_ZOOM, zoomLevel.value + ZOOM_STEP)
    }
  }

  const zoomOut = () => {
    if (canZoomOut.value) {
      zoomLevel.value = Math.max(MIN_ZOOM, zoomLevel.value - ZOOM_STEP)
    }
  }

  const setZoom = (zoom) => {
    zoomLevel.value = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoom))
  }

  const resetZoom = () => {
    zoomLevel.value = 1
    panOffset.value = { x: 0, y: 0 }
  }

  // 鼠标滚轮缩放
  const handleWheel = (event, centerX = 0, centerY = 0) => {
    event.preventDefault()
    
    const delta = event.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP
    const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoomLevel.value + delta))
    
    if (newZoom !== zoomLevel.value) {
      // 计算缩放中心点
      const zoomFactor = newZoom / zoomLevel.value
      
      // 调整平移偏移以保持缩放中心点不变
      panOffset.value = {
        x: centerX - (centerX - panOffset.value.x) * zoomFactor,
        y: centerY - (centerY - panOffset.value.y) * zoomFactor
      }
      
      zoomLevel.value = newZoom
    }
  }

  // 平移方法
  const startPan = (clientX, clientY) => {
    isDragging.value = true
    dragStart.value = {
      x: clientX - panOffset.value.x,
      y: clientY - panOffset.value.y
    }
  }

  const updatePan = (clientX, clientY) => {
    if (isDragging.value) {
      panOffset.value = {
        x: clientX - dragStart.value.x,
        y: clientY - dragStart.value.y
      }
    }
  }

  const endPan = () => {
    isDragging.value = false
  }

  // 重置平移
  const resetPan = () => {
    panOffset.value = { x: 0, y: 0 }
  }

  // 适应窗口大小
  const fitToWindow = (imageWidth, imageHeight, containerWidth, containerHeight) => {
    if (!imageWidth || !imageHeight || !containerWidth || !containerHeight) {
      return
    }

    const scaleX = containerWidth / imageWidth
    const scaleY = containerHeight / imageHeight
    const scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小

    setZoom(scale)
    resetPan()
  }

  // 坐标转换方法
  const screenToImageCoordinates = (screenX, screenY, imageWidth, imageHeight) => {
    const imageX = (screenX - panOffset.value.x) / zoomLevel.value
    const imageY = (screenY - panOffset.value.y) / zoomLevel.value
    
    return {
      x: Math.max(0, Math.min(imageWidth, imageX)),
      y: Math.max(0, Math.min(imageHeight, imageY))
    }
  }

  const imageToScreenCoordinates = (imageX, imageY) => {
    return {
      x: imageX * zoomLevel.value + panOffset.value.x,
      y: imageY * zoomLevel.value + panOffset.value.y
    }
  }

  // 检查点是否在图像范围内
  const isPointInImage = (screenX, screenY, imageWidth, imageHeight) => {
    const imageCoords = screenToImageCoordinates(screenX, screenY, imageWidth, imageHeight)
    return imageCoords.x >= 0 && imageCoords.x <= imageWidth &&
           imageCoords.y >= 0 && imageCoords.y <= imageHeight
  }

  // 获取变换样式
  const getTransformStyle = () => {
    return {
      transform: `translate(${panOffset.value.x}px, ${panOffset.value.y}px) scale(${zoomLevel.value})`,
      transformOrigin: '0 0'
    }
  }

  // 全屏相关
  const isFullscreen = ref(false)

  const toggleFullscreen = (element) => {
    if (!document.fullscreenElement) {
      element.requestFullscreen?.()
      isFullscreen.value = true
    } else {
      document.exitFullscreen?.()
      isFullscreen.value = false
    }
  }

  // 监听全屏状态变化
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement
  }

  // 键盘快捷键处理
  const handleKeydown = (event) => {
    switch (event.key) {
      case '+':
      case '=':
        event.preventDefault()
        zoomIn()
        break
      case '-':
        event.preventDefault()
        zoomOut()
        break
      case '0':
        event.preventDefault()
        resetZoom()
        break
      case 'f':
      case 'F':
        if (event.ctrlKey) {
          event.preventDefault()
          // 可以触发适应窗口
        }
        break
    }
  }

  return {
    // 响应式数据
    zoomLevel,
    panOffset,
    isDragging,
    isFullscreen,
    
    // 计算属性
    zoomPercentage,
    canZoomIn,
    canZoomOut,
    
    // 缩放方法
    zoomIn,
    zoomOut,
    setZoom,
    resetZoom,
    handleWheel,
    
    // 平移方法
    startPan,
    updatePan,
    endPan,
    resetPan,
    
    // 适应窗口
    fitToWindow,
    
    // 坐标转换
    screenToImageCoordinates,
    imageToScreenCoordinates,
    isPointInImage,
    
    // 样式
    getTransformStyle,
    
    // 全屏
    toggleFullscreen,
    handleFullscreenChange,
    
    // 键盘
    handleKeydown
  }
}
