import SvgIcon from './SvgIcon/index.vue'
import ImageUploader from './ImageUploader/index.vue'
import UserAvatar from './UserAvatar/index.vue'
import ImageCropper from './ImageCropper/index.vue'
import type { App, Component } from 'vue'
const components: { [name: string]: Component } = { SvgIcon, ImageUploader, UserAvatar, ImageCropper }
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

export default {
  install(app: App) {
    Object.keys(components).forEach((key: string) => {
      app.component(key, components[key])
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
  },
}
