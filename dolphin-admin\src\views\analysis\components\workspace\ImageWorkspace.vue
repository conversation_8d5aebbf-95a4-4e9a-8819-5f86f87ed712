<template>
  <div class="image-workspace">
    <!-- 标注工具栏 -->
    <div class="annotation-toolbar-area">
      <AnnotationToolbar
        :current-mode="annotationMode"
        :has-annotations="tempAnnotations.length > 0"
        :can-export="canExportLesion"
        @mode-change="handleAnnotationModeChange"
        @undo-last-annotation="handleUndoLastAnnotation"
        @export-lesion="handleExportLesion"
      />
    </div>

    <!-- 图像显示区域 - 占据整个空间并居中 -->
    <div class="image-viewer-area">
      <MedicalImageCanvas
        ref="canvasRef"
        :image-url="imageData?.src"
        :image-data="imageData"
        :mode="annotationMode"
        :canvas-width="canvasWidth"
        :canvas-height="canvasHeight"
        @canvas-ready="handleCanvasReady"
        @image-loaded="handleImageLoad"
        @annotation-created="handleAnnotationCreated"
        @annotation-updated="handleAnnotationUpdated"
        @annotation-selected="handleAnnotationSelected"
        @mode-changed="handleModeChanged"
        @error="handleCanvasError"
      />
    </div>

    <!-- 标注管理器（逻辑组件） -->
    <AnnotationManager
      ref="annotationManagerRef"
      :canvas-ref="canvasRef"
      :annotation-mode="annotationMode"
      :canvas-width="canvasWidth"
      :canvas-height="canvasHeight"
      @annotations-updated="handleAnnotationsUpdated"
      @temp-annotations-updated="handleTempAnnotationsUpdated"
      @annotation-saved="handleAnnotationSaved"
      @annotation-undone="handleAnnotationUndone"
    />
  </div>
</template>

<script setup>
import { ref, nextTick, computed } from 'vue'
import MedicalImageCanvas from '../image/MedicalImageCanvas.vue'
import AnnotationToolbar from '../image/AnnotationToolbar.vue'
import AnnotationManager from '../annotation/AnnotationManager.vue'
import useImageStore from '@/store/modules/image'

// 定义组件名称
defineOptions({
  name: 'ImageWorkspace',
})

// 定义 Props
const props = defineProps({
  imageData: {
    type: Object,
    default: null,
  },
  canvasWidth: {
    type: Number,
    default: 720,
  },
  canvasHeight: {
    type: Number,
    default: 720,
  },
  initialMode: {
    type: String,
    default: 'draw-point',
  },
  lesionDescription: {
    type: String,
    default: '',
  },
})

// 定义 Events
const emit = defineEmits([
  'canvas-ready',
  'image-loaded',
  'annotations-updated',
  'annotation-saved',
  'canvas-error',
  'lesion-exported',
])

// 响应式数据
const canvasRef = ref(null)
const annotationManagerRef = ref(null)
const annotationMode = ref(props.initialMode)
const tempAnnotations = ref([])
const annotations = ref([])

// 计算属性
const canExportLesion = computed(() => {
  return tempAnnotations.value.length >= 3 && props.lesionDescription.trim().length > 0
})

// Canvas 相关事件处理
const handleCanvasReady = (canvas) => {
  // 确保画布准备好后立即设置正确的模式
  nextTick(() => {
    if (canvasRef.value) {
      canvasRef.value.setMode(annotationMode.value)
    }
  })

  emit('canvas-ready', canvas)
}

const handleImageLoad = (imageInfo) => {
  emit('image-loaded', imageInfo)
}

const handleCanvasError = (error) => {
  emit('canvas-error', error)
}

// 标注相关事件处理
const handleAnnotationCreated = (annotation) => {
  if (annotationManagerRef.value) {
    annotationManagerRef.value.handleAnnotationCreated(annotation)
  }
}

const handleAnnotationUpdated = () => {
  // 可以在这里处理标注更新逻辑
}

const handleAnnotationSelected = () => {
  // 可以在这里处理标注选择逻辑
}

const handleModeChanged = (mode) => {
  annotationMode.value = mode
}

// 工具栏事件处理
const handleAnnotationModeChange = (mode) => {
  annotationMode.value = mode

  if (canvasRef.value) {
    canvasRef.value.setMode(mode)
  }
}

const handleUndoLastAnnotation = () => {
  if (annotationManagerRef.value) {
    annotationManagerRef.value.handleUndoLastAnnotation()
  }
}

// 导出病灶处理
const handleExportLesion = () => {
  emit('lesion-exported', {
    description: props.lesionDescription,
    annotations: tempAnnotations.value,
  })
}

// 创建闭合区域
const createClosedRegion = (points, color = '#ff4757') => {
  if (canvasRef.value && points.length >= 3) {
    canvasRef.value.createAnnotationRegion(points, color)
  }
}

// 删除闭合区域和标注点
const deleteClosedRegion = (color, lesionPoints = []) => {
  if (canvasRef.value) {
    return canvasRef.value.deleteAnnotationRegion(color, lesionPoints)
  }
  return false
}

// 保存Canvas快照
const saveCanvasSnapshot = () => {
  if (canvasRef.value) {
    const canvasDataURL = canvasRef.value.exportCanvasAsImage()
    if (canvasDataURL) {
      // 保存到ImageStore
      const imageStore = useImageStore()

      imageStore.updateCanvasSnapshot(canvasDataURL)

      return true
    }
  }
  return false
}

// 标注管理器事件处理
const handleAnnotationsUpdated = (newAnnotations) => {
  annotations.value = newAnnotations
  emit('annotations-updated', newAnnotations)
}

const handleTempAnnotationsUpdated = (newTempAnnotations) => {
  tempAnnotations.value = newTempAnnotations
}

const handleAnnotationSaved = (savedAnnotations) => {
  emit('annotation-saved', savedAnnotations)
}

const handleAnnotationUndone = () => {}

// 暴露给父组件的方法和数据
defineExpose({
  // 数据
  annotations,
  tempAnnotations,
  annotationMode,

  // Canvas相关方法
  getCanvas: () => canvasRef.value?.getCanvas(),
  setMode: (mode) => {
    annotationMode.value = mode
    canvasRef.value?.setMode(mode)
  },

  // 标注相关方法
  clearAnnotations: () => {
    annotationManagerRef.value?.clearAnnotations()
  },
  clearTempAnnotations: () => {
    annotationManagerRef.value?.clearTempAnnotations()
  },
  clearAllAnnotations: () => {
    if (canvasRef.value) {
      canvasRef.value.clearAllAnnotations()
    }
  },
  createClosedRegion,
  deleteClosedRegion,
  saveCanvasSnapshot,
})
</script>

<style scoped>
.image-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
  min-height: 600px;
  position: relative;
}

.annotation-toolbar-area {
  flex-shrink: 0;
  padding: 8px;
  background: var(--bg-primary);
  position: relative;
  z-index: 50;
  display: flex;
  justify-content: center; /* 工具栏居中显示 */
}

/* 图像查看器区域 - 占据整个空间并居中显示图像 */
.image-viewer-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  padding: 2rem;
  min-height: 0;
}
</style>
