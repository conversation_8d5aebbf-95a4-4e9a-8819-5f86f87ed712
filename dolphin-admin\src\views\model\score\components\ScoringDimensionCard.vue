<template>
  <div :class="['dimension-card', { 'critical-dimension': dimension.critical }]">
    <!-- 维度标题区域 -->
    <div class="dimension-header">
      <div class="dimension-title">
        <div class="title-left">
          <el-icon class="dimension-icon">
            <component :is="getIconComponent(dimension.icon)" />
          </el-icon>
          <h3>{{ dimension.name }}</h3>
          <el-tag v-if="dimension.critical" type="danger" size="small" class="critical-tag">
            一票否决
          </el-tag>
          <div class="info-icon-tooltip">
            <el-icon class="info-icon">
              <InfoFilled />
            </el-icon>
            <span class="tooltip-text">{{ dimension.description }}</span>
          </div>
        </div>

        <div v-if="score > 0" class="current-score">
          <span class="score-display">{{ score }}/5</span>
          <span class="score-level">{{ dimension.levels[score - 1] }}</span>
        </div>
      </div>
    </div>

    <!-- 评分选项区域 -->
    <div class="rating-section">
      <div class="rating-header">
        <span class="rating-label">请选择评分</span>
        <div class="rating-scale">
          <span class="scale-low">1分 (最低)</span>
          <span class="scale-high">5分 (最高)</span>
        </div>
      </div>

      <div class="rating-buttons">
        <button
          v-for="(level, index) in dimension.levels"
          :key="index"
          :class="[
            'rating-button',
            { 'active': score === index + 1 },
            `score-${index + 1}`
          ]"
          @click="handleScoreChange(index + 1)"
        >
          <div class="button-score">{{ index + 1 }}</div>
          <div class="button-text">{{ level }}</div>
        </button>
      </div>
    </div>

    <!-- 一票否决警告 -->
    <div v-if="dimension.critical && score <= 2 && score > 0" class="veto-warning">
      <div class="warning-content">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <div class="warning-text">
          <div class="warning-title">一票否决警告</div>
          <div class="warning-desc">{{ dimension.name }}评分过低可能导致整体评估不合格，请仔细核实医学内容的正确性</div>
        </div>
      </div>
    </div>

    <!-- 评分理由区域 -->
    <div ref="commentSectionRef" class="comment-section">
      <div class="comment-header">
        <label class="comment-label">评分理由 <span class="required">*</span></label>
        <span class="comment-tip">请详细说明评分依据</span>
      </div>
      <div class="comment-input">
        <textarea
          :value="comment"
          :placeholder="`请从专业角度详细说明${dimension.name}的评分理由，包括具体的优点、不足和改进建议...`"
          maxlength="200"
          rows="3"
          class="comment-textarea"
          @input="handleCommentChange"
        />
        <div class="comment-count">{{ comment.length }}/200</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Aim,
  List,
  Connection,
  Reading,
  Lock,
  InfoFilled,
  Warning
} from '@element-plus/icons-vue'
import type { ScoringDimension } from '../types'

// Props
interface Props {
  dimension: ScoringDimension
  score: number
  comment: string
}

// Events
interface Emits {
  (e: 'score-change', dimensionId: string, score: number): void
  (e: 'comment-change', dimensionId: string, comment: string): void
}

// 获取 props 和 emit
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 模板引用
const commentSectionRef = ref<HTMLElement>()

// 方法
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    Target: Aim,
    List,
    Connection,
    Reading,
    Lock
  }
  return iconMap[iconName] || Aim
}

const handleScoreChange = (score: number) => {
  emit('score-change', props.dimension.id, score)

  // 检查准确性一票否决
  if (props.dimension.id === 'accuracy' && score <= 2) {
    ElMessage.warning('准确性评分较低，请仔细核实医学内容的正确性')
  }

  // 点击打分后自动滚动到评分理由区域
  nextTick(() => {
    if (commentSectionRef.value) {
      commentSectionRef.value.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  })
}

const handleCommentChange = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('comment-change', props.dimension.id, target.value)
}
</script>

<style scoped>
/* 医疗专业配色变量 */
:root {
  --medical-blue: #2E7CE6;
  --medical-green: #00D4AA;
  --medical-orange: #FF8C00;
  --medical-red: #FF4757;
  --medical-card: #FFFFFF;
  --medical-text-dark: #2C3E50;
  --medical-text-light: #7F8C8D;
  --medical-bg-light: #F8FAFC;
  --medical-border: #E4E7ED;
}

/* 维度卡片 */
.dimension-card {
  background: var(--medical-card);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 0px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--medical-border);
  transition: all 0.3s ease;
  position: relative;
}

.dimension-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.critical-dimension {
  border-left: 5px solid var(--medical-red);
  background: linear-gradient(135deg, #FFF8F8, var(--medical-card));
}

.critical-dimension::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--medical-red), #FF8A80);
  border-radius: 16px 16px 0 0;
}

/* 维度标题区域 */
.dimension-header {
  margin-bottom: 24px;
}

.dimension-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-left h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--medical-text-dark);
  line-height: 1.2;
}

.dimension-icon {
  font-size: 31px;
  color: var(--medical-blue);
  padding: 8px;
  background: rgba(46, 124, 230, 0.1);
  border-radius: 8px;
}

.critical-dimension .dimension-icon {
  color: var(--medical-red);
  background: rgba(255, 71, 87, 0.1);
}

/* 信息图标悬停提示容器 */
.info-icon-tooltip {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.info-icon {
  font-size: 30px;
  color: #9CA3AF;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 6px;
  border-radius: 4px;
  flex-shrink: 0;
}

.info-icon:hover {
  color: var(--medical-text-light);
  background: rgba(156, 163, 175, 0.1);
}

/* 悬停提示文字 */
.tooltip-text {
  visibility: hidden;
  opacity: 0;
  color: #000000;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 400;
  white-space: nowrap;
  transition: all 0.3s ease;
  transform: translateX(-10px);
}

.info-icon-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
  transform: translateX(0);
}



.current-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.score-display {
  font-size: 24px;
  font-weight: 600;
  color: var(--medical-blue);
  line-height: 1;
}

.score-level {
  font-size: 12px;
  color: var(--medical-text-light);
  font-weight: 400;
}

/* 评分选项区域 */
.rating-section {
  margin-bottom: 28px;
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.rating-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.rating-scale {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--medical-text-light);
}

.scale-low, .scale-high {
  padding: 4px 8px;
  background: var(--medical-bg-light);
  border-radius: 4px;
}

/* 评分按钮组 */
.rating-buttons {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.rating-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border: 2px solid #D1D5DB;
  border-radius: 12px;
  background: var(--medical-card);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.rating-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  transition: all 0.3s ease;
}

.rating-button:hover {
  border-color: transparent;
  background: #F0F9FF;
  box-shadow: 0 4px 16px rgba(46, 124, 230, 0.15);
}

.rating-button:hover::before {
  background: var(--medical-blue);
}

.button-score {
  font-size: 24px;
  font-weight: 600;
  color: var(--medical-blue);
  margin-bottom: 8px;
  line-height: 1;
}

.button-text {
  font-size: 12px;
  color: var(--medical-text-light);
  font-weight: 400;
  line-height: 1.3;
  text-align: center;
  word-break: break-all;
}

/* 激活状态 */
.rating-button.active {
  border-color: transparent;
  position: relative;
}

.rating-button.active .button-score {
  font-weight: 600;
  font-size: 26px;
}

.rating-button.active .button-text {
  font-weight: 500;
  font-size: 13px;
}

/* 不同分数的颜色 */
.rating-button.score-1.active {
  background: #FEF2F2;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
}

.rating-button.score-1.active::before {
  background: var(--medical-red);
}

.rating-button.score-1.active .button-score,
.rating-button.score-1.active .button-text {
  color: var(--medical-red);
}

.rating-button.score-2.active {
  background: #FEF2F2;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
}

.rating-button.score-2.active::before {
  background: var(--medical-red);
}

.rating-button.score-2.active .button-score,
.rating-button.score-2.active .button-text {
  color: var(--medical-red);
}

.rating-button.score-3.active {
  background: #FFF7ED;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(255, 140, 0, 0.2);
}

.rating-button.score-3.active::before {
  background: var(--medical-orange);
}

.rating-button.score-3.active .button-score,
.rating-button.score-3.active .button-text {
  color: var(--medical-orange);
}

.rating-button.score-4.active {
  background: #EFF6FF;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(46, 124, 230, 0.2);
}

.rating-button.score-4.active::before {
  background: var(--medical-blue);
}

.rating-button.score-4.active .button-score,
.rating-button.score-4.active .button-text {
  color: var(--medical-blue);
}

.rating-button.score-5.active {
  background: #F0FDFA;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(0, 212, 170, 0.2);
}

.rating-button.score-5.active::before {
  background: var(--medical-green);
}

.rating-button.score-5.active .button-score,
.rating-button.score-5.active .button-text {
  color: var(--medical-green);
}

/* 一票否决警告 */
.veto-warning {
  margin-top: 20px;
  margin-bottom: 20px;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
}

.warning-icon {
  color: var(--medical-red);
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--medical-red);
  margin-bottom: 4px;
}

.warning-desc {
  font-size: 14px;
  color: #B71C1C;
  line-height: 1.5;
}

/* 评论区域 */
.comment-section {
  margin-top: 24px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--medical-text-dark);
}

.required {
  color: var(--medical-red);
  margin-left: 4px;
}

.comment-tip {
  font-size: 12px;
  color: var(--medical-text-light);
}

.comment-input {
  position: relative;
}

.comment-textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid #D1D5DB;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--medical-text-dark);
  background: var(--medical-card);
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.comment-textarea:focus {
  outline: none;
  border-color: #D1D5DB;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  background: #FAFBFF;
}

.comment-textarea::placeholder {
  color: #9CA3AF;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

.comment-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: var(--medical-text-light);
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dimension-card {
    padding: 20px;
  }

  .rating-buttons {
    gap: 10px;
  }

  .rating-button {
    padding: 14px 6px;
  }

  .button-score {
    font-size: 20px;
  }

  .button-text {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .dimension-card {
    padding: 16px;
    margin-bottom: 20px;
  }

  .dimension-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .title-left h3 {
    font-size: 18px;
  }

  .current-score {
    align-items: flex-start;
  }

  .rating-buttons {
    gap: 8px;
  }

  .rating-button {
    padding: 12px 4px;
  }

  .button-score {
    font-size: 18px;
    margin-bottom: 6px;
  }

  .button-text {
    font-size: 10px;
  }

  .rating-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .comment-textarea {
    padding: 12px;
    font-size: 13px;
    border: 2px solid #D1D5DB;
  }

  .comment-textarea::placeholder {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dimension-card {
    padding: 12px;
  }

  .rating-buttons {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .rating-button {
    padding: 10px 4px;
  }

  .button-score {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .button-text {
    font-size: 9px;
    line-height: 1.2;
  }

  .warning-content {
    padding: 12px;
  }

  .warning-title {
    font-size: 14px;
  }

  .warning-desc {
    font-size: 13px;
  }
}
</style>

