/**
 * 标注数据本地存储管理工具
 * 用于在localStorage中管理图像标注数据的存储、读取和清理
 */

// 标注数据接口定义
export interface AnnotationData {
  focus: string           // 病灶信息
  location: string[]      // 标注位置数组，格式如 ["(x1,y1)", "(x2,y2)"]
  status: 'draft' | 'saved'  // 标注状态
}

// localStorage key前缀
const STORAGE_PREFIX = 'annotation_'

/**
 * 标注数据存储管理类
 */
export class AnnotationStorage {

  /**
   * 保存标注数据到localStorage
   * @param imageId 图像ID
   * @param data 标注数据
   */
  static save(imageId: string, data: AnnotationData): void {
    try {
      const key = `${STORAGE_PREFIX}${imageId}`
      const jsonData = JSON.stringify(data)
      localStorage.setItem(key, jsonData)

    } catch (error) {
      console.error('❌ 保存标注数据失败:', error)
      throw new Error('保存标注数据失败')
    }
  }

  /**
   * 从localStorage读取标注数据
   * @param imageId 图像ID
   * @returns 标注数据或null
   */
  static load(imageId: string): AnnotationData | null {
    try {
      const key = `${STORAGE_PREFIX}${imageId}`
      const jsonData = localStorage.getItem(key)

      if (!jsonData) {

        return null
      }

      const data = JSON.parse(jsonData) as AnnotationData

      return data
    } catch (error) {
      console.error('❌ 读取标注数据失败:', error)
      return null
    }
  }

  /**
   * 检查是否存在标注数据
   * @param imageId 图像ID
   * @returns 是否存在标注数据
   */
  static exists(imageId: string): boolean {
    const key = `${STORAGE_PREFIX}${imageId}`
    return localStorage.getItem(key) !== null
  }

  /**
   * 删除指定图像的标注数据
   * @param imageId 图像ID
   */
  static remove(imageId: string): void {
    try {
      const key = `${STORAGE_PREFIX}${imageId}`
      localStorage.removeItem(key)

    } catch (error) {
      console.error('❌ 删除标注数据失败:', error)
    }
  }

  /**
   * 清理所有标注数据
   */
  static clearAll(): void {
    try {
      const keys = Object.keys(localStorage)
      const annotationKeys = keys.filter(key => key.startsWith(STORAGE_PREFIX))

      annotationKeys.forEach(key => {
        localStorage.removeItem(key)
      })


    } catch (error) {
      console.error('❌ 清理标注数据失败:', error)
    }
  }

  /**
   * 获取所有标注数据的图像ID列表
   * @returns 图像ID数组
   */
  static getAllImageIds(): string[] {
    try {
      const keys = Object.keys(localStorage)
      const imageIds = keys
        .filter(key => key.startsWith(STORAGE_PREFIX))
        .map(key => key.replace(STORAGE_PREFIX, ''))


      return imageIds
    } catch (error) {
      console.error('❌ 获取图像ID列表失败:', error)
      return []
    }
  }

  /**
   * 更新标注数据的状态
   * @param imageId 图像ID
   * @param status 新状态
   */
  static updateStatus(imageId: string, status: 'draft' | 'saved'): boolean {
    try {
      const data = this.load(imageId)
      if (!data) {
        console.warn('⚠️ 未找到要更新的标注数据:', imageId)
        return false
      }

      data.status = status
      this.save(imageId, data)

      return true
    } catch (error) {
      console.error('❌ 更新标注状态失败:', error)
      return false
    }
  }

  /**
   * 获取localStorage使用情况统计
   * @returns 统计信息
   */
  static getStorageStats(): {
    totalAnnotations: number
    draftCount: number
    savedCount: number
    storageSize: number
  } {
    try {
      const imageIds = this.getAllImageIds()
      let draftCount = 0
      let savedCount = 0
      let storageSize = 0

      imageIds.forEach(imageId => {
        const data = this.load(imageId)
        if (data) {
          if (data.status === 'draft') draftCount++
          else if (data.status === 'saved') savedCount++

          const key = `${STORAGE_PREFIX}${imageId}`
          const jsonData = localStorage.getItem(key)
          if (jsonData) {
            storageSize += jsonData.length
          }
        }
      })

      const stats = {
        totalAnnotations: imageIds.length,
        draftCount,
        savedCount,
        storageSize
      }


      return stats
    } catch (error) {
      console.error('❌ 获取存储统计失败:', error)
      return {
        totalAnnotations: 0,
        draftCount: 0,
        savedCount: 0,
        storageSize: 0
      }
    }
  }
}

// 导出便捷方法
export const annotationStorage = {
  save: AnnotationStorage.save,
  load: AnnotationStorage.load,
  exists: AnnotationStorage.exists,
  remove: AnnotationStorage.remove,
  clearAll: AnnotationStorage.clearAll,
  getAllImageIds: AnnotationStorage.getAllImageIds,
  updateStatus: AnnotationStorage.updateStatus,
  getStorageStats: AnnotationStorage.getStorageStats
}

export default AnnotationStorage
