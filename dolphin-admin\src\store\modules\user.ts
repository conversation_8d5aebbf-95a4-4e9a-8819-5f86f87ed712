import { defineStore } from 'pinia'
import { reqLogin, reqUserInfo, reqRefreshToken } from '@/api/user'
// import { reqLogout } from '@/api/user' // 暂时注释，等后端接口实现后再启用
import type { loginForm, loginResponse, userResponseData, refreshTokenResponse } from '@/api/user/type'
import type { UserState } from './types/types'
import {
  GET_TOKEN,
  GET_REFRESH_TOKEN,
  GET_TOKEN_EXPIRES,
  SET_TOKEN_INFO,
  CLEAR_ALL_TOKENS,
  IS_TOKEN_EXPIRED,
  GET_USER_INFO,
  SET_USER_INFO
} from '@/utils/token'
import { constantRoute } from '@/router/routes'
import { API_CONFIG } from '@/config/api'
import { authDebugger } from '@/utils/authDebug'

/**
 * 用户状态管理Store
 *
 * 功能：
 * - 用户登录/登出
 * - 令牌管理（访问令牌 + 刷新令牌）
 * - 用户信息管理
 * - 权限路由管理
 * - 自动令牌刷新
 */
let useUserStore = defineStore('User', {
  state: (): UserState => {
    const token = GET_TOKEN()
    const refreshToken = GET_REFRESH_TOKEN()
    const tokenExpires = GET_TOKEN_EXPIRES()
    const cachedUserInfo = GET_USER_INFO()

    return {
      // 令牌相关
      token,
      refreshToken,
      tokenExpires,

      // 路由相关
      menuRoutes: constantRoute, // 初始化为常量路由
      asyncRoutes: [], // 异步路由（权限路由）

      // 用户信息 - 从缓存恢复
      id: cachedUserInfo?.id || null,
      username: cachedUserInfo?.username || '',
      nickname: cachedUserInfo?.nickname || '',
      avatar: cachedUserInfo?.avatar || '',
      sex: cachedUserInfo?.sex || -1, // 默认为未知
      email: cachedUserInfo?.email || '',
      phone: cachedUserInfo?.phone || '',
      status: cachedUserInfo?.status || 1,

      // 权限相关 - 从缓存恢复
      roles: cachedUserInfo?.roles || [], // 用户角色
      buttons: cachedUserInfo?.buttons || [], // 按钮权限
    }
  },
  actions: {
    /**
     * 用户登录
     * @param val 登录表单数据
     * @returns Promise<string> 操作结果
     */
    async userLogin(val: loginForm) {
      try {
        // 注意：reqLogin返回的是经过axios响应拦截器处理后的data.data
        const response = await reqLogin(val) as unknown as loginResponse

        // 保存令牌信息
        this.token = response.accessToken
        this.refreshToken = response.refreshToken
        this.tokenExpires = null

        // 存储到本地（过期时间暂时设为null）
        SET_TOKEN_INFO(response.accessToken, response.refreshToken, null)
        authDebugger.log('登录成功，token已保存', 'userStore.userLogin')

        // 登录成功后，立即获取用户信息
        await this.userInfo()

        return 'ok'
      } catch (err: any) {
        return Promise.reject(new Error(err.message))
      }
    },

    /**
     * 获取用户详细信息
     * @param retryCount 重试次数，默认为0
     * @returns Promise<string> 操作结果
     */
    async userInfo(retryCount: number = 0): Promise<string> {
      try {
        const result = await reqUserInfo() as unknown as userResponseData


        // 更新用户信息
        this.id = result.id
        this.username = result.username
        this.avatar = result.avatar || ''
        this.nickname = result.nickname || ''
        this.sex = result.sex || -1
        this.email = result.email || ''
        this.phone = result.phone || ''
        this.status = result.status || 1

        // 更新权限信息
        if (result.roles) {
          this.roles = result.roles
        }
        if (result.buttons) {
          this.buttons = result.buttons
        }

        // 缓存用户信息到localStorage
        const userInfoToCache = {
          id: this.id,
          username: this.username,
          nickname: this.nickname,
          avatar: this.avatar,
          sex: this.sex,
          email: this.email,
          phone: this.phone,
          status: this.status,
          roles: this.roles,
          buttons: this.buttons
        }
        SET_USER_INFO(userInfoToCache)

        // 根据用户权限生成菜单路由
        this.generateRoutes()

        return 'ok'
      } catch (err: any) {
        console.error('❌ 获取用户信息失败:', err)

        // 如果是网络错误且重试次数小于2，则进行重试
        if (retryCount < 2 && (!err.code || err.code >= 500)) {
          console.log(`🔄 网络错误，进行第${retryCount + 1}次重试...`)
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))) // 递增延迟
          return this.userInfo(retryCount + 1)
        }

        return Promise.reject(new Error(err.message))
      }
    },

    /**
     * 用户登出
     * @returns Promise<void>
     */
    async userLogout() {


      // 根据配置决定是否调用登出接口
      if (API_CONFIG.LOGOUT_API_ENABLED) {
        try {
          if (this.refreshToken) {

            // 动态导入登出接口，避免编译时错误
            const { reqLogout } = await import('@/api/user')
            await reqLogout({ refreshToken: this.refreshToken })

          }
        } catch (error) {
          console.warn('⚠️ 登出接口调用失败:', error)
          // 即使接口失败，也要清理本地状态
        }
      } else {
        console.log('⏭️ 跳过登出接口调用（配置中已禁用）')
      }


      authDebugger.log('用户登出', 'userStore.userLogout')
      this.clearUserState()

    },

    /**
     * 刷新访问令牌
     * @returns Promise<boolean> 刷新是否成功
     */
    async refreshAccessToken(): Promise<boolean> {
      try {
        if (!this.refreshToken) {
          throw new Error('没有刷新令牌')
        }

        const response = await reqRefreshToken({ refreshToken: this.refreshToken }) as unknown as refreshTokenResponse

        // 更新令牌信息
        this.token = response.accessToken
        this.refreshToken = response.refreshToken
        // 由于API没有返回过期时间，保持原有值或设为null
        this.tokenExpires = null

        // 存储到本地
        SET_TOKEN_INFO(response.accessToken, response.refreshToken, null)

        return true
      } catch (error) {
        console.error('刷新令牌失败:', error)
        // 刷新失败，清理状态
        this.clearUserState()
        return false
      }
    },

    /**
     * 检查令牌是否需要刷新
     * @returns Promise<boolean> 是否需要刷新
     */
    async checkTokenRefresh(): Promise<boolean> {
      if (!this.token || !this.refreshToken) {
        return false
      }

      // 只有在明确知道令牌过期时间且即将过期时才刷新
      // 如果没有过期时间信息，依赖后端的401响应来处理过期
      if (this.tokenExpires && IS_TOKEN_EXPIRED(60)) {
        return await this.refreshAccessToken()
      }

      return true
    },

    /**
     * 清理用户状态
     */
    clearUserState() {
      // 清理令牌
      this.token = null
      this.refreshToken = null
      this.tokenExpires = null

      // 清理用户信息
      this.id = null
      this.username = ''
      this.nickname = ''
      this.avatar = ''
      this.sex = -1
      this.email = ''
      this.phone = ''
      this.status = 1

      // 清理权限信息
      this.roles = []
      this.buttons = []
      this.menuRoutes = constantRoute
      this.asyncRoutes = []

      // 清理本地存储
      CLEAR_ALL_TOKENS()
      authDebugger.log('用户状态已清理', 'userStore.clearUserState')
    },
    // 根据用户权限生成菜单路由
    generateRoutes() {
      // 这里可以根据用户角色过滤路由
      // 示例：管理员可以看到所有菜单，普通用户只能看到部分菜单
      if (this.roles && this.roles.includes('平台管理员')) {
        this.menuRoutes = constantRoute
      } else {
        // 如果没有权限信息或非管理员，过滤掉权限管理相关的菜单
        this.menuRoutes = constantRoute.filter(route => {
          if (route.path === '/acl') {
            return false // 非管理员不显示权限管理菜单
          }
          return true
        })
      }
    },
  },
  getters: {},
})

export default useUserStore
