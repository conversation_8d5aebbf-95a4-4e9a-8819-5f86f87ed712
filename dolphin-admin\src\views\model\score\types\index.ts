// 评分维度接口
export interface ScoringDimension {
  id: string
  name: string
  icon: string
  description: string
  critical: boolean
  levels: string[]
}

// 评分数据接口
export interface ScoreData {
  [key: string]: number
}

// 评论数据接口
export interface CommentData {
  [key: string]: string
}

// 病例信息接口
export interface CaseInfo {
  caseId: string
  diagnosisType: 'normal' | 'benign' | 'malignant' | 'uncertain' | 'other'
  diagnosisDetail: string
}

// 评分等级接口
export interface ScoreLevel {
  text: string
  color: string
}

// 权重配置接口
export interface WeightConfig {
  accuracy: number
  completeness: number
  relevance: number
  readability: number
  safety: number
}
