
// 覆盖 Element Plus 菜单项高度和间距
.el-menu-item {
    height: 45px !important;
    line-height: 45px !important;
    font-size: 15px !important;  // 增加字体大小，让字体更饱满
    font-weight: 500 !important;  // 增加字体粗细，让字体更饱满
    margin: 4px 10px !important;  // 增加上下间距
    border-radius: 8px !important;  // 默认状态也要有圆角
    transition: all 0.3s ease !important;  // 添加过渡动画
  }

  .el-sub-menu__title {
    height: 45px !important;
    line-height: 45px !important;
    font-size: 15px !important;  // 增加字体大小，让字体更饱满
    font-weight: 500 !important;  // 增加字体粗细，让字体更饱满
    margin: 4px 8px !important;  // 增加上下间距
    border-radius: 8px !important;  // 默认状态也要有圆角
    transition: all 0.3s ease !important;  // 添加过渡动画
  }

  // 菜单项hover样式
  .el-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    margin: 4px 10px !important;
  }

  // 子菜单标题hover样式
  .el-sub-menu__title:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;
  }

  // 子菜单激活状态样式 - 新添加
  .el-sub-menu.is-active > .el-sub-menu__title {
    background-color: rgba(0, 212, 170, 0.2) !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;
    color: #00D4AA !important;
  }

  // 菜单项激活状态样式 - 新添加
  .el-menu-item.is-active {
    background-color: rgba(0, 212, 170, 0.2) !important;
    border-radius: 8px !important;
    margin: 4px 10px !important;
    color: #00D4AA !important;
  }


  // 菜单项文字样式 - 确保所有文字都有饱满效果
  .el-menu-item span,
  .el-sub-menu__title span {
    font-size: 15px !important;  // 增加字体大小，让字体更饱满
    font-weight: 500 !important;  // 增加字体粗细，让字体更饱满
  }

  // 折叠状态下的样式修复 - 更精确的选择器
  .el-menu--collapse {
    .el-menu-item,
    .el-sub-menu > .el-sub-menu__title {
      height: 45px !important;
      margin: 4px 0px !important;  // 折叠状态下也保持间距
      padding-left: 0 !important;
      padding-right: 0 !important;
      display: flex !important;
      justify-content: center !important;  // 使用flex居中
      align-items: center !important;
      font-size: 15px !important;  // 折叠状态下也保持字体大小
      font-weight: 500 !important;  // 折叠状态下也保持字体粗细
    }

    .el-menu-item:hover,
    .el-sub-menu > .el-sub-menu__title:hover {
      margin: 4px 0px !important;
      border-radius: 6px !important;  // 保持圆角但稍小一点
      background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .el-menu-item.is-active,
    .el-sub-menu.is-active > .el-sub-menu__title {
      margin: 4px 0px !important;
      border-radius: 6px !important;
      background-color: rgba(0, 212, 170, 0.2) !important;
      color: #00D4AA !important;
    }
  }
