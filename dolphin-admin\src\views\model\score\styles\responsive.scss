/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    padding: 16px;
  }

  .right-panel {
    width: 100%;
  }

  .sticky-content {
    position: static;
  }

  .case-info-compact {
    gap: 24px;
  }

  .info-group {
    padding: 10px 14px;
    font-size: 14px;
  }

  .carousel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
  }

  .carousel-controls {
    align-self: stretch;
    justify-content: space-between;
  }

  .carousel-indicators {
    gap: 12px;
    padding: 12px;
  }

  .indicator {
    min-width: 70px;
    padding: 10px 12px;
  }

  .indicator-icon {
    width: 28px;
    height: 28px;
    font-size: 13px;
  }

  .indicator-label {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .top-info-bar {
    padding: 16px 20px;
  }

  .case-info-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .info-group {
    width: 100%;
    padding: 14px 16px;
    font-size: 14px;
  }

  .info-icon {
    font-size: 16px;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value {
    font-size: 14px;
  }

  .carousel-header {
    padding: 12px 16px;
  }

  .carousel-title {
    font-size: 18px;
  }

  .carousel-progress {
    font-size: 14px;
    padding: 4px 8px;
  }

  .carousel-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .carousel-indicators {
    gap: 8px;
    padding: 8px;
    overflow-x: auto;
  }

  .indicator {
    min-width: 60px;
    padding: 8px 10px;
  }

  .indicator-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .indicator-label {
    font-size: 10px;
  }

  .panel-title {
    font-size: 20px;
  }

  .panel-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .carousel-header {
    padding: 10px 12px;
  }

  .carousel-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .carousel-title {
    font-size: 16px;
  }

  .carousel-controls {
    gap: 8px;
  }

  .carousel-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .carousel-indicators {
    gap: 6px;
    padding: 6px;
  }

  .indicator {
    min-width: 50px;
    padding: 6px 8px;
  }

  .indicator-icon {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }

  .indicator-label {
    font-size: 9px;
  }
}
