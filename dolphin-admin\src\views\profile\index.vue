<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <!-- 左侧用户信息卡片 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="6">
        <el-card class="user-info-card">
          <!-- 用户头像区域 -->
          <div class="avatar-section">
            <div class="avatar-wrapper">
              <UserAvatar
                ref="userAvatarRef"
                :avatar="userStore.avatar"
                :size="120"
                :clickable="true"
                class="user-avatar"
                @upload-success="handleAvatarUploadSuccess"
                @upload-error="handleAvatarUploadError"
              />
            </div>
            <h3 class="username">{{ userStore.username }}</h3>
            <p class="nickname">{{ userStore.nickname || '未设置昵称' }}</p>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息详情 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="18">
        <el-card class="info-card">
          <!-- 账号信息 -->
          <div class="info-section">
            <div class="section-header">
              <h3>账号信息</h3>
              <el-button
                type="primary"
                size="small"
                @click="handleEditToggle"
                :icon="editMode ? 'Check' : 'Edit'"
                :loading="editLoading"
              >
                {{ editMode ? '保存' : '修改' }}
              </el-button>
            </div>

            <div class="info-grid">
              <div class="info-item">
                <label>用户名</label>
                <div class="info-value">
                  <span v-if="!editMode">{{ userStore.username }}</span>
                  <el-input v-else v-model="editForm.username" size="small" disabled />
                </div>
              </div>

              <div class="info-item">
                <label>昵称</label>
                <div class="info-value">
                  <span v-if="!editMode">{{ userStore.nickname || '未设置' }}</span>
                  <el-input v-else v-model="editForm.nickname" size="small" />
                </div>
              </div>

              <div class="info-item">
                <label>性别</label>
                <div class="info-value">
                  <span v-if="!editMode">{{ getSexText(userStore.sex) }}</span>
                  <el-select v-else v-model="editForm.sex" size="small" style="width: 100%">
                    <el-option label="男" :value="1" />
                    <el-option label="女" :value="0" />
                    <el-option label="未设置" :value="-1" />
                  </el-select>
                </div>
              </div>

              <div class="info-item">
                <label>手机号</label>
                <div class="info-value">
                  <span v-if="!editMode">{{ userStore.phone || '未设置' }}</span>
                  <el-input v-else v-model="editForm.phone" size="small" />
                </div>
              </div>

              <div class="info-item">
                <label>邮箱</label>
                <div class="info-value">
                  <span v-if="!editMode">{{ userStore.email || '未设置' }}</span>
                  <el-input v-else v-model="editForm.email" size="small" />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import { reqEditUserInfo, reqUserInfo } from '@/api/user'
import type { editUserInfoForm, userResponseData } from '@/api/user/type'

defineOptions({
  name: 'Profile',
})

const userStore = useUserStore()

// 组件引用
const userAvatarRef = ref()

// 响应式数据
const editMode = ref(false)
const editLoading = ref(false)

// 编辑表单
const editForm = reactive({
  username: '',
  nickname: '',
  sex: -1,
  phone: '',
  email: '',
})

// 方法
const getSexText = (sex: number) => {
  switch (sex) {
    case 1:
      return '男'
    case 0:
      return '女'
    case -1:
    default:
      return '未设置'
  }
}

const handleEditToggle = async () => {
  if (editMode.value) {
    // 保存模式
    try {
      editLoading.value = true

      // 检查用户ID是否存在
      if (!userStore.id) {
        ElMessage.error('用户ID不存在，请重新登录')
        return
      }

      // 构建要提交的数据，包含用户ID和需要修改的字段
      const data: editUserInfoForm = {
        id: userStore.id,
        nickname: editForm.nickname,
        sex: editForm.sex,
        phone: editForm.phone,
        email: editForm.email,
      }

      // 使用新的API接口 /api/v1/user/info/edit
      await reqEditUserInfo(data)

      // 更新用户store中的信息
      userStore.nickname = editForm.nickname
      userStore.sex = editForm.sex
      userStore.phone = editForm.phone
      userStore.email = editForm.email

      ElMessage.success('信息更新成功!')
      editMode.value = false
    } catch (error) {
      console.error('❌ 信息更新失败:', error)
      ElMessage.error('信息更新失败')
    } finally {
      editLoading.value = false
    }
  } else {
    // 编辑模式
    editMode.value = true
    // 进入编辑模式时，重新初始化表单数据
    initData()
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 调用API获取最新用户信息
    const result = (await reqUserInfo()) as unknown as userResponseData

    // 更新用户store中的信息
    userStore.id = result.id
    userStore.username = result.username
    userStore.nickname = result.nickname || ''
    userStore.sex = result.sex || -1
    userStore.avatar = result.avatar || ''
    userStore.phone = result.phone || ''
    userStore.email = result.email || ''

    // 初始化编辑表单
    initData()
  } catch (error) {
    console.error('❌ 个人中心页面：获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
    // 即使API失败，也要初始化表单数据
    initData()
  }
}

// 初始化数据
const initData = () => {
  editForm.username = userStore.username
  editForm.nickname = userStore.nickname || ''
  editForm.sex = userStore.sex || -1
  editForm.phone = userStore.phone || ''
  editForm.email = userStore.email || ''
}

// 处理头像上传成功
const handleAvatarUploadSuccess = async (avatarId: string) => {
  try {
    // 更新用户store中的头像ID
    userStore.avatar = avatarId

    // 清除头像缓存并重新加载头像
    if (userAvatarRef.value?.clearCacheAndReload) {
      await userAvatarRef.value.clearCacheAndReload()
    }

    // 重新获取用户信息以确保数据同步
    await fetchUserInfo()

    ElMessage.success('头像更新成功')
  } catch (error) {
    console.error('❌ 更新头像信息失败:', error)
    ElMessage.error('头像信息更新失败')
  }
}

// 处理头像上传失败
const handleAvatarUploadError = (error: Error) => {
  console.error('❌ 头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

onMounted(() => {
  // 检查用户信息是否已存在，如果不存在才调用API
  if (!userStore.username || !userStore.id) {
    fetchUserInfo()
  } else {
    // 直接使用store中的数据初始化表单
    initData()
  }
})
</script>

<style scoped lang="scss">
.profile-container {
  padding: 20px;

  .user-info-card {
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;

      .avatar-wrapper {
        position: relative;
        margin-bottom: 15px;

        .user-avatar {
          border: 4px solid #fff;
          // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
      }

      .username {
        font-size: 20px;
        font-weight: 600;
        margin: 0 0 5px;
      }

      .nickname {
        font-size: 14px;
        color: #909399;
        margin: 0;
      }
    }
  }

  .info-card {
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    padding: 7px; // 增加内边距来提高卡片高度

    .info-section {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .info-item {
          display: flex;
          flex-direction: column;

          label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
          }

          .info-value {
            display: flex;
            align-items: center;

            span {
              font-size: 16px;
              color: #303133;
            }

            .el-button {
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
