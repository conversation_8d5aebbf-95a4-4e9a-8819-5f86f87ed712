<template>
  <div class="annotation-panel-wrapper">
    <ModernPanel title="标注信息" :icon="Location" size="default" :bordered="true" position="right" :scrollable="true">
      <div class="annotation-info-panel">
        <div class="panel-content">
          <!-- 空状态 -->
          <div v-if="exportedLesions.length === 0" class="empty-annotations">
            <div class="empty-icon">
              <el-icon><Pointer /></el-icon>
            </div>
            <p class="empty-text">暂无病灶信息</p>
            <p class="empty-hint">填写病灶信息并标注后点击"导出"按钮</p>
          </div>

          <!-- 已导出病灶列表 -->
          <div v-else class="lesions-list">
            <div class="list-header">
              <span class="lesion-count">已导出 {{ exportedLesions.length }} 个病灶</span>
            </div>

            <div class="lesion-items">
              <div
                v-for="(lesion, index) in exportedLesions"
                :key="lesion.id"
                class="lesion-item"
                :class="{ 'lesion-item--selected': selectedLesionId === lesion.id }"
                @click="handleLesionClick(lesion, index)"
              >
                <!-- 病灶标题 -->
                <div class="lesion-header">
                  <div class="lesion-indicator" :style="{ backgroundColor: lesion.color }"></div>
                  <span class="lesion-title">病灶 {{ index + 1 }}</span>
                  <div class="lesion-actions">
                    <el-button
                      type="danger"
                      size="small"
                      text
                      @click.stop="handleDeleteLesion(lesion.id, lesion.color)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>

                <!-- 病灶信息 -->
                <div class="lesion-info">
                  <div class="info-item">
                    <span class="info-label">病灶信息：</span>
                    <span class="info-content">{{ lesion.description }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">病灶位置：</span>
                    <span class="info-content">
                      <span v-for="(point, pointIndex) in lesion.points" :key="point.id" class="point-coord">
                        ({{ Math.round(point.x) }},{{ Math.round(point.y) }}){{
                          pointIndex < lesion.points.length - 1 ? ' ' : ''
                        }}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModernPanel>

    <!-- 外侧固定底部保存按钮 -->
    <div class="external-footer">
      <el-tooltip content="导出所有已标注的病灶信息" placement="top" :show-after="300">
        <el-button
          type="primary"
          size="large"
          class="save-all-btn"
          :disabled="exportedLesions.length === 0"
          @click="handleSaveAllLesions"
        >
          导出标注
        </el-button>
      </el-tooltip>
    </div>

    <!-- 病灶详细信息弹窗 -->
    <LesionDetailDialog
      v-model:visible="detailDialogVisible"
      :lesion="selectedLesionForDetail"
      :lesion-index="selectedLesionIndex"
      @delete-lesion="handleDeleteLesionFromDialog"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Location, Pointer } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ModernPanel from '../common/ModernPanel.vue'
import LesionDetailDialog from './LesionDetailDialog.vue'
import useImageStore from '@/store/modules/image'

// 定义组件名称
defineOptions({
  name: 'AnnotationInfoPanel',
})

// 获取状态管理实例
const imageStore = useImageStore()

// 响应式数据
const detailDialogVisible = ref(false)
const selectedLesionForDetail = ref(null)
const selectedLesionIndex = ref(0)

// 定义 Events
const emit = defineEmits(['lesion-click', 'lesion-deleted', 'save-all-lesions', 'delete-lesion-from-canvas'])

// 计算属性
const exportedLesions = computed(() => {
  return imageStore.getExportedLesions()
})

const selectedLesionId = computed(() => {
  return imageStore.selectedLesionId
})

// 处理病灶点击 - 显示详细信息弹窗
const handleLesionClick = (lesion, index) => {
  imageStore.setSelectedLesion(lesion.id)
  selectedLesionForDetail.value = lesion
  selectedLesionIndex.value = index
  detailDialogVisible.value = true
  emit('lesion-click', lesion)
}

// 处理删除病灶
const handleDeleteLesion = async (lesionId, lesionColor) => {
  try {
    await ElMessageBox.confirm('确定要删除这个病灶标注吗？此操作将同时清除画布上的标注区域。', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const success = imageStore.deleteLesion(lesionId)
    if (success) {
      ElMessage.success('病灶删除成功')
      // 通知父组件删除画布上的标注区域
      emit('delete-lesion-from-canvas', { lesionId, color: lesionColor })
      emit('lesion-deleted', lesionId)
    } else {
      ElMessage.error('删除失败')
    }
  } catch {
    // 用户取消删除
  }
}

// 处理从详情弹窗删除病灶
const handleDeleteLesionFromDialog = async (lesionId) => {
  const lesion = imageStore.getLesionById(lesionId)
  if (lesion) {
    const success = imageStore.deleteLesion(lesionId)
    if (success) {
      ElMessage.success('病灶删除成功')
      // 通知父组件删除画布上的标注区域
      emit('delete-lesion-from-canvas', { lesionId, color: lesion.color })
      emit('lesion-deleted', lesionId)
    } else {
      ElMessage.error('删除失败')
    }
  }
}

// 处理保存所有病灶
const handleSaveAllLesions = () => {
  emit('save-all-lesions', exportedLesions.value)
}
</script>

<style scoped>
/* 外层包装器 */
.annotation-panel-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.annotation-info-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-height: 0; /* 重要：允许flex子项收缩 */
  max-height: calc(100vh - 150px); /* 增加可视区域高度 */

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.panel-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 空状态 */
.empty-annotations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.empty-hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 病灶列表 */
.lesions-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.list-header {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  flex-shrink: 0;
}

.lesion-count {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.lesion-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* 减少项目间距 */
  margin-bottom: 1rem;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  max-height: calc(100vh - 250px); /* 增加可视区域，显示更多病灶 */

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.lesion-items::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 病灶项目 - 更紧凑的布局 */
.lesion-item {
  padding: 0.75rem; /* 减少内边距 */
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.lesion-item:hover {
  border-color: var(--medical-blue);
  box-shadow: var(--shadow-light);
}

.lesion-item--selected {
  border-color: var(--medical-blue);
  background: var(--bg-hover);
}

/* 病灶标题 */
.lesion-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.lesion-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.lesion-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.lesion-actions {
  display: flex;
  gap: 0.25rem;
}

/* 病灶信息 */
.lesion-info {
  font-size: 0.875rem;
  line-height: 1.4;
}

.info-item {
  margin-bottom: 0.5rem;
  word-break: break-word;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: var(--text-primary);
  display: inline-block;
  min-width: 70px;
}

.info-content {
  color: var(--text-secondary);
}

.point-coord {
  background: var(--bg-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: monospace;
  color: var(--text-primary);
}

.more-points {
  color: var(--text-secondary);
  font-style: italic;
}

/* 外侧固定底部按钮区域 - 无浮动效果 */
.external-footer {
  flex-shrink: 0;
  padding: 0.75rem 1rem;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-primary);
}

/* 保存所有标注按钮 - 无浮动效果 */
.save-all-btn {
  width: 100%;
  height: 44px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  background: linear-gradient(to right, #1d4ed8 0%, #3b82f6 50%, #60a5fa 100%);
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

.save-all-btn:hover {
  background: linear-gradient(to right, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
}

.save-all-btn:active {
  background: linear-gradient(to right, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);
}

.save-all-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}
</style>
