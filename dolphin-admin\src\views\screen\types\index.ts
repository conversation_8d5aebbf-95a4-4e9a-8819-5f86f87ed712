// 医学数据采集相关类型定义

export interface MedicalFormData {
  imageFile: File | null
  fileName: string
  imagePreview: string
  imageIds: string[]       // 图像上传后返回的ID数组 (对应后端 images 字段)

  examPart: string         // 检查部位
  sliceType: string        // 切面类型
  location: string[]      // 标注位置 (来自标注)
  caption: string[]        // 特征分析 (对应后端 caption)
  associate: string[]      // 诊断联想 (对应后端 associate)
  basis: string[]          // 诊断依据 (对应后端 basis)
  classification: string   // 诊断分类 (对应后端 classification)
  detail: string           // 详细诊断 (对应后端 detail)
  remark: string           // 备注 (对应后端 remark)
  report: string           // 病史 (对应后端 report)

  // 兼容旧字段名 (已废弃，保留用于向后兼容)
  /** @deprecated 请使用 caption */
  imageFeatures?: string[]
  /** @deprecated 请使用 associate */
  diagnosticThinking?: string[]
  /** @deprecated 请使用 basis */
  diagnosticBasis?: string[]
  /** @deprecated 请使用 classification */
  diagnosisType?: string
  /** @deprecated 请使用 detail */
  detailedDiagnosis?: string
}

export interface TagInputVisible {
  features: boolean
  thinking: boolean
  basis: boolean
}

export interface TagInputValue {
  features: string
  thinking: string
  basis: string
}

export interface PresetTags {
  features: string[]
  thinking: string[]
  basis: string[]
}

// 图像上传组件的 Props
export interface ImageUploaderProps {
  modelValue?: File | null
  imagePreview?: string
  fileName?: string
  accept?: string
  maxSize?: number // MB
  disabled?: boolean
}

// 图像上传组件的 Emits
export interface ImageUploaderEmits {
  'update:modelValue': [file: File | null]
  'update:imagePreview': [preview: string]
  'update:fileName': [name: string]
  'upload-success': [file: File]
  'upload-error': [error: Error]
}

// 标签管理组件的 Props
export interface TagManagerProps {
  modelValue: string[]
  type: 'features' | 'thinking' | 'basis'
  title: string
  placeholder: string
  presetTags: string[]
  tagType?: 'success' | 'primary' | 'warning' | 'danger' | 'info'
  disabled?: boolean
}

// 标签管理组件的 Emits
export interface TagManagerEmits {
  'update:modelValue': [tags: string[]]
}

// 数据预览组件的 Props
export interface DataPreviewProps {
  visible: boolean
  formData: MedicalFormData // 这里会自动继承上面 MedicalFormData 的新增字段
  diagnosisTypeText: string
}

// 数据预览组件的 Emits
export interface DataPreviewEmits {
  'update:visible': [visible: boolean]
}

// 提交数据的格式 (对应后端 DiagnoseDto)
// 这个接口也需要更新，以匹配你提交到后端的数据结构
export interface SubmitData {
  fileName: string
  caption: string[]        // 特征分析
  associate: string[]      // 诊断联想
  basis: string[]          // 诊断依据
  classification: string   // 诊断分类
  detail: string           // 详细诊断
  remark: string           // 备注         // [新增]
  report: string           // 病史         // [新增]
  part?: string           // 检查部位
  section?: string        // 切面类型
  timestamp: string

  // 兼容旧字段 (已废弃)
  /** @deprecated 请使用 caption */
  imageFeatures?: string[]
  /** @deprecated 请使用 associate */
  diagnosticThinking?: string[]
  /** @deprecated 请使用 basis */
  diagnosticBasis?: string[]
  /** @deprecated 请使用 classification */
  diagnosisType?: string
  /** @deprecated 已移除 */
  diagnosisTypeText?: string
  /** @deprecated 请使用 detail */
  detailedDiagnosis?: string
}

