<template>
  <el-card class="hot-search-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">
          <el-icon><Search /></el-icon>
          热搜索
        </span>
        <el-button type="text" size="small" @click="refreshData">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>

    <div class="word-cloud-container" ref="wordCloudRef">
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-else class="word-cloud">
        <span
          v-for="(word, index) in wordCloudData"
          :key="index"
          class="word-item"
          :class="`level-${word.level}`"
          :style="{
            fontSize: word.size + 'px',
            color: word.color,
            transform: `rotate(${word.rotation}deg)`,
            left: word.x + '%',
            top: word.y + '%',
          }"
          @click="handleWordClick(word)"
        >
          {{ word.text }}
        </span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { Search, Refresh, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 词云数据接口
interface WordCloudItem {
  text: string
  weight: number
  size: number
  color: string
  level: number
  rotation: number
  x: number
  y: number
}

// 响应式数据
const loading = ref(false)
const wordCloudRef = ref<HTMLElement>()
const wordCloudData = ref<WordCloudItem[]>([])

// 医学超声相关的热搜词汇
const medicalUltrasoundTerms = [
  { text: '心脏超声', weight: 100 },
  { text: '腹部超声', weight: 95 },
  { text: '甲状腺超声', weight: 90 },
  { text: '血管超声', weight: 85 },
  { text: '妇科超声', weight: 80 },
  { text: '产科超声', weight: 75 },
  { text: '肝脏超声', weight: 70 },
  { text: '胆囊超声', weight: 65 },
  { text: '肾脏超声', weight: 60 },
  { text: '膀胱超声', weight: 55 },
  { text: '乳腺超声', weight: 50 },
  { text: '颈动脉超声', weight: 45 },
  { text: '下肢血管', weight: 40 },
  { text: '胎儿超声', weight: 38 },
  { text: '心功能评估', weight: 35 },
  { text: '肝硬化', weight: 32 },
  { text: '胆结石', weight: 30 },
  { text: '肾结石', weight: 28 },
  { text: '甲状腺结节', weight: 25 },
  { text: '乳腺肿块', weight: 22 },
  { text: '子宫肌瘤', weight: 20 },
  { text: '卵巢囊肿', weight: 18 },
  { text: '前列腺', weight: 15 },
  { text: '脾脏', weight: 12 },
  { text: '胰腺', weight: 10 },
  { text: '淋巴结', weight: 8 },
  { text: '软组织', weight: 6 },
  { text: '肌肉骨骼', weight: 5 },
  { text: '眼部超声', weight: 4 },
  { text: '小器官', weight: 3 },
]

// 颜色配置
const colors = [
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#36CFC9',
  '#722ED1',
  '#EB2F96',
  '#FA541C',
  '#13C2C2',
  '#52C41A',
  '#1890FF',
  '#FAAD14',
  '#F5222D',
  '#722ED1',
]

// 生成词云数据
const generateWordCloud = () => {
  const container = wordCloudRef.value
  if (!container) return

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  // 根据权重计算字体大小
  const maxWeight = Math.max(...medicalUltrasoundTerms.map((item) => item.weight))
  const minWeight = Math.min(...medicalUltrasoundTerms.map((item) => item.weight))

  wordCloudData.value = medicalUltrasoundTerms.map((term, index) => {
    // 计算字体大小 (12-32px)
    const sizeRatio = (term.weight - minWeight) / (maxWeight - minWeight)
    const size = 12 + sizeRatio * 20

    // 确定等级
    let level = 1
    if (term.weight >= 80) level = 5
    else if (term.weight >= 60) level = 4
    else if (term.weight >= 40) level = 3
    else if (term.weight >= 20) level = 2

    // 随机颜色
    const color = colors[index % colors.length]

    // 随机旋转角度 (-15度到15度)
    const rotation = (Math.random() - 0.5) * 30

    // 随机位置 (避免重叠的简单算法)
    const x = Math.random() * 80 + 10 // 10%-90%
    const y = Math.random() * 70 + 15 // 15%-85%

    return {
      text: term.text,
      weight: term.weight,
      size,
      color,
      level,
      rotation,
      x,
      y,
    }
  })
}

// 处理词汇点击
const handleWordClick = (word: WordCloudItem) => {
  ElMessage.success(`搜索: ${word.text}`)
  // 这里可以添加实际的搜索逻辑
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))
    await nextTick()
    generateWordCloud()
  } catch (error) {
    console.error('刷新热搜数据失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载后生成词云
onMounted(async () => {
  await nextTick()
  generateWordCloud()
})

// 暴露刷新方法
defineExpose({
  refreshData,
})
</script>

<style scoped lang="scss">
.hot-search-card {
  height: 400px;
  border-radius: 8px;
  border: 1px solid var(--border-primary);

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-primary);
  }

  :deep(.el-card__body) {
    padding: 0;
    height: calc(100% - 57px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);

    .el-icon {
      color: #409eff;
    }
  }
}

.word-cloud-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: var(--bg-secondary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  gap: 8px;

  .el-icon {
    font-size: 24px;
  }
}

.word-cloud {
  width: 100%;
  height: 100%;
  position: relative;
}

.word-item {
  position: absolute;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: scale(1.1) !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
  }

  &.level-5 {
    font-weight: 700;
    opacity: 1;
  }

  &.level-4 {
    font-weight: 600;
    opacity: 0.9;
  }

  &.level-3 {
    font-weight: 500;
    opacity: 0.8;
  }

  &.level-2 {
    font-weight: 400;
    opacity: 0.7;
  }

  &.level-1 {
    font-weight: 300;
    opacity: 0.6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hot-search-card {
    height: 300px;
  }

  .word-item {
    font-size: 12px !important;
  }
}
</style>
