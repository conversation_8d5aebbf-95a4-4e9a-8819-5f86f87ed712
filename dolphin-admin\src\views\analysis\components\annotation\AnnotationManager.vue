<template>
  <div class="annotation-manager">
    <!-- 这是一个逻辑组件，不渲染UI -->
  </div>
</template>

<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAnnotationLogic } from '../../composables/useAnnotationLogic.js'
import { useCoordinateTransform } from '../../composables/useCoordinateTransform.js'
import { useConvexHull } from '../../composables/useConvexHull.js'

import { exportCanvasAsFile, getCanvasPreviewDataURL, hasAnnotations } from '@/utils/canvasUtils'
import { reqUploadImage } from '@/api/data'
import useImageStore from '@/store/modules/image'

// 定义组件名称
defineOptions({
  name: 'AnnotationManager',
})

// 初始化路由和状态管理
const router = useRouter()
const imageStore = useImageStore()

// 定义 Props
const props = defineProps({
  canvasRef: {
    type: Object,
    default: null,
  },
  annotationMode: {
    type: String,
    default: 'draw-point',
  },
  canvasWidth: {
    type: Number,
    default: 720,
  },
  canvasHeight: {
    type: Number,
    default: 720,
  },
})

// 定义 Events
const emit = defineEmits(['annotations-updated', 'temp-annotations-updated', 'annotation-saved', 'annotation-undone'])

// 使用composables
const {
  annotations,
  tempAnnotations,
  selectedAnnotation,
  addTempAnnotation,
  saveAnnotations,
  undoLastAnnotation,
  clearAnnotations,
  clearTempAnnotations,
} = useAnnotationLogic()

const { convertToDisplayCoordinates } = useCoordinateTransform(props.canvasHeight)

const { computeConvexHull } = useConvexHull()

// 监听临时标注变化
watch(
  tempAnnotations,
  (newValue) => {
    emit('temp-annotations-updated', newValue)
  },
  { deep: true },
)

// 监听已保存标注变化
watch(
  annotations,
  (newValue) => {
    emit('annotations-updated', newValue)
  },
  { deep: true },
)

// 处理标注创建
const handleAnnotationCreated = (annotation) => {
  addTempAnnotation(annotation)
}

// 处理保存标注
const handleSaveAnnotations = async () => {
  if (tempAnnotations.value.length === 0) {
    ElMessage.warning('没有标注数据可保存')
    return
  }

  try {
    // 转换坐标并保存到内存
    const savedAnnotations = tempAnnotations.value.map((annotation, index) => {
      const displayCoords = convertToDisplayCoordinates(annotation)

      return {
        id: annotation.id || `point_${index + 1}`,
        index: index + 1,
        x: Math.round(displayCoords.x),
        y: Math.round(displayCoords.y),
        originalAnnotation: annotation,
        fabricX: displayCoords.fabricX,
        fabricY: displayCoords.fabricY,
      }
    })

    // 创建透明区域
    createAnnotationRegion(savedAnnotations)

    // 保存到内存状态
    saveAnnotations(savedAnnotations)

    // 注意：在新的多病灶流程中，标注数据通过导出病灶的方式管理
    // 这里的保存主要是为了兼容旧的标注流程

    // 获取原始图像数据（用于标注）
    const originalImageData = imageStore.getOriginalCroppedImage()

    if (!originalImageData || !originalImageData.id) {
      ElMessage.error('无法获取原始图像信息，保存失败')
      return
    }

    // 获取Canvas实例
    const canvas = props.canvasRef?.getCanvas()

    if (!canvas) {
      ElMessage.error('无法获取Canvas实例，保存失败')
      return
    }

    // 检查Canvas是否包含标注
    if (!hasAnnotations(canvas)) {
      ElMessage.warning('Canvas中没有检测到标注内容')
      return
    }

    // 显示处理中消息
    ElMessage.info('正在导出标注图片并上传...')

    // 导出Canvas为带标注的图片文件
    const annotatedFile = await exportCanvasAsFile(canvas, `annotated_${originalImageData.name}`, 1.0)

    // 获取预览DataURL
    const newImagePreview = getCanvasPreviewDataURL(canvas, 0.8)

    // 重新上传标注后的图片
    const newImageId = await reqUploadImage(annotatedFile)

    const originalImageId = String(originalImageData.id)

    // 创建标注后图片数据
    const annotatedImageData = {
      imageId: newImageId,
      previewUrl: newImagePreview,
      file: annotatedFile,
      originalImageId,
      timestamp: Date.now(),
    }

    // 保存标注后图片数据到状态管理（不影响原始图片）
    imageStore.setLatestAnnotatedImage(annotatedImageData)

    // 触发保存事件
    emit('annotation-saved', savedAnnotations)

    // 显示成功消息
    ElMessage.success('标注图片已上传，正在返回数据采集页面...')

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/screen/collection')
    }, 1500)
  } catch (error) {
    console.error('❌ 保存标注数据失败:', error)
    ElMessage.error('保存标注数据失败，请重试')
  }
}

// 处理撤销标注
const handleUndoLastAnnotation = () => {
  const undoneAnnotation = undoLastAnnotation()

  if (undoneAnnotation && props.canvasRef) {
    // 从画布中移除对应的标注对象
    const canvas = props.canvasRef.getCanvas()
    if (canvas) {
      const objects = canvas.getObjects()
      const targetObject = objects.find((obj) => obj.id === undoneAnnotation.id)
      if (targetObject) {
        canvas.remove(targetObject)
        canvas.renderAll()
      }
    }
  }

  emit('annotation-undone', undoneAnnotation)
}

// 创建标注区域（使用凸包算法）
const createAnnotationRegion = (annotationPoints) => {
  if (!props.canvasRef || annotationPoints.length < 3) return

  // 准备点坐标数组
  const points = annotationPoints.map((point) => ({
    x: point.fabricX,
    y: point.fabricY,
  }))

  // 计算凸包
  const convexHullPoints = computeConvexHull(points)

  // 调用Canvas组件的方法创建区域
  props.canvasRef.createAnnotationRegion(convexHullPoints)
}

// 暴露给父组件的方法
defineExpose({
  // 数据
  annotations,
  tempAnnotations,
  selectedAnnotation,

  // 方法
  handleAnnotationCreated,
  handleSaveAnnotations,
  handleUndoLastAnnotation,
  clearAnnotations,
  clearTempAnnotations,
})
</script>

<style scoped>
.annotation-manager {
  display: none;
}
</style>
