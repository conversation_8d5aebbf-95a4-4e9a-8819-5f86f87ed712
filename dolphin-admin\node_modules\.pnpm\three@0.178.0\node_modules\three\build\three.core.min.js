/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
const t="178",e={LEFT:0,MIDDLE:1,RIGHT:2,ROTATE:0,DOLLY:1,PAN:2},i={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},s=0,r=1,n=2,a=3,o=0,h=1,l=2,c=3,u=0,d=1,p=2,m=0,y=1,g=2,f=3,x=4,b=5,v=100,w=101,M=102,S=103,_=104,A=200,T=201,z=202,I=203,C=204,B=205,k=206,E=207,R=208,P=209,O=210,N=211,F=212,V=213,L=214,j=0,W=1,U=2,D=3,H=4,q=5,J=6,X=7,Y=0,Z=1,G=2,$=0,Q=1,K=2,tt=3,et=4,it=5,st=6,rt=7,nt="attached",at="detached",ot=300,ht=301,lt=302,ct=303,ut=304,dt=306,pt=1e3,mt=1001,yt=1002,gt=1003,ft=1004,xt=1004,bt=1005,vt=1005,wt=1006,Mt=1007,St=1007,_t=1008,At=1008,Tt=1009,zt=1010,It=1011,Ct=1012,Bt=1013,kt=1014,Et=1015,Rt=1016,Pt=1017,Ot=1018,Nt=1020,Ft=35902,Vt=1021,Lt=1022,jt=1023,Wt=1026,Ut=1027,Dt=1028,Ht=1029,qt=1030,Jt=1031,Xt=1032,Yt=1033,Zt=33776,Gt=33777,$t=33778,Qt=33779,Kt=35840,te=35841,ee=35842,ie=35843,se=36196,re=37492,ne=37496,ae=37808,oe=37809,he=37810,le=37811,ce=37812,ue=37813,de=37814,pe=37815,me=37816,ye=37817,ge=37818,fe=37819,xe=37820,be=37821,ve=36492,we=36494,Me=36495,Se=36283,_e=36284,Ae=36285,Te=36286,ze=2200,Ie=2201,Ce=2202,Be=2300,ke=2301,Ee=2302,Re=2400,Pe=2401,Oe=2402,Ne=2500,Fe=2501,Ve=0,Le=1,je=2,We=3200,Ue=3201,De=3202,He=3203,qe=0,Je=1,Xe="",Ye="srgb",Ze="srgb-linear",Ge="linear",$e="srgb",Qe=0,Ke=7680,ti=7681,ei=7682,ii=7683,si=34055,ri=34056,ni=5386,ai=512,oi=513,hi=514,li=515,ci=516,ui=517,di=518,pi=519,mi=512,yi=513,gi=514,fi=515,xi=516,bi=517,vi=518,wi=519,Mi=35044,Si=35048,_i=35040,Ai=35045,Ti=35049,zi=35041,Ii=35046,Ci=35050,Bi=35042,ki="100",Ei="300 es",Ri=2e3,Pi=2001,Oi={COMPUTE:"compute",RENDER:"render"},Ni={PERSPECTIVE:"perspective",LINEAR:"linear",FLAT:"flat"},Fi={NORMAL:"normal",CENTROID:"centroid",SAMPLE:"sample",FIRST:"first",EITHER:"either"};class Vi{addEventListener(t,e){void 0===this._listeners&&(this._listeners={});const i=this._listeners;void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e)}hasEventListener(t,e){const i=this._listeners;return void 0!==i&&(void 0!==i[t]&&-1!==i[t].indexOf(e))}removeEventListener(t,e){const i=this._listeners;if(void 0===i)return;const s=i[t];if(void 0!==s){const t=s.indexOf(e);-1!==t&&s.splice(t,1)}}dispatchEvent(t){const e=this._listeners;if(void 0===e)return;const i=e[t.type];if(void 0!==i){t.target=this;const e=i.slice(0);for(let i=0,s=e.length;i<s;i++)e[i].call(this,t);t.target=null}}}const Li=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"];let ji=1234567;const Wi=Math.PI/180,Ui=180/Math.PI;function Di(){const t=4294967295*Math.random()|0,e=4294967295*Math.random()|0,i=4294967295*Math.random()|0,s=4294967295*Math.random()|0;return(Li[255&t]+Li[t>>8&255]+Li[t>>16&255]+Li[t>>24&255]+"-"+Li[255&e]+Li[e>>8&255]+"-"+Li[e>>16&15|64]+Li[e>>24&255]+"-"+Li[63&i|128]+Li[i>>8&255]+"-"+Li[i>>16&255]+Li[i>>24&255]+Li[255&s]+Li[s>>8&255]+Li[s>>16&255]+Li[s>>24&255]).toLowerCase()}function Hi(t,e,i){return Math.max(e,Math.min(i,t))}function qi(t,e){return(t%e+e)%e}function Ji(t,e,i){return(1-i)*t+i*e}function Xi(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return t/4294967295;case Uint16Array:return t/65535;case Uint8Array:return t/255;case Int32Array:return Math.max(t/2147483647,-1);case Int16Array:return Math.max(t/32767,-1);case Int8Array:return Math.max(t/127,-1);default:throw new Error("Invalid component type.")}}function Yi(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return Math.round(4294967295*t);case Uint16Array:return Math.round(65535*t);case Uint8Array:return Math.round(255*t);case Int32Array:return Math.round(2147483647*t);case Int16Array:return Math.round(32767*t);case Int8Array:return Math.round(127*t);default:throw new Error("Invalid component type.")}}const Zi={DEG2RAD:Wi,RAD2DEG:Ui,generateUUID:Di,clamp:Hi,euclideanModulo:qi,mapLinear:function(t,e,i,s,r){return s+(t-e)*(r-s)/(i-e)},inverseLerp:function(t,e,i){return t!==e?(i-t)/(e-t):0},lerp:Ji,damp:function(t,e,i,s){return Ji(t,e,1-Math.exp(-i*s))},pingpong:function(t,e=1){return e-Math.abs(qi(t,2*e)-e)},smoothstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)},smootherstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*t*(t*(6*t-15)+10)},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){void 0!==t&&(ji=t);let e=ji+=1831565813;return e=Math.imul(e^e>>>15,1|e),e^=e+Math.imul(e^e>>>7,61|e),((e^e>>>14)>>>0)/4294967296},degToRad:function(t){return t*Wi},radToDeg:function(t){return t*Ui},isPowerOfTwo:function(t){return!(t&t-1)&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,e,i,s,r){const n=Math.cos,a=Math.sin,o=n(i/2),h=a(i/2),l=n((e+s)/2),c=a((e+s)/2),u=n((e-s)/2),d=a((e-s)/2),p=n((s-e)/2),m=a((s-e)/2);switch(r){case"XYX":t.set(o*c,h*u,h*d,o*l);break;case"YZY":t.set(h*d,o*c,h*u,o*l);break;case"ZXZ":t.set(h*u,h*d,o*c,o*l);break;case"XZX":t.set(o*c,h*m,h*p,o*l);break;case"YXY":t.set(h*p,o*c,h*m,o*l);break;case"ZYZ":t.set(h*m,h*p,o*c,o*l);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+r)}},normalize:Yi,denormalize:Xi};class Gi{constructor(t=0,e=0){Gi.prototype.isVector2=!0,this.x=t,this.y=e}get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){const e=this.x,i=this.y,s=t.elements;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=Hi(this.x,t.x,e.x),this.y=Hi(this.y,t.y,e.y),this}clampScalar(t,e){return this.x=Hi(this.x,t,e),this.y=Hi(this.y,t,e),this}clampLength(t,e){const i=this.length();return this.divideScalar(i||1).multiplyScalar(Hi(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(0===e)return Math.PI/2;const i=this.dot(t)/e;return Math.acos(Hi(i,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,i=this.y-t.y;return e*e+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){const i=Math.cos(e),s=Math.sin(e),r=this.x-t.x,n=this.y-t.y;return this.x=r*i-n*s+t.x,this.y=r*s+n*i+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class $i{constructor(t=0,e=0,i=0,s=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=i,this._w=s}static slerpFlat(t,e,i,s,r,n,a){let o=i[s+0],h=i[s+1],l=i[s+2],c=i[s+3];const u=r[n+0],d=r[n+1],p=r[n+2],m=r[n+3];if(0===a)return t[e+0]=o,t[e+1]=h,t[e+2]=l,void(t[e+3]=c);if(1===a)return t[e+0]=u,t[e+1]=d,t[e+2]=p,void(t[e+3]=m);if(c!==m||o!==u||h!==d||l!==p){let t=1-a;const e=o*u+h*d+l*p+c*m,i=e>=0?1:-1,s=1-e*e;if(s>Number.EPSILON){const r=Math.sqrt(s),n=Math.atan2(r,e*i);t=Math.sin(t*n)/r,a=Math.sin(a*n)/r}const r=a*i;if(o=o*t+u*r,h=h*t+d*r,l=l*t+p*r,c=c*t+m*r,t===1-a){const t=1/Math.sqrt(o*o+h*h+l*l+c*c);o*=t,h*=t,l*=t,c*=t}}t[e]=o,t[e+1]=h,t[e+2]=l,t[e+3]=c}static multiplyQuaternionsFlat(t,e,i,s,r,n){const a=i[s],o=i[s+1],h=i[s+2],l=i[s+3],c=r[n],u=r[n+1],d=r[n+2],p=r[n+3];return t[e]=a*p+l*c+o*d-h*u,t[e+1]=o*p+l*u+h*c-a*d,t[e+2]=h*p+l*d+a*u-o*c,t[e+3]=l*p-a*c-o*u-h*d,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,i,s){return this._x=t,this._y=e,this._z=i,this._w=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t,e=!0){const i=t._x,s=t._y,r=t._z,n=t._order,a=Math.cos,o=Math.sin,h=a(i/2),l=a(s/2),c=a(r/2),u=o(i/2),d=o(s/2),p=o(r/2);switch(n){case"XYZ":this._x=u*l*c+h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c-u*d*p;break;case"YXZ":this._x=u*l*c+h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c+u*d*p;break;case"ZXY":this._x=u*l*c-h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c-u*d*p;break;case"ZYX":this._x=u*l*c-h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c+u*d*p;break;case"YZX":this._x=u*l*c+h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c-u*d*p;break;case"XZY":this._x=u*l*c-h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c+u*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+n)}return!0===e&&this._onChangeCallback(),this}setFromAxisAngle(t,e){const i=e/2,s=Math.sin(i);return this._x=t.x*s,this._y=t.y*s,this._z=t.z*s,this._w=Math.cos(i),this._onChangeCallback(),this}setFromRotationMatrix(t){const e=t.elements,i=e[0],s=e[4],r=e[8],n=e[1],a=e[5],o=e[9],h=e[2],l=e[6],c=e[10],u=i+a+c;if(u>0){const t=.5/Math.sqrt(u+1);this._w=.25/t,this._x=(l-o)*t,this._y=(r-h)*t,this._z=(n-s)*t}else if(i>a&&i>c){const t=2*Math.sqrt(1+i-a-c);this._w=(l-o)/t,this._x=.25*t,this._y=(s+n)/t,this._z=(r+h)/t}else if(a>c){const t=2*Math.sqrt(1+a-i-c);this._w=(r-h)/t,this._x=(s+n)/t,this._y=.25*t,this._z=(o+l)/t}else{const t=2*Math.sqrt(1+c-i-a);this._w=(n-s)/t,this._x=(r+h)/t,this._y=(o+l)/t,this._z=.25*t}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let i=t.dot(e)+1;return i<1e-8?(i=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0,this._w=i):(this._x=0,this._y=-t.z,this._z=t.y,this._w=i)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x,this._w=i),this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(Hi(this.dot(t),-1,1)))}rotateTowards(t,e){const i=this.angleTo(t);if(0===i)return this;const s=Math.min(1,e/i);return this.slerp(t,s),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return 0===t?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){const i=t._x,s=t._y,r=t._z,n=t._w,a=e._x,o=e._y,h=e._z,l=e._w;return this._x=i*l+n*a+s*h-r*o,this._y=s*l+n*o+r*a-i*h,this._z=r*l+n*h+i*o-s*a,this._w=n*l-i*a-s*o-r*h,this._onChangeCallback(),this}slerp(t,e){if(0===e)return this;if(1===e)return this.copy(t);const i=this._x,s=this._y,r=this._z,n=this._w;let a=n*t._w+i*t._x+s*t._y+r*t._z;if(a<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,a=-a):this.copy(t),a>=1)return this._w=n,this._x=i,this._y=s,this._z=r,this;const o=1-a*a;if(o<=Number.EPSILON){const t=1-e;return this._w=t*n+e*this._w,this._x=t*i+e*this._x,this._y=t*s+e*this._y,this._z=t*r+e*this._z,this.normalize(),this}const h=Math.sqrt(o),l=Math.atan2(h,a),c=Math.sin((1-e)*l)/h,u=Math.sin(e*l)/h;return this._w=n*c+this._w*u,this._x=i*c+this._x*u,this._y=s*c+this._y*u,this._z=r*c+this._z*u,this._onChangeCallback(),this}slerpQuaternions(t,e,i){return this.copy(t).slerp(e,i)}random(){const t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),i=Math.random(),s=Math.sqrt(1-i),r=Math.sqrt(i);return this.set(s*Math.sin(t),s*Math.cos(t),r*Math.sin(e),r*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t,e=0){return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class Qi{constructor(t=0,e=0,i=0){Qi.prototype.isVector3=!0,this.x=t,this.y=e,this.z=i}set(t,e,i){return void 0===i&&(i=this.z),this.x=t,this.y=e,this.z=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(ts.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(ts.setFromAxisAngle(t,e))}applyMatrix3(t){const e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[3]*i+r[6]*s,this.y=r[1]*e+r[4]*i+r[7]*s,this.z=r[2]*e+r[5]*i+r[8]*s,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){const e=this.x,i=this.y,s=this.z,r=t.elements,n=1/(r[3]*e+r[7]*i+r[11]*s+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*s+r[12])*n,this.y=(r[1]*e+r[5]*i+r[9]*s+r[13])*n,this.z=(r[2]*e+r[6]*i+r[10]*s+r[14])*n,this}applyQuaternion(t){const e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=2*(n*s-a*i),l=2*(a*e-r*s),c=2*(r*i-n*e);return this.x=e+o*h+n*c-a*l,this.y=i+o*l+a*h-r*c,this.z=s+o*c+r*l-n*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){const e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[4]*i+r[8]*s,this.y=r[1]*e+r[5]*i+r[9]*s,this.z=r[2]*e+r[6]*i+r[10]*s,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=Hi(this.x,t.x,e.x),this.y=Hi(this.y,t.y,e.y),this.z=Hi(this.z,t.z,e.z),this}clampScalar(t,e){return this.x=Hi(this.x,t,e),this.y=Hi(this.y,t,e),this.z=Hi(this.z,t,e),this}clampLength(t,e){const i=this.length();return this.divideScalar(i||1).multiplyScalar(Hi(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){const i=t.x,s=t.y,r=t.z,n=e.x,a=e.y,o=e.z;return this.x=s*o-r*a,this.y=r*n-i*o,this.z=i*a-s*n,this}projectOnVector(t){const e=t.lengthSq();if(0===e)return this.set(0,0,0);const i=t.dot(this)/e;return this.copy(t).multiplyScalar(i)}projectOnPlane(t){return Ki.copy(this).projectOnVector(t),this.sub(Ki)}reflect(t){return this.sub(Ki.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(0===e)return Math.PI/2;const i=this.dot(t)/e;return Math.acos(Hi(i,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,i=this.y-t.y,s=this.z-t.z;return e*e+i*i+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,i){const s=Math.sin(e)*t;return this.x=s*Math.sin(i),this.y=Math.cos(e)*t,this.z=s*Math.cos(i),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){const e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){const e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length(),s=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=i,this.z=s,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,4*e)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,3*e)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){const t=Math.random()*Math.PI*2,e=2*Math.random()-1,i=Math.sqrt(1-e*e);return this.x=i*Math.cos(t),this.y=e,this.z=i*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}const Ki=new Qi,ts=new $i;class es{constructor(t,e,i,s,r,n,a,o,h){es.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h)}set(t,e,i,s,r,n,a,o,h){const l=this.elements;return l[0]=t,l[1]=s,l[2]=a,l[3]=e,l[4]=r,l[5]=o,l[6]=i,l[7]=n,l[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){const e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}extractBasis(t,e,i){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),i.setFromMatrix3Column(this,2),this}setFromMatrix4(t){const e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[3],o=i[6],h=i[1],l=i[4],c=i[7],u=i[2],d=i[5],p=i[8],m=s[0],y=s[3],g=s[6],f=s[1],x=s[4],b=s[7],v=s[2],w=s[5],M=s[8];return r[0]=n*m+a*f+o*v,r[3]=n*y+a*x+o*w,r[6]=n*g+a*b+o*M,r[1]=h*m+l*f+c*v,r[4]=h*y+l*x+c*w,r[7]=h*g+l*b+c*M,r[2]=u*m+d*f+p*v,r[5]=u*y+d*x+p*w,r[8]=u*g+d*b+p*M,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){const t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*n*l-e*a*h-i*r*l+i*a*o+s*r*h-s*n*o}invert(){const t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=l*n-a*h,u=a*o-l*r,d=h*r-n*o,p=e*c+i*u+s*d;if(0===p)return this.set(0,0,0,0,0,0,0,0,0);const m=1/p;return t[0]=c*m,t[1]=(s*h-l*i)*m,t[2]=(a*i-s*n)*m,t[3]=u*m,t[4]=(l*e-s*o)*m,t[5]=(s*r-a*e)*m,t[6]=d*m,t[7]=(i*o-h*e)*m,t[8]=(n*e-i*r)*m,this}transpose(){let t;const e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){const e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,i,s,r,n,a){const o=Math.cos(r),h=Math.sin(r);return this.set(i*o,i*h,-i*(o*n+h*a)+n+t,-s*h,s*o,-s*(-h*n+o*a)+a+e,0,0,1),this}scale(t,e){return this.premultiply(is.makeScale(t,e)),this}rotate(t){return this.premultiply(is.makeRotation(-t)),this}translate(t,e){return this.premultiply(is.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,i,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){const e=this.elements,i=t.elements;for(let t=0;t<9;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<9;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){const i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}clone(){return(new this.constructor).fromArray(this.elements)}}const is=new es;function ss(t){for(let e=t.length-1;e>=0;--e)if(t[e]>=65535)return!0;return!1}const rs={Int8Array:Int8Array,Uint8Array:Uint8Array,Uint8ClampedArray:Uint8ClampedArray,Int16Array:Int16Array,Uint16Array:Uint16Array,Int32Array:Int32Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array};function ns(t,e){return new rs[t](e)}function as(t){return document.createElementNS("http://www.w3.org/1999/xhtml",t)}function os(){const t=as("canvas");return t.style.display="block",t}const hs={};function ls(t){t in hs||(hs[t]=!0,console.warn(t))}function cs(t,e,i){return new Promise(function(s,r){setTimeout(function n(){switch(t.clientWaitSync(e,t.SYNC_FLUSH_COMMANDS_BIT,0)){case t.WAIT_FAILED:r();break;case t.TIMEOUT_EXPIRED:setTimeout(n,i);break;default:s()}},i)})}function us(t){const e=t.elements;e[2]=.5*e[2]+.5*e[3],e[6]=.5*e[6]+.5*e[7],e[10]=.5*e[10]+.5*e[11],e[14]=.5*e[14]+.5*e[15]}function ds(t){const e=t.elements;-1===e[11]?(e[10]=-e[10]-1,e[14]=-e[14]):(e[10]=-e[10],e[14]=1-e[14])}const ps=(new es).set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),ms=(new es).set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715);function ys(){const t={enabled:!0,workingColorSpace:Ze,spaces:{},convert:function(t,e,i){return!1!==this.enabled&&e!==i&&e&&i?(this.spaces[e].transfer===$e&&(t.r=fs(t.r),t.g=fs(t.g),t.b=fs(t.b)),this.spaces[e].primaries!==this.spaces[i].primaries&&(t.applyMatrix3(this.spaces[e].toXYZ),t.applyMatrix3(this.spaces[i].fromXYZ)),this.spaces[i].transfer===$e&&(t.r=xs(t.r),t.g=xs(t.g),t.b=xs(t.b)),t):t},workingToColorSpace:function(t,e){return this.convert(t,this.workingColorSpace,e)},colorSpaceToWorking:function(t,e){return this.convert(t,e,this.workingColorSpace)},getPrimaries:function(t){return this.spaces[t].primaries},getTransfer:function(t){return""===t?Ge:this.spaces[t].transfer},getLuminanceCoefficients:function(t,e=this.workingColorSpace){return t.fromArray(this.spaces[e].luminanceCoefficients)},define:function(t){Object.assign(this.spaces,t)},_getMatrix:function(t,e,i){return t.copy(this.spaces[e].toXYZ).multiply(this.spaces[i].fromXYZ)},_getDrawingBufferColorSpace:function(t){return this.spaces[t].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(t=this.workingColorSpace){return this.spaces[t].workingColorSpaceConfig.unpackColorSpace},fromWorkingColorSpace:function(e,i){return ls("THREE.ColorManagement: .fromWorkingColorSpace() has been renamed to .workingToColorSpace()."),t.workingToColorSpace(e,i)},toWorkingColorSpace:function(e,i){return ls("THREE.ColorManagement: .toWorkingColorSpace() has been renamed to .colorSpaceToWorking()."),t.colorSpaceToWorking(e,i)}},e=[.64,.33,.3,.6,.15,.06],i=[.2126,.7152,.0722],s=[.3127,.329];return t.define({[Ze]:{primaries:e,whitePoint:s,transfer:Ge,toXYZ:ps,fromXYZ:ms,luminanceCoefficients:i,workingColorSpaceConfig:{unpackColorSpace:Ye},outputColorSpaceConfig:{drawingBufferColorSpace:Ye}},[Ye]:{primaries:e,whitePoint:s,transfer:$e,toXYZ:ps,fromXYZ:ms,luminanceCoefficients:i,outputColorSpaceConfig:{drawingBufferColorSpace:Ye}}}),t}const gs=ys();function fs(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function xs(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}let bs;class vs{static getDataURL(t,e="image/png"){if(/^data:/i.test(t.src))return t.src;if("undefined"==typeof HTMLCanvasElement)return t.src;let i;if(t instanceof HTMLCanvasElement)i=t;else{void 0===bs&&(bs=as("canvas")),bs.width=t.width,bs.height=t.height;const e=bs.getContext("2d");t instanceof ImageData?e.putImageData(t,0,0):e.drawImage(t,0,0,t.width,t.height),i=bs}return i.toDataURL(e)}static sRGBToLinear(t){if("undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap){const e=as("canvas");e.width=t.width,e.height=t.height;const i=e.getContext("2d");i.drawImage(t,0,0,t.width,t.height);const s=i.getImageData(0,0,t.width,t.height),r=s.data;for(let t=0;t<r.length;t++)r[t]=255*fs(r[t]/255);return i.putImageData(s,0,0),e}if(t.data){const e=t.data.slice(0);for(let t=0;t<e.length;t++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[t]=Math.floor(255*fs(e[t]/255)):e[t]=fs(e[t]);return{data:e,width:t.width,height:t.height}}return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t}}let ws=0;class Ms{constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:ws++}),this.uuid=Di(),this.data=t,this.dataReady=!0,this.version=0}getSize(t){const e=this.data;return e instanceof HTMLVideoElement?t.set(e.videoWidth,e.videoHeight):null!==e?t.set(e.width,e.height,e.depth||0):t.set(0,0,0),t}set needsUpdate(t){!0===t&&this.version++}toJSON(t){const e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.images[this.uuid])return t.images[this.uuid];const i={uuid:this.uuid,url:""},s=this.data;if(null!==s){let t;if(Array.isArray(s)){t=[];for(let e=0,i=s.length;e<i;e++)s[e].isDataTexture?t.push(Ss(s[e].image)):t.push(Ss(s[e]))}else t=Ss(s);i.url=t}return e||(t.images[this.uuid]=i),i}}function Ss(t){return"undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap?vs.getDataURL(t):t.data?{data:Array.from(t.data),width:t.width,height:t.height,type:t.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let _s=0;const As=new Qi;class Ts extends Vi{constructor(t=Ts.DEFAULT_IMAGE,e=Ts.DEFAULT_MAPPING,i=1001,s=1001,r=1006,n=1008,a=1023,o=1009,h=Ts.DEFAULT_ANISOTROPY,l=""){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:_s++}),this.uuid=Di(),this.name="",this.source=new Ms(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=i,this.wrapT=s,this.magFilter=r,this.minFilter=n,this.anisotropy=h,this.format=a,this.internalFormat=null,this.type=o,this.offset=new Gi(0,0),this.repeat=new Gi(1,1),this.center=new Gi(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new es,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=l,this.userData={},this.updateRanges=[],this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.isArrayTexture=!!(t&&t.depth&&t.depth>1),this.pmremVersion=0}get width(){return this.source.getSize(As).x}get height(){return this.source.getSize(As).y}get depth(){return this.source.getSize(As).z}get image(){return this.source.data}set image(t=null){this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}clone(){return(new this.constructor).copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.renderTarget=t.renderTarget,this.isRenderTargetTexture=t.isRenderTargetTexture,this.isArrayTexture=t.isArrayTexture,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}setValues(t){for(const e in t){const i=t[e];if(void 0===i){console.warn(`THREE.Texture.setValues(): parameter '${e}' has value of undefined.`);continue}const s=this[e];void 0!==s?s&&i&&s.isVector2&&i.isVector2||s&&i&&s.isVector3&&i.isVector3||s&&i&&s.isMatrix3&&i.isMatrix3?s.copy(i):this[e]=i:console.warn(`THREE.Texture.setValues(): property '${e}' does not exist.`)}}toJSON(t){const e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];const i={metadata:{version:4.7,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(i.userData=this.userData),e||(t.textures[this.uuid]=i),i}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(this.mapping!==ot)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case pt:t.x=t.x-Math.floor(t.x);break;case mt:t.x=t.x<0?0:1;break;case yt:1===Math.abs(Math.floor(t.x)%2)?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x)}if(t.y<0||t.y>1)switch(this.wrapT){case pt:t.y=t.y-Math.floor(t.y);break;case mt:t.y=t.y<0?0:1;break;case yt:1===Math.abs(Math.floor(t.y)%2)?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){!0===t&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){!0===t&&this.pmremVersion++}}Ts.DEFAULT_IMAGE=null,Ts.DEFAULT_MAPPING=ot,Ts.DEFAULT_ANISOTROPY=1;class zs{constructor(t=0,e=0,i=0,s=1){zs.prototype.isVector4=!0,this.x=t,this.y=e,this.z=i,this.w=s}get width(){return this.z}set width(t){this.z=t}get height(){return this.w}set height(t){this.w=t}set(t,e,i,s){return this.x=t,this.y=e,this.z=i,this.w=s,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this.w=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setW(t){return this.w=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}applyMatrix4(t){const e=this.x,i=this.y,s=this.z,r=this.w,n=t.elements;return this.x=n[0]*e+n[4]*i+n[8]*s+n[12]*r,this.y=n[1]*e+n[5]*i+n[9]*s+n[13]*r,this.z=n[2]*e+n[6]*i+n[10]*s+n[14]*r,this.w=n[3]*e+n[7]*i+n[11]*s+n[15]*r,this}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this.w/=t.w,this}divideScalar(t){return this.multiplyScalar(1/t)}setAxisAngleFromQuaternion(t){this.w=2*Math.acos(t.w);const e=Math.sqrt(1-t.w*t.w);return e<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this}setAxisAngleFromRotationMatrix(t){let e,i,s,r;const n=.01,a=.1,o=t.elements,h=o[0],l=o[4],c=o[8],u=o[1],d=o[5],p=o[9],m=o[2],y=o[6],g=o[10];if(Math.abs(l-u)<n&&Math.abs(c-m)<n&&Math.abs(p-y)<n){if(Math.abs(l+u)<a&&Math.abs(c+m)<a&&Math.abs(p+y)<a&&Math.abs(h+d+g-3)<a)return this.set(1,0,0,0),this;e=Math.PI;const t=(h+1)/2,o=(d+1)/2,f=(g+1)/2,x=(l+u)/4,b=(c+m)/4,v=(p+y)/4;return t>o&&t>f?t<n?(i=0,s=.*********,r=.*********):(i=Math.sqrt(t),s=x/i,r=b/i):o>f?o<n?(i=.*********,s=0,r=.*********):(s=Math.sqrt(o),i=x/s,r=v/s):f<n?(i=.*********,s=.*********,r=0):(r=Math.sqrt(f),i=b/r,s=v/r),this.set(i,s,r,e),this}let f=Math.sqrt((y-p)*(y-p)+(c-m)*(c-m)+(u-l)*(u-l));return Math.abs(f)<.001&&(f=1),this.x=(y-p)/f,this.y=(c-m)/f,this.z=(u-l)/f,this.w=Math.acos((h+d+g-1)/2),this}setFromMatrixPosition(t){const e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this.w=e[15],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this}clamp(t,e){return this.x=Hi(this.x,t.x,e.x),this.y=Hi(this.y,t.y,e.y),this.z=Hi(this.z,t.z,e.z),this.w=Hi(this.w,t.w,e.w),this}clampScalar(t,e){return this.x=Hi(this.x,t,e),this.y=Hi(this.y,t,e),this.z=Hi(this.z,t,e),this.w=Hi(this.w,t,e),this}clampLength(t,e){const i=this.length();return this.divideScalar(i||1).multiplyScalar(Hi(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this.w=t.w+(e.w-t.w)*i,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}}class Is extends Vi{constructor(t=1,e=1,i={}){super(),i=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:wt,depthBuffer:!0,stencilBuffer:!1,resolveDepthBuffer:!0,resolveStencilBuffer:!0,depthTexture:null,samples:0,count:1,depth:1,multiview:!1},i),this.isRenderTarget=!0,this.width=t,this.height=e,this.depth=i.depth,this.scissor=new zs(0,0,t,e),this.scissorTest=!1,this.viewport=new zs(0,0,t,e);const s={width:t,height:e,depth:i.depth},r=new Ts(s);this.textures=[];const n=i.count;for(let t=0;t<n;t++)this.textures[t]=r.clone(),this.textures[t].isRenderTargetTexture=!0,this.textures[t].renderTarget=this;this._setTextureOptions(i),this.depthBuffer=i.depthBuffer,this.stencilBuffer=i.stencilBuffer,this.resolveDepthBuffer=i.resolveDepthBuffer,this.resolveStencilBuffer=i.resolveStencilBuffer,this._depthTexture=null,this.depthTexture=i.depthTexture,this.samples=i.samples,this.multiview=i.multiview}_setTextureOptions(t={}){const e={minFilter:wt,generateMipmaps:!1,flipY:!1,internalFormat:null};void 0!==t.mapping&&(e.mapping=t.mapping),void 0!==t.wrapS&&(e.wrapS=t.wrapS),void 0!==t.wrapT&&(e.wrapT=t.wrapT),void 0!==t.wrapR&&(e.wrapR=t.wrapR),void 0!==t.magFilter&&(e.magFilter=t.magFilter),void 0!==t.minFilter&&(e.minFilter=t.minFilter),void 0!==t.format&&(e.format=t.format),void 0!==t.type&&(e.type=t.type),void 0!==t.anisotropy&&(e.anisotropy=t.anisotropy),void 0!==t.colorSpace&&(e.colorSpace=t.colorSpace),void 0!==t.flipY&&(e.flipY=t.flipY),void 0!==t.generateMipmaps&&(e.generateMipmaps=t.generateMipmaps),void 0!==t.internalFormat&&(e.internalFormat=t.internalFormat);for(let t=0;t<this.textures.length;t++){this.textures[t].setValues(e)}}get texture(){return this.textures[0]}set texture(t){this.textures[0]=t}set depthTexture(t){null!==this._depthTexture&&(this._depthTexture.renderTarget=null),null!==t&&(t.renderTarget=this),this._depthTexture=t}get depthTexture(){return this._depthTexture}setSize(t,e,i=1){if(this.width!==t||this.height!==e||this.depth!==i){this.width=t,this.height=e,this.depth=i;for(let s=0,r=this.textures.length;s<r;s++)this.textures[s].image.width=t,this.textures[s].image.height=e,this.textures[s].image.depth=i,this.textures[s].isArrayTexture=this.textures[s].image.depth>1;this.dispose()}this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)}clone(){return(new this.constructor).copy(this)}copy(t){this.width=t.width,this.height=t.height,this.depth=t.depth,this.scissor.copy(t.scissor),this.scissorTest=t.scissorTest,this.viewport.copy(t.viewport),this.textures.length=0;for(let e=0,i=t.textures.length;e<i;e++){this.textures[e]=t.textures[e].clone(),this.textures[e].isRenderTargetTexture=!0,this.textures[e].renderTarget=this;const i=Object.assign({},t.textures[e].image);this.textures[e].source=new Ms(i)}return this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.resolveDepthBuffer=t.resolveDepthBuffer,this.resolveStencilBuffer=t.resolveStencilBuffer,null!==t.depthTexture&&(this.depthTexture=t.depthTexture.clone()),this.samples=t.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}}class Cs extends Is{constructor(t=1,e=1,i={}){super(t,e,i),this.isWebGLRenderTarget=!0}}class Bs extends Ts{constructor(t=null,e=1,i=1,s=1){super(null),this.isDataArrayTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=gt,this.minFilter=gt,this.wrapR=mt,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class ks extends Cs{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isWebGLArrayRenderTarget=!0,this.depth=i,this.texture=new Bs(null,t,e,i),this._setTextureOptions(s),this.texture.isRenderTargetTexture=!0}}class Es extends Ts{constructor(t=null,e=1,i=1,s=1){super(null),this.isData3DTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=gt,this.minFilter=gt,this.wrapR=mt,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class Rs extends Cs{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isWebGL3DRenderTarget=!0,this.depth=i,this.texture=new Es(null,t,e,i),this._setTextureOptions(s),this.texture.isRenderTargetTexture=!0}}class Ps{constructor(t=new Qi(1/0,1/0,1/0),e=new Qi(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e+=3)this.expandByPoint(Ns.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,i=t.count;e<i;e++)this.expandByPoint(Ns.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){const i=Ns.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}setFromObject(t,e=!1){return this.makeEmpty(),this.expandByObject(t,e)}clone(){return(new this.constructor).copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=1/0,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t,e=!1){t.updateWorldMatrix(!1,!1);const i=t.geometry;if(void 0!==i){const s=i.getAttribute("position");if(!0===e&&void 0!==s&&!0!==t.isInstancedMesh)for(let e=0,i=s.count;e<i;e++)!0===t.isMesh?t.getVertexPosition(e,Ns):Ns.fromBufferAttribute(s,e),Ns.applyMatrix4(t.matrixWorld),this.expandByPoint(Ns);else void 0!==t.boundingBox?(null===t.boundingBox&&t.computeBoundingBox(),Fs.copy(t.boundingBox)):(null===i.boundingBox&&i.computeBoundingBox(),Fs.copy(i.boundingBox)),Fs.applyMatrix4(t.matrixWorld),this.union(Fs)}const s=t.children;for(let t=0,i=s.length;t<i;t++)this.expandByObject(s[t],e);return this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y&&t.z>=this.min.z&&t.z<=this.max.z}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y&&t.max.z>=this.min.z&&t.min.z<=this.max.z}intersectsSphere(t){return this.clampPoint(t.center,Ns),Ns.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,i;return t.normal.x>0?(e=t.normal.x*this.min.x,i=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,i=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=-t.constant&&i>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(Hs),qs.subVectors(this.max,Hs),Vs.subVectors(t.a,Hs),Ls.subVectors(t.b,Hs),js.subVectors(t.c,Hs),Ws.subVectors(Ls,Vs),Us.subVectors(js,Ls),Ds.subVectors(Vs,js);let e=[0,-Ws.z,Ws.y,0,-Us.z,Us.y,0,-Ds.z,Ds.y,Ws.z,0,-Ws.x,Us.z,0,-Us.x,Ds.z,0,-Ds.x,-Ws.y,Ws.x,0,-Us.y,Us.x,0,-Ds.y,Ds.x,0];return!!Ys(e,Vs,Ls,js,qs)&&(e=[1,0,0,0,1,0,0,0,1],!!Ys(e,Vs,Ls,js,qs)&&(Js.crossVectors(Ws,Us),e=[Js.x,Js.y,Js.z],Ys(e,Vs,Ls,js,qs)))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,Ns).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=.5*this.getSize(Ns).length()),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()||(Os[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),Os[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),Os[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),Os[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),Os[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),Os[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),Os[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),Os[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(Os)),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}toJSON(){return{min:this.min.toArray(),max:this.max.toArray()}}fromJSON(t){return this.min.fromArray(t.min),this.max.fromArray(t.max),this}}const Os=[new Qi,new Qi,new Qi,new Qi,new Qi,new Qi,new Qi,new Qi],Ns=new Qi,Fs=new Ps,Vs=new Qi,Ls=new Qi,js=new Qi,Ws=new Qi,Us=new Qi,Ds=new Qi,Hs=new Qi,qs=new Qi,Js=new Qi,Xs=new Qi;function Ys(t,e,i,s,r){for(let n=0,a=t.length-3;n<=a;n+=3){Xs.fromArray(t,n);const a=r.x*Math.abs(Xs.x)+r.y*Math.abs(Xs.y)+r.z*Math.abs(Xs.z),o=e.dot(Xs),h=i.dot(Xs),l=s.dot(Xs);if(Math.max(-Math.max(o,h,l),Math.min(o,h,l))>a)return!1}return!0}const Zs=new Ps,Gs=new Qi,$s=new Qi;class Qs{constructor(t=new Qi,e=-1){this.isSphere=!0,this.center=t,this.radius=e}set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){const i=this.center;void 0!==e?i.copy(e):Zs.setFromPoints(t).getCenter(i);let s=0;for(let e=0,r=t.length;e<r;e++)s=Math.max(s,i.distanceToSquared(t[e]));return this.radius=Math.sqrt(s),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){const e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){const i=this.center.distanceToSquared(t);return e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?(t.makeEmpty(),t):(t.set(this.center,this.center),t.expandByScalar(this.radius),t)}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;Gs.subVectors(t,this.center);const e=Gs.lengthSq();if(e>this.radius*this.radius){const t=Math.sqrt(e),i=.5*(t-this.radius);this.center.addScaledVector(Gs,i/t),this.radius+=i}return this}union(t){return t.isEmpty()?this:this.isEmpty()?(this.copy(t),this):(!0===this.center.equals(t.center)?this.radius=Math.max(this.radius,t.radius):($s.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(Gs.copy(t.center).add($s)),this.expandByPoint(Gs.copy(t.center).sub($s))),this)}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return(new this.constructor).copy(this)}toJSON(){return{radius:this.radius,center:this.center.toArray()}}fromJSON(t){return this.radius=t.radius,this.center.fromArray(t.center),this}}const Ks=new Qi,tr=new Qi,er=new Qi,ir=new Qi,sr=new Qi,rr=new Qi,nr=new Qi;class ar{constructor(t=new Qi,e=new Qi(0,0,-1)){this.origin=t,this.direction=e}set(t,e){return this.origin.copy(t),this.direction.copy(e),this}copy(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this}at(t,e){return e.copy(this.origin).addScaledVector(this.direction,t)}lookAt(t){return this.direction.copy(t).sub(this.origin).normalize(),this}recast(t){return this.origin.copy(this.at(t,Ks)),this}closestPointToPoint(t,e){e.subVectors(t,this.origin);const i=e.dot(this.direction);return i<0?e.copy(this.origin):e.copy(this.origin).addScaledVector(this.direction,i)}distanceToPoint(t){return Math.sqrt(this.distanceSqToPoint(t))}distanceSqToPoint(t){const e=Ks.subVectors(t,this.origin).dot(this.direction);return e<0?this.origin.distanceToSquared(t):(Ks.copy(this.origin).addScaledVector(this.direction,e),Ks.distanceToSquared(t))}distanceSqToSegment(t,e,i,s){tr.copy(t).add(e).multiplyScalar(.5),er.copy(e).sub(t).normalize(),ir.copy(this.origin).sub(tr);const r=.5*t.distanceTo(e),n=-this.direction.dot(er),a=ir.dot(this.direction),o=-ir.dot(er),h=ir.lengthSq(),l=Math.abs(1-n*n);let c,u,d,p;if(l>0)if(c=n*o-a,u=n*a-o,p=r*l,c>=0)if(u>=-p)if(u<=p){const t=1/l;c*=t,u*=t,d=c*(c+n*u+2*a)+u*(n*c+u+2*o)+h}else u=r,c=Math.max(0,-(n*u+a)),d=-c*c+u*(u+2*o)+h;else u=-r,c=Math.max(0,-(n*u+a)),d=-c*c+u*(u+2*o)+h;else u<=-p?(c=Math.max(0,-(-n*r+a)),u=c>0?-r:Math.min(Math.max(-r,-o),r),d=-c*c+u*(u+2*o)+h):u<=p?(c=0,u=Math.min(Math.max(-r,-o),r),d=u*(u+2*o)+h):(c=Math.max(0,-(n*r+a)),u=c>0?r:Math.min(Math.max(-r,-o),r),d=-c*c+u*(u+2*o)+h);else u=n>0?-r:r,c=Math.max(0,-(n*u+a)),d=-c*c+u*(u+2*o)+h;return i&&i.copy(this.origin).addScaledVector(this.direction,c),s&&s.copy(tr).addScaledVector(er,u),d}intersectSphere(t,e){Ks.subVectors(t.center,this.origin);const i=Ks.dot(this.direction),s=Ks.dot(Ks)-i*i,r=t.radius*t.radius;if(s>r)return null;const n=Math.sqrt(r-s),a=i-n,o=i+n;return o<0?null:a<0?this.at(o,e):this.at(a,e)}intersectsSphere(t){return!(t.radius<0)&&this.distanceSqToPoint(t.center)<=t.radius*t.radius}distanceToPlane(t){const e=t.normal.dot(this.direction);if(0===e)return 0===t.distanceToPoint(this.origin)?0:null;const i=-(this.origin.dot(t.normal)+t.constant)/e;return i>=0?i:null}intersectPlane(t,e){const i=this.distanceToPlane(t);return null===i?null:this.at(i,e)}intersectsPlane(t){const e=t.distanceToPoint(this.origin);if(0===e)return!0;return t.normal.dot(this.direction)*e<0}intersectBox(t,e){let i,s,r,n,a,o;const h=1/this.direction.x,l=1/this.direction.y,c=1/this.direction.z,u=this.origin;return h>=0?(i=(t.min.x-u.x)*h,s=(t.max.x-u.x)*h):(i=(t.max.x-u.x)*h,s=(t.min.x-u.x)*h),l>=0?(r=(t.min.y-u.y)*l,n=(t.max.y-u.y)*l):(r=(t.max.y-u.y)*l,n=(t.min.y-u.y)*l),i>n||r>s?null:((r>i||isNaN(i))&&(i=r),(n<s||isNaN(s))&&(s=n),c>=0?(a=(t.min.z-u.z)*c,o=(t.max.z-u.z)*c):(a=(t.max.z-u.z)*c,o=(t.min.z-u.z)*c),i>o||a>s?null:((a>i||i!=i)&&(i=a),(o<s||s!=s)&&(s=o),s<0?null:this.at(i>=0?i:s,e)))}intersectsBox(t){return null!==this.intersectBox(t,Ks)}intersectTriangle(t,e,i,s,r){sr.subVectors(e,t),rr.subVectors(i,t),nr.crossVectors(sr,rr);let n,a=this.direction.dot(nr);if(a>0){if(s)return null;n=1}else{if(!(a<0))return null;n=-1,a=-a}ir.subVectors(this.origin,t);const o=n*this.direction.dot(rr.crossVectors(ir,rr));if(o<0)return null;const h=n*this.direction.dot(sr.cross(ir));if(h<0)return null;if(o+h>a)return null;const l=-n*ir.dot(nr);return l<0?null:this.at(l/a,r)}applyMatrix4(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this}equals(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}clone(){return(new this.constructor).copy(this)}}class or{constructor(t,e,i,s,r,n,a,o,h,l,c,u,d,p,m,y){or.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h,l,c,u,d,p,m,y)}set(t,e,i,s,r,n,a,o,h,l,c,u,d,p,m,y){const g=this.elements;return g[0]=t,g[4]=e,g[8]=i,g[12]=s,g[1]=r,g[5]=n,g[9]=a,g[13]=o,g[2]=h,g[6]=l,g[10]=c,g[14]=u,g[3]=d,g[7]=p,g[11]=m,g[15]=y,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return(new or).fromArray(this.elements)}copy(t){const e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}copyPosition(t){const e=this.elements,i=t.elements;return e[12]=i[12],e[13]=i[13],e[14]=i[14],this}setFromMatrix3(t){const e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this}makeBasis(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this}extractRotation(t){const e=this.elements,i=t.elements,s=1/hr.setFromMatrixColumn(t,0).length(),r=1/hr.setFromMatrixColumn(t,1).length(),n=1/hr.setFromMatrixColumn(t,2).length();return e[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=0,e[4]=i[4]*r,e[5]=i[5]*r,e[6]=i[6]*r,e[7]=0,e[8]=i[8]*n,e[9]=i[9]*n,e[10]=i[10]*n,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){const e=this.elements,i=t.x,s=t.y,r=t.z,n=Math.cos(i),a=Math.sin(i),o=Math.cos(s),h=Math.sin(s),l=Math.cos(r),c=Math.sin(r);if("XYZ"===t.order){const t=n*l,i=n*c,s=a*l,r=a*c;e[0]=o*l,e[4]=-o*c,e[8]=h,e[1]=i+s*h,e[5]=t-r*h,e[9]=-a*o,e[2]=r-t*h,e[6]=s+i*h,e[10]=n*o}else if("YXZ"===t.order){const t=o*l,i=o*c,s=h*l,r=h*c;e[0]=t+r*a,e[4]=s*a-i,e[8]=n*h,e[1]=n*c,e[5]=n*l,e[9]=-a,e[2]=i*a-s,e[6]=r+t*a,e[10]=n*o}else if("ZXY"===t.order){const t=o*l,i=o*c,s=h*l,r=h*c;e[0]=t-r*a,e[4]=-n*c,e[8]=s+i*a,e[1]=i+s*a,e[5]=n*l,e[9]=r-t*a,e[2]=-n*h,e[6]=a,e[10]=n*o}else if("ZYX"===t.order){const t=n*l,i=n*c,s=a*l,r=a*c;e[0]=o*l,e[4]=s*h-i,e[8]=t*h+r,e[1]=o*c,e[5]=r*h+t,e[9]=i*h-s,e[2]=-h,e[6]=a*o,e[10]=n*o}else if("YZX"===t.order){const t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=r-t*c,e[8]=s*c+i,e[1]=c,e[5]=n*l,e[9]=-a*l,e[2]=-h*l,e[6]=i*c+s,e[10]=t-r*c}else if("XZY"===t.order){const t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=-c,e[8]=h*l,e[1]=t*c+r,e[5]=n*l,e[9]=i*c-s,e[2]=s*c-i,e[6]=a*l,e[10]=r*c+t}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(cr,t,ur)}lookAt(t,e,i){const s=this.elements;return mr.subVectors(t,e),0===mr.lengthSq()&&(mr.z=1),mr.normalize(),dr.crossVectors(i,mr),0===dr.lengthSq()&&(1===Math.abs(i.z)?mr.x+=1e-4:mr.z+=1e-4,mr.normalize(),dr.crossVectors(i,mr)),dr.normalize(),pr.crossVectors(mr,dr),s[0]=dr.x,s[4]=pr.x,s[8]=mr.x,s[1]=dr.y,s[5]=pr.y,s[9]=mr.y,s[2]=dr.z,s[6]=pr.z,s[10]=mr.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[4],o=i[8],h=i[12],l=i[1],c=i[5],u=i[9],d=i[13],p=i[2],m=i[6],y=i[10],g=i[14],f=i[3],x=i[7],b=i[11],v=i[15],w=s[0],M=s[4],S=s[8],_=s[12],A=s[1],T=s[5],z=s[9],I=s[13],C=s[2],B=s[6],k=s[10],E=s[14],R=s[3],P=s[7],O=s[11],N=s[15];return r[0]=n*w+a*A+o*C+h*R,r[4]=n*M+a*T+o*B+h*P,r[8]=n*S+a*z+o*k+h*O,r[12]=n*_+a*I+o*E+h*N,r[1]=l*w+c*A+u*C+d*R,r[5]=l*M+c*T+u*B+d*P,r[9]=l*S+c*z+u*k+d*O,r[13]=l*_+c*I+u*E+d*N,r[2]=p*w+m*A+y*C+g*R,r[6]=p*M+m*T+y*B+g*P,r[10]=p*S+m*z+y*k+g*O,r[14]=p*_+m*I+y*E+g*N,r[3]=f*w+x*A+b*C+v*R,r[7]=f*M+x*T+b*B+v*P,r[11]=f*S+x*z+b*k+v*O,r[15]=f*_+x*I+b*E+v*N,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){const t=this.elements,e=t[0],i=t[4],s=t[8],r=t[12],n=t[1],a=t[5],o=t[9],h=t[13],l=t[2],c=t[6],u=t[10],d=t[14];return t[3]*(+r*o*c-s*h*c-r*a*u+i*h*u+s*a*d-i*o*d)+t[7]*(+e*o*d-e*h*u+r*n*u-s*n*d+s*h*l-r*o*l)+t[11]*(+e*h*c-e*a*d-r*n*c+i*n*d+r*a*l-i*h*l)+t[15]*(-s*a*l-e*o*c+e*a*u+s*n*c-i*n*u+i*o*l)}transpose(){const t=this.elements;let e;return e=t[1],t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this}setPosition(t,e,i){const s=this.elements;return t.isVector3?(s[12]=t.x,s[13]=t.y,s[14]=t.z):(s[12]=t,s[13]=e,s[14]=i),this}invert(){const t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=t[9],u=t[10],d=t[11],p=t[12],m=t[13],y=t[14],g=t[15],f=c*y*h-m*u*h+m*o*d-a*y*d-c*o*g+a*u*g,x=p*u*h-l*y*h-p*o*d+n*y*d+l*o*g-n*u*g,b=l*m*h-p*c*h+p*a*d-n*m*d-l*a*g+n*c*g,v=p*c*o-l*m*o-p*a*u+n*m*u+l*a*y-n*c*y,w=e*f+i*x+s*b+r*v;if(0===w)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);const M=1/w;return t[0]=f*M,t[1]=(m*u*r-c*y*r-m*s*d+i*y*d+c*s*g-i*u*g)*M,t[2]=(a*y*r-m*o*r+m*s*h-i*y*h-a*s*g+i*o*g)*M,t[3]=(c*o*r-a*u*r-c*s*h+i*u*h+a*s*d-i*o*d)*M,t[4]=x*M,t[5]=(l*y*r-p*u*r+p*s*d-e*y*d-l*s*g+e*u*g)*M,t[6]=(p*o*r-n*y*r-p*s*h+e*y*h+n*s*g-e*o*g)*M,t[7]=(n*u*r-l*o*r+l*s*h-e*u*h-n*s*d+e*o*d)*M,t[8]=b*M,t[9]=(p*c*r-l*m*r-p*i*d+e*m*d+l*i*g-e*c*g)*M,t[10]=(n*m*r-p*a*r+p*i*h-e*m*h-n*i*g+e*a*g)*M,t[11]=(l*a*r-n*c*r-l*i*h+e*c*h+n*i*d-e*a*d)*M,t[12]=v*M,t[13]=(l*m*s-p*c*s+p*i*u-e*m*u-l*i*y+e*c*y)*M,t[14]=(p*a*s-n*m*s-p*i*o+e*m*o+n*i*y-e*a*y)*M,t[15]=(n*c*s-l*a*s+l*i*o-e*c*o-n*i*u+e*a*u)*M,this}scale(t){const e=this.elements,i=t.x,s=t.y,r=t.z;return e[0]*=i,e[4]*=s,e[8]*=r,e[1]*=i,e[5]*=s,e[9]*=r,e[2]*=i,e[6]*=s,e[10]*=r,e[3]*=i,e[7]*=s,e[11]*=r,this}getMaxScaleOnAxis(){const t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],i=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],s=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(e,i,s))}makeTranslation(t,e,i){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this}makeRotationX(t){const e=Math.cos(t),i=Math.sin(t);return this.set(1,0,0,0,0,e,-i,0,0,i,e,0,0,0,0,1),this}makeRotationY(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,0,i,0,0,1,0,0,-i,0,e,0,0,0,0,1),this}makeRotationZ(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,0,i,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){const i=Math.cos(e),s=Math.sin(e),r=1-i,n=t.x,a=t.y,o=t.z,h=r*n,l=r*a;return this.set(h*n+i,h*a-s*o,h*o+s*a,0,h*a+s*o,l*a+i,l*o-s*n,0,h*o-s*a,l*o+s*n,r*o*o+i,0,0,0,0,1),this}makeScale(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this}makeShear(t,e,i,s,r,n){return this.set(1,i,r,0,t,1,n,0,e,s,1,0,0,0,0,1),this}compose(t,e,i){const s=this.elements,r=e._x,n=e._y,a=e._z,o=e._w,h=r+r,l=n+n,c=a+a,u=r*h,d=r*l,p=r*c,m=n*l,y=n*c,g=a*c,f=o*h,x=o*l,b=o*c,v=i.x,w=i.y,M=i.z;return s[0]=(1-(m+g))*v,s[1]=(d+b)*v,s[2]=(p-x)*v,s[3]=0,s[4]=(d-b)*w,s[5]=(1-(u+g))*w,s[6]=(y+f)*w,s[7]=0,s[8]=(p+x)*M,s[9]=(y-f)*M,s[10]=(1-(u+m))*M,s[11]=0,s[12]=t.x,s[13]=t.y,s[14]=t.z,s[15]=1,this}decompose(t,e,i){const s=this.elements;let r=hr.set(s[0],s[1],s[2]).length();const n=hr.set(s[4],s[5],s[6]).length(),a=hr.set(s[8],s[9],s[10]).length();this.determinant()<0&&(r=-r),t.x=s[12],t.y=s[13],t.z=s[14],lr.copy(this);const o=1/r,h=1/n,l=1/a;return lr.elements[0]*=o,lr.elements[1]*=o,lr.elements[2]*=o,lr.elements[4]*=h,lr.elements[5]*=h,lr.elements[6]*=h,lr.elements[8]*=l,lr.elements[9]*=l,lr.elements[10]*=l,e.setFromRotationMatrix(lr),i.x=r,i.y=n,i.z=a,this}makePerspective(t,e,i,s,r,n,a=2e3){const o=this.elements,h=2*r/(e-t),l=2*r/(i-s),c=(e+t)/(e-t),u=(i+s)/(i-s);let d,p;if(a===Ri)d=-(n+r)/(n-r),p=-2*n*r/(n-r);else{if(a!==Pi)throw new Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+a);d=-n/(n-r),p=-n*r/(n-r)}return o[0]=h,o[4]=0,o[8]=c,o[12]=0,o[1]=0,o[5]=l,o[9]=u,o[13]=0,o[2]=0,o[6]=0,o[10]=d,o[14]=p,o[3]=0,o[7]=0,o[11]=-1,o[15]=0,this}makeOrthographic(t,e,i,s,r,n,a=2e3){const o=this.elements,h=1/(e-t),l=1/(i-s),c=1/(n-r),u=(e+t)*h,d=(i+s)*l;let p,m;if(a===Ri)p=(n+r)*c,m=-2*c;else{if(a!==Pi)throw new Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+a);p=r*c,m=-1*c}return o[0]=2*h,o[4]=0,o[8]=0,o[12]=-u,o[1]=0,o[5]=2*l,o[9]=0,o[13]=-d,o[2]=0,o[6]=0,o[10]=m,o[14]=-p,o[3]=0,o[7]=0,o[11]=0,o[15]=1,this}equals(t){const e=this.elements,i=t.elements;for(let t=0;t<16;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<16;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){const i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}}const hr=new Qi,lr=new or,cr=new Qi(0,0,0),ur=new Qi(1,1,1),dr=new Qi,pr=new Qi,mr=new Qi,yr=new or,gr=new $i;class fr{constructor(t=0,e=0,i=0,s=fr.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=i,this._order=s}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,i,s=this._order){return this._x=t,this._y=e,this._z=i,this._order=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t,e=this._order,i=!0){const s=t.elements,r=s[0],n=s[4],a=s[8],o=s[1],h=s[5],l=s[9],c=s[2],u=s[6],d=s[10];switch(e){case"XYZ":this._y=Math.asin(Hi(a,-1,1)),Math.abs(a)<.9999999?(this._x=Math.atan2(-l,d),this._z=Math.atan2(-n,r)):(this._x=Math.atan2(u,h),this._z=0);break;case"YXZ":this._x=Math.asin(-Hi(l,-1,1)),Math.abs(l)<.9999999?(this._y=Math.atan2(a,d),this._z=Math.atan2(o,h)):(this._y=Math.atan2(-c,r),this._z=0);break;case"ZXY":this._x=Math.asin(Hi(u,-1,1)),Math.abs(u)<.9999999?(this._y=Math.atan2(-c,d),this._z=Math.atan2(-n,h)):(this._y=0,this._z=Math.atan2(o,r));break;case"ZYX":this._y=Math.asin(-Hi(c,-1,1)),Math.abs(c)<.9999999?(this._x=Math.atan2(u,d),this._z=Math.atan2(o,r)):(this._x=0,this._z=Math.atan2(-n,h));break;case"YZX":this._z=Math.asin(Hi(o,-1,1)),Math.abs(o)<.9999999?(this._x=Math.atan2(-l,h),this._y=Math.atan2(-c,r)):(this._x=0,this._y=Math.atan2(a,d));break;case"XZY":this._z=Math.asin(-Hi(n,-1,1)),Math.abs(n)<.9999999?(this._x=Math.atan2(u,h),this._y=Math.atan2(a,r)):(this._x=Math.atan2(-l,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,!0===i&&this._onChangeCallback(),this}setFromQuaternion(t,e,i){return yr.makeRotationFromQuaternion(t),this.setFromRotationMatrix(yr,e,i)}setFromVector3(t,e=this._order){return this.set(t.x,t.y,t.z,e)}reorder(t){return gr.setFromEuler(this),this.setFromQuaternion(gr,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}fr.DEFAULT_ORDER="XYZ";class xr{constructor(){this.mask=1}set(t){this.mask=1<<t>>>0}enable(t){this.mask|=1<<t}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t}disable(t){this.mask&=~(1<<t)}disableAll(){this.mask=0}test(t){return 0!==(this.mask&t.mask)}isEnabled(t){return!!(this.mask&1<<t)}}let br=0;const vr=new Qi,wr=new $i,Mr=new or,Sr=new Qi,_r=new Qi,Ar=new Qi,Tr=new $i,zr=new Qi(1,0,0),Ir=new Qi(0,1,0),Cr=new Qi(0,0,1),Br={type:"added"},kr={type:"removed"},Er={type:"childadded",child:null},Rr={type:"childremoved",child:null};class Pr extends Vi{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:br++}),this.uuid=Di(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=Pr.DEFAULT_UP.clone();const t=new Qi,e=new fr,i=new $i,s=new Qi(1,1,1);e._onChange(function(){i.setFromEuler(e,!1)}),i._onChange(function(){e.setFromQuaternion(i,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:i},scale:{configurable:!0,enumerable:!0,value:s},modelViewMatrix:{value:new or},normalMatrix:{value:new es}}),this.matrix=new or,this.matrixWorld=new or,this.matrixAutoUpdate=Pr.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=Pr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new xr,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.customDepthMaterial=void 0,this.customDistanceMaterial=void 0,this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return wr.setFromAxisAngle(t,e),this.quaternion.multiply(wr),this}rotateOnWorldAxis(t,e){return wr.setFromAxisAngle(t,e),this.quaternion.premultiply(wr),this}rotateX(t){return this.rotateOnAxis(zr,t)}rotateY(t){return this.rotateOnAxis(Ir,t)}rotateZ(t){return this.rotateOnAxis(Cr,t)}translateOnAxis(t,e){return vr.copy(t).applyQuaternion(this.quaternion),this.position.add(vr.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(zr,t)}translateY(t){return this.translateOnAxis(Ir,t)}translateZ(t){return this.translateOnAxis(Cr,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(Mr.copy(this.matrixWorld).invert())}lookAt(t,e,i){t.isVector3?Sr.copy(t):Sr.set(t,e,i);const s=this.parent;this.updateWorldMatrix(!0,!1),_r.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?Mr.lookAt(_r,Sr,this.up):Mr.lookAt(Sr,_r,this.up),this.quaternion.setFromRotationMatrix(Mr),s&&(Mr.extractRotation(s.matrixWorld),wr.setFromRotationMatrix(Mr),this.quaternion.premultiply(wr.invert()))}add(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}return t===this?(console.error("THREE.Object3D.add: object can't be added as a child of itself.",t),this):(t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(Br),Er.child=t,this.dispatchEvent(Er),Er.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this)}remove(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.remove(arguments[t]);return this}const e=this.children.indexOf(t);return-1!==e&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(kr),Rr.child=t,this.dispatchEvent(Rr),Rr.child=null),this}removeFromParent(){const t=this.parent;return null!==t&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),Mr.copy(this.matrixWorld).invert(),null!==t.parent&&(t.parent.updateWorldMatrix(!0,!1),Mr.multiply(t.parent.matrixWorld)),t.applyMatrix4(Mr),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(Br),Er.child=t,this.dispatchEvent(Er),Er.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let i=0,s=this.children.length;i<s;i++){const s=this.children[i].getObjectByProperty(t,e);if(void 0!==s)return s}}getObjectsByProperty(t,e,i=[]){this[t]===e&&i.push(this);const s=this.children;for(let r=0,n=s.length;r<n;r++)s[r].getObjectsByProperty(t,e,i);return i}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(_r,t,Ar),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(_r,Tr,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);const e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);const e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverse(t)}traverseVisible(t){if(!1===this.visible)return;t(this);const e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverseVisible(t)}traverseAncestors(t){const e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,t=!0);const e=this.children;for(let i=0,s=e.length;i<s;i++){e[i].updateMatrixWorld(t)}}updateWorldMatrix(t,e){const i=this.parent;if(!0===t&&null!==i&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===e){const t=this.children;for(let e=0,i=t.length;e<i;e++){t[e].updateWorldMatrix(!1,!0)}}}toJSON(t){const e=void 0===t||"string"==typeof t,i={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},i.metadata={version:4.7,type:"Object",generator:"Object3D.toJSON"});const s={};function r(e,i){return void 0===e[i.uuid]&&(e[i.uuid]=i.toJSON(t)),i.uuid}if(s.uuid=this.uuid,s.type=this.type,""!==this.name&&(s.name=this.name),!0===this.castShadow&&(s.castShadow=!0),!0===this.receiveShadow&&(s.receiveShadow=!0),!1===this.visible&&(s.visible=!1),!1===this.frustumCulled&&(s.frustumCulled=!1),0!==this.renderOrder&&(s.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(s.userData=this.userData),s.layers=this.layers.mask,s.matrix=this.matrix.toArray(),s.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(s.matrixAutoUpdate=!1),this.isInstancedMesh&&(s.type="InstancedMesh",s.count=this.count,s.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(s.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(s.type="BatchedMesh",s.perObjectFrustumCulled=this.perObjectFrustumCulled,s.sortObjects=this.sortObjects,s.drawRanges=this._drawRanges,s.reservedRanges=this._reservedRanges,s.geometryInfo=this._geometryInfo.map(t=>({...t,boundingBox:t.boundingBox?t.boundingBox.toJSON():void 0,boundingSphere:t.boundingSphere?t.boundingSphere.toJSON():void 0})),s.instanceInfo=this._instanceInfo.map(t=>({...t})),s.availableInstanceIds=this._availableInstanceIds.slice(),s.availableGeometryIds=this._availableGeometryIds.slice(),s.nextIndexStart=this._nextIndexStart,s.nextVertexStart=this._nextVertexStart,s.geometryCount=this._geometryCount,s.maxInstanceCount=this._maxInstanceCount,s.maxVertexCount=this._maxVertexCount,s.maxIndexCount=this._maxIndexCount,s.geometryInitialized=this._geometryInitialized,s.matricesTexture=this._matricesTexture.toJSON(t),s.indirectTexture=this._indirectTexture.toJSON(t),null!==this._colorsTexture&&(s.colorsTexture=this._colorsTexture.toJSON(t)),null!==this.boundingSphere&&(s.boundingSphere=this.boundingSphere.toJSON()),null!==this.boundingBox&&(s.boundingBox=this.boundingBox.toJSON())),this.isScene)this.background&&(this.background.isColor?s.background=this.background.toJSON():this.background.isTexture&&(s.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(s.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){s.geometry=r(t.geometries,this.geometry);const e=this.geometry.parameters;if(void 0!==e&&void 0!==e.shapes){const i=e.shapes;if(Array.isArray(i))for(let e=0,s=i.length;e<s;e++){const s=i[e];r(t.shapes,s)}else r(t.shapes,i)}}if(this.isSkinnedMesh&&(s.bindMode=this.bindMode,s.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(t.skeletons,this.skeleton),s.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){const e=[];for(let i=0,s=this.material.length;i<s;i++)e.push(r(t.materials,this.material[i]));s.material=e}else s.material=r(t.materials,this.material);if(this.children.length>0){s.children=[];for(let e=0;e<this.children.length;e++)s.children.push(this.children[e].toJSON(t).object)}if(this.animations.length>0){s.animations=[];for(let e=0;e<this.animations.length;e++){const i=this.animations[e];s.animations.push(r(t.animations,i))}}if(e){const e=n(t.geometries),s=n(t.materials),r=n(t.textures),a=n(t.images),o=n(t.shapes),h=n(t.skeletons),l=n(t.animations),c=n(t.nodes);e.length>0&&(i.geometries=e),s.length>0&&(i.materials=s),r.length>0&&(i.textures=r),a.length>0&&(i.images=a),o.length>0&&(i.shapes=o),h.length>0&&(i.skeletons=h),l.length>0&&(i.animations=l),c.length>0&&(i.nodes=c)}return i.object=s,i;function n(t){const e=[];for(const i in t){const s=t[i];delete s.metadata,e.push(s)}return e}}clone(t){return(new this.constructor).copy(this,t)}copy(t,e=!0){if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(let e=0;e<t.children.length;e++){const i=t.children[e];this.add(i.clone())}return this}}Pr.DEFAULT_UP=new Qi(0,1,0),Pr.DEFAULT_MATRIX_AUTO_UPDATE=!0,Pr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;const Or=new Qi,Nr=new Qi,Fr=new Qi,Vr=new Qi,Lr=new Qi,jr=new Qi,Wr=new Qi,Ur=new Qi,Dr=new Qi,Hr=new Qi,qr=new zs,Jr=new zs,Xr=new zs;class Yr{constructor(t=new Qi,e=new Qi,i=new Qi){this.a=t,this.b=e,this.c=i}static getNormal(t,e,i,s){s.subVectors(i,e),Or.subVectors(t,e),s.cross(Or);const r=s.lengthSq();return r>0?s.multiplyScalar(1/Math.sqrt(r)):s.set(0,0,0)}static getBarycoord(t,e,i,s,r){Or.subVectors(s,e),Nr.subVectors(i,e),Fr.subVectors(t,e);const n=Or.dot(Or),a=Or.dot(Nr),o=Or.dot(Fr),h=Nr.dot(Nr),l=Nr.dot(Fr),c=n*h-a*a;if(0===c)return r.set(0,0,0),null;const u=1/c,d=(h*o-a*l)*u,p=(n*l-a*o)*u;return r.set(1-d-p,p,d)}static containsPoint(t,e,i,s){return null!==this.getBarycoord(t,e,i,s,Vr)&&(Vr.x>=0&&Vr.y>=0&&Vr.x+Vr.y<=1)}static getInterpolation(t,e,i,s,r,n,a,o){return null===this.getBarycoord(t,e,i,s,Vr)?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(r,Vr.x),o.addScaledVector(n,Vr.y),o.addScaledVector(a,Vr.z),o)}static getInterpolatedAttribute(t,e,i,s,r,n){return qr.setScalar(0),Jr.setScalar(0),Xr.setScalar(0),qr.fromBufferAttribute(t,e),Jr.fromBufferAttribute(t,i),Xr.fromBufferAttribute(t,s),n.setScalar(0),n.addScaledVector(qr,r.x),n.addScaledVector(Jr,r.y),n.addScaledVector(Xr,r.z),n}static isFrontFacing(t,e,i,s){return Or.subVectors(i,e),Nr.subVectors(t,e),Or.cross(Nr).dot(s)<0}set(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this}setFromPointsAndIndices(t,e,i,s){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[s]),this}setFromAttributeAndIndices(t,e,i,s){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,i),this.c.fromBufferAttribute(t,s),this}clone(){return(new this.constructor).copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return Or.subVectors(this.c,this.b),Nr.subVectors(this.a,this.b),.5*Or.cross(Nr).length()}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return Yr.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return Yr.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,i,s,r){return Yr.getInterpolation(t,this.a,this.b,this.c,e,i,s,r)}containsPoint(t){return Yr.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return Yr.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){const i=this.a,s=this.b,r=this.c;let n,a;Lr.subVectors(s,i),jr.subVectors(r,i),Ur.subVectors(t,i);const o=Lr.dot(Ur),h=jr.dot(Ur);if(o<=0&&h<=0)return e.copy(i);Dr.subVectors(t,s);const l=Lr.dot(Dr),c=jr.dot(Dr);if(l>=0&&c<=l)return e.copy(s);const u=o*c-l*h;if(u<=0&&o>=0&&l<=0)return n=o/(o-l),e.copy(i).addScaledVector(Lr,n);Hr.subVectors(t,r);const d=Lr.dot(Hr),p=jr.dot(Hr);if(p>=0&&d<=p)return e.copy(r);const m=d*h-o*p;if(m<=0&&h>=0&&p<=0)return a=h/(h-p),e.copy(i).addScaledVector(jr,a);const y=l*p-d*c;if(y<=0&&c-l>=0&&d-p>=0)return Wr.subVectors(r,s),a=(c-l)/(c-l+(d-p)),e.copy(s).addScaledVector(Wr,a);const g=1/(y+m+u);return n=m*g,a=u*g,e.copy(i).addScaledVector(Lr,n).addScaledVector(jr,a)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}const Zr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},Gr={h:0,s:0,l:0},$r={h:0,s:0,l:0};function Qr(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+6*(e-t)*(2/3-i):t}class Kr{constructor(t,e,i){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(t,e,i)}set(t,e,i){if(void 0===e&&void 0===i){const e=t;e&&e.isColor?this.copy(e):"number"==typeof e?this.setHex(e):"string"==typeof e&&this.setStyle(e)}else this.setRGB(t,e,i);return this}setScalar(t){return this.r=t,this.g=t,this.b=t,this}setHex(t,e=Ye){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,gs.colorSpaceToWorking(this,e),this}setRGB(t,e,i,s=gs.workingColorSpace){return this.r=t,this.g=e,this.b=i,gs.colorSpaceToWorking(this,s),this}setHSL(t,e,i,s=gs.workingColorSpace){if(t=qi(t,1),e=Hi(e,0,1),i=Hi(i,0,1),0===e)this.r=this.g=this.b=i;else{const s=i<=.5?i*(1+e):i+e-i*e,r=2*i-s;this.r=Qr(r,s,t+1/3),this.g=Qr(r,s,t),this.b=Qr(r,s,t-1/3)}return gs.colorSpaceToWorking(this,s),this}setStyle(t,e=Ye){function i(e){void 0!==e&&parseFloat(e)<1&&console.warn("THREE.Color: Alpha component of "+t+" will be ignored.")}let s;if(s=/^(\w+)\(([^\)]*)\)/.exec(t)){let r;const n=s[1],a=s[2];switch(n){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return i(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,e);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return i(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,e);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return i(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,e);break;default:console.warn("THREE.Color: Unknown color model "+t)}}else if(s=/^\#([A-Fa-f\d]+)$/.exec(t)){const i=s[1],r=i.length;if(3===r)return this.setRGB(parseInt(i.charAt(0),16)/15,parseInt(i.charAt(1),16)/15,parseInt(i.charAt(2),16)/15,e);if(6===r)return this.setHex(parseInt(i,16),e);console.warn("THREE.Color: Invalid hex color "+t)}else if(t&&t.length>0)return this.setColorName(t,e);return this}setColorName(t,e=Ye){const i=Zr[t.toLowerCase()];return void 0!==i?this.setHex(i,e):console.warn("THREE.Color: Unknown color "+t),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(t){return this.r=t.r,this.g=t.g,this.b=t.b,this}copySRGBToLinear(t){return this.r=fs(t.r),this.g=fs(t.g),this.b=fs(t.b),this}copyLinearToSRGB(t){return this.r=xs(t.r),this.g=xs(t.g),this.b=xs(t.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(t=Ye){return gs.workingToColorSpace(tn.copy(this),t),65536*Math.round(Hi(255*tn.r,0,255))+256*Math.round(Hi(255*tn.g,0,255))+Math.round(Hi(255*tn.b,0,255))}getHexString(t=Ye){return("000000"+this.getHex(t).toString(16)).slice(-6)}getHSL(t,e=gs.workingColorSpace){gs.workingToColorSpace(tn.copy(this),e);const i=tn.r,s=tn.g,r=tn.b,n=Math.max(i,s,r),a=Math.min(i,s,r);let o,h;const l=(a+n)/2;if(a===n)o=0,h=0;else{const t=n-a;switch(h=l<=.5?t/(n+a):t/(2-n-a),n){case i:o=(s-r)/t+(s<r?6:0);break;case s:o=(r-i)/t+2;break;case r:o=(i-s)/t+4}o/=6}return t.h=o,t.s=h,t.l=l,t}getRGB(t,e=gs.workingColorSpace){return gs.workingToColorSpace(tn.copy(this),e),t.r=tn.r,t.g=tn.g,t.b=tn.b,t}getStyle(t=Ye){gs.workingToColorSpace(tn.copy(this),t);const e=tn.r,i=tn.g,s=tn.b;return t!==Ye?`color(${t} ${e.toFixed(3)} ${i.toFixed(3)} ${s.toFixed(3)})`:`rgb(${Math.round(255*e)},${Math.round(255*i)},${Math.round(255*s)})`}offsetHSL(t,e,i){return this.getHSL(Gr),this.setHSL(Gr.h+t,Gr.s+e,Gr.l+i)}add(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this}addColors(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this}addScalar(t){return this.r+=t,this.g+=t,this.b+=t,this}sub(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this}multiply(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this}multiplyScalar(t){return this.r*=t,this.g*=t,this.b*=t,this}lerp(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this}lerpColors(t,e,i){return this.r=t.r+(e.r-t.r)*i,this.g=t.g+(e.g-t.g)*i,this.b=t.b+(e.b-t.b)*i,this}lerpHSL(t,e){this.getHSL(Gr),t.getHSL($r);const i=Ji(Gr.h,$r.h,e),s=Ji(Gr.s,$r.s,e),r=Ji(Gr.l,$r.l,e);return this.setHSL(i,s,r),this}setFromVector3(t){return this.r=t.x,this.g=t.y,this.b=t.z,this}applyMatrix3(t){const e=this.r,i=this.g,s=this.b,r=t.elements;return this.r=r[0]*e+r[3]*i+r[6]*s,this.g=r[1]*e+r[4]*i+r[7]*s,this.b=r[2]*e+r[5]*i+r[8]*s,this}equals(t){return t.r===this.r&&t.g===this.g&&t.b===this.b}fromArray(t,e=0){return this.r=t[e],this.g=t[e+1],this.b=t[e+2],this}toArray(t=[],e=0){return t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t}fromBufferAttribute(t,e){return this.r=t.getX(e),this.g=t.getY(e),this.b=t.getZ(e),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}}const tn=new Kr;Kr.NAMES=Zr;let en=0;class sn extends Vi{constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:en++}),this.uuid=Di(),this.name="",this.type="Material",this.blending=1,this.side=0,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=204,this.blendDst=205,this.blendEquation=100,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new Kr(0,0,0),this.blendAlpha=0,this.depthFunc=3,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=519,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=Ke,this.stencilZFail=Ke,this.stencilZPass=Ke,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.allowOverride=!0,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}get alphaTest(){return this._alphaTest}set alphaTest(t){this._alphaTest>0!=t>0&&this.version++,this._alphaTest=t}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(t){if(void 0!==t)for(const e in t){const i=t[e];if(void 0===i){console.warn(`THREE.Material: parameter '${e}' has value of undefined.`);continue}const s=this[e];void 0!==s?s&&s.isColor?s.set(i):s&&s.isVector3&&i&&i.isVector3?s.copy(i):this[e]=i:console.warn(`THREE.Material: '${e}' is not a property of THREE.${this.type}.`)}}toJSON(t){const e=void 0===t||"string"==typeof t;e&&(t={textures:{},images:{}});const i={metadata:{version:4.7,type:"Material",generator:"Material.toJSON"}};function s(t){const e=[];for(const i in t){const s=t[i];delete s.metadata,e.push(s)}return e}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),this.color&&this.color.isColor&&(i.color=this.color.getHex()),void 0!==this.roughness&&(i.roughness=this.roughness),void 0!==this.metalness&&(i.metalness=this.metalness),void 0!==this.sheen&&(i.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(i.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(i.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(i.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(i.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(i.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(i.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(i.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(i.shininess=this.shininess),void 0!==this.clearcoat&&(i.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(i.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(i.clearcoatMap=this.clearcoatMap.toJSON(t).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(i.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(t).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(i.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(t).uuid,i.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(i.dispersion=this.dispersion),void 0!==this.iridescence&&(i.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(i.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(i.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(i.iridescenceMap=this.iridescenceMap.toJSON(t).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(i.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(t).uuid),void 0!==this.anisotropy&&(i.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(i.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(i.anisotropyMap=this.anisotropyMap.toJSON(t).uuid),this.map&&this.map.isTexture&&(i.map=this.map.toJSON(t).uuid),this.matcap&&this.matcap.isTexture&&(i.matcap=this.matcap.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(i.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(i.lightMap=this.lightMap.toJSON(t).uuid,i.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(i.aoMap=this.aoMap.toJSON(t).uuid,i.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(i.bumpMap=this.bumpMap.toJSON(t).uuid,i.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(i.normalMap=this.normalMap.toJSON(t).uuid,i.normalMapType=this.normalMapType,i.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(i.displacementMap=this.displacementMap.toJSON(t).uuid,i.displacementScale=this.displacementScale,i.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(i.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(i.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(i.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(i.specularMap=this.specularMap.toJSON(t).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(i.specularIntensityMap=this.specularIntensityMap.toJSON(t).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(i.specularColorMap=this.specularColorMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(i.envMap=this.envMap.toJSON(t).uuid,void 0!==this.combine&&(i.combine=this.combine)),void 0!==this.envMapRotation&&(i.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(i.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(i.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(i.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(i.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.transmission&&(i.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(i.transmissionMap=this.transmissionMap.toJSON(t).uuid),void 0!==this.thickness&&(i.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(i.thicknessMap=this.thicknessMap.toJSON(t).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(i.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(i.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(i.size=this.size),null!==this.shadowSide&&(i.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(i.sizeAttenuation=this.sizeAttenuation),1!==this.blending&&(i.blending=this.blending),0!==this.side&&(i.side=this.side),!0===this.vertexColors&&(i.vertexColors=!0),this.opacity<1&&(i.opacity=this.opacity),!0===this.transparent&&(i.transparent=!0),204!==this.blendSrc&&(i.blendSrc=this.blendSrc),205!==this.blendDst&&(i.blendDst=this.blendDst),100!==this.blendEquation&&(i.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(i.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(i.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(i.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(i.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(i.blendAlpha=this.blendAlpha),3!==this.depthFunc&&(i.depthFunc=this.depthFunc),!1===this.depthTest&&(i.depthTest=this.depthTest),!1===this.depthWrite&&(i.depthWrite=this.depthWrite),!1===this.colorWrite&&(i.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(i.stencilWriteMask=this.stencilWriteMask),519!==this.stencilFunc&&(i.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(i.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(i.stencilFuncMask=this.stencilFuncMask),this.stencilFail!==Ke&&(i.stencilFail=this.stencilFail),this.stencilZFail!==Ke&&(i.stencilZFail=this.stencilZFail),this.stencilZPass!==Ke&&(i.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(i.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(i.rotation=this.rotation),!0===this.polygonOffset&&(i.polygonOffset=!0),0!==this.polygonOffsetFactor&&(i.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(i.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(i.linewidth=this.linewidth),void 0!==this.dashSize&&(i.dashSize=this.dashSize),void 0!==this.gapSize&&(i.gapSize=this.gapSize),void 0!==this.scale&&(i.scale=this.scale),!0===this.dithering&&(i.dithering=!0),this.alphaTest>0&&(i.alphaTest=this.alphaTest),!0===this.alphaHash&&(i.alphaHash=!0),!0===this.alphaToCoverage&&(i.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(i.premultipliedAlpha=!0),!0===this.forceSinglePass&&(i.forceSinglePass=!0),!0===this.wireframe&&(i.wireframe=!0),this.wireframeLinewidth>1&&(i.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(i.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(i.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(i.flatShading=!0),!1===this.visible&&(i.visible=!1),!1===this.toneMapped&&(i.toneMapped=!1),!1===this.fog&&(i.fog=!1),Object.keys(this.userData).length>0&&(i.userData=this.userData),e){const e=s(t.textures),r=s(t.images);e.length>0&&(i.textures=e),r.length>0&&(i.images=r)}return i}clone(){return(new this.constructor).copy(this)}copy(t){this.name=t.name,this.blending=t.blending,this.side=t.side,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.blendColor.copy(t.blendColor),this.blendAlpha=t.blendAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.stencilWriteMask=t.stencilWriteMask,this.stencilFunc=t.stencilFunc,this.stencilRef=t.stencilRef,this.stencilFuncMask=t.stencilFuncMask,this.stencilFail=t.stencilFail,this.stencilZFail=t.stencilZFail,this.stencilZPass=t.stencilZPass,this.stencilWrite=t.stencilWrite;const e=t.clippingPlanes;let i=null;if(null!==e){const t=e.length;i=new Array(t);for(let s=0;s!==t;++s)i[s]=e[s].clone()}return this.clippingPlanes=i,this.clipIntersection=t.clipIntersection,this.clipShadows=t.clipShadows,this.shadowSide=t.shadowSide,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.alphaHash=t.alphaHash,this.alphaToCoverage=t.alphaToCoverage,this.premultipliedAlpha=t.premultipliedAlpha,this.forceSinglePass=t.forceSinglePass,this.visible=t.visible,this.toneMapped=t.toneMapped,this.userData=JSON.parse(JSON.stringify(t.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(t){!0===t&&this.version++}}class rn extends sn{constructor(t){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new Kr(16777215),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new fr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}const nn=an();function an(){const t=new ArrayBuffer(4),e=new Float32Array(t),i=new Uint32Array(t),s=new Uint32Array(512),r=new Uint32Array(512);for(let t=0;t<256;++t){const e=t-127;e<-27?(s[t]=0,s[256|t]=32768,r[t]=24,r[256|t]=24):e<-14?(s[t]=1024>>-e-14,s[256|t]=1024>>-e-14|32768,r[t]=-e-1,r[256|t]=-e-1):e<=15?(s[t]=e+15<<10,s[256|t]=e+15<<10|32768,r[t]=13,r[256|t]=13):e<128?(s[t]=31744,s[256|t]=64512,r[t]=24,r[256|t]=24):(s[t]=31744,s[256|t]=64512,r[t]=13,r[256|t]=13)}const n=new Uint32Array(2048),a=new Uint32Array(64),o=new Uint32Array(64);for(let t=1;t<1024;++t){let e=t<<13,i=0;for(;!(8388608&e);)e<<=1,i-=8388608;e&=-8388609,i+=947912704,n[t]=e|i}for(let t=1024;t<2048;++t)n[t]=939524096+(t-1024<<13);for(let t=1;t<31;++t)a[t]=t<<23;a[31]=1199570944,a[32]=2147483648;for(let t=33;t<63;++t)a[t]=2147483648+(t-32<<23);a[63]=3347054592;for(let t=1;t<64;++t)32!==t&&(o[t]=1024);return{floatView:e,uint32View:i,baseTable:s,shiftTable:r,mantissaTable:n,exponentTable:a,offsetTable:o}}function on(t){Math.abs(t)>65504&&console.warn("THREE.DataUtils.toHalfFloat(): Value out of range."),t=Hi(t,-65504,65504),nn.floatView[0]=t;const e=nn.uint32View[0],i=e>>23&511;return nn.baseTable[i]+((8388607&e)>>nn.shiftTable[i])}function hn(t){const e=t>>10;return nn.uint32View[0]=nn.mantissaTable[nn.offsetTable[e]+(1023&t)]+nn.exponentTable[e],nn.floatView[0]}class ln{static toHalfFloat(t){return on(t)}static fromHalfFloat(t){return hn(t)}}const cn=new Qi,un=new Gi;let dn=0;class pn{constructor(t,e,i=!1){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,Object.defineProperty(this,"id",{value:dn++}),this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=i,this.usage=Mi,this.updateRanges=[],this.gpuType=Et,this.version=0}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,i){t*=this.itemSize,i*=e.itemSize;for(let s=0,r=this.itemSize;s<r;s++)this.array[t+s]=e.array[i+s];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(2===this.itemSize)for(let e=0,i=this.count;e<i;e++)un.fromBufferAttribute(this,e),un.applyMatrix3(t),this.setXY(e,un.x,un.y);else if(3===this.itemSize)for(let e=0,i=this.count;e<i;e++)cn.fromBufferAttribute(this,e),cn.applyMatrix3(t),this.setXYZ(e,cn.x,cn.y,cn.z);return this}applyMatrix4(t){for(let e=0,i=this.count;e<i;e++)cn.fromBufferAttribute(this,e),cn.applyMatrix4(t),this.setXYZ(e,cn.x,cn.y,cn.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)cn.fromBufferAttribute(this,e),cn.applyNormalMatrix(t),this.setXYZ(e,cn.x,cn.y,cn.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)cn.fromBufferAttribute(this,e),cn.transformDirection(t),this.setXYZ(e,cn.x,cn.y,cn.z);return this}set(t,e=0){return this.array.set(t,e),this}getComponent(t,e){let i=this.array[t*this.itemSize+e];return this.normalized&&(i=Xi(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=Yi(i,this.array)),this.array[t*this.itemSize+e]=i,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=Xi(e,this.array)),e}setX(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=Xi(e,this.array)),e}setY(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=Xi(e,this.array)),e}setZ(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=Xi(e,this.array)),e}setW(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array)),this.array[t+0]=e,this.array[t+1]=i,this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array),r=Yi(r,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this.array[t+3]=r,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){const t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(t.name=this.name),this.usage!==Mi&&(t.usage=this.usage),t}}class mn extends pn{constructor(t,e,i){super(new Int8Array(t),e,i)}}class yn extends pn{constructor(t,e,i){super(new Uint8Array(t),e,i)}}class gn extends pn{constructor(t,e,i){super(new Uint8ClampedArray(t),e,i)}}class fn extends pn{constructor(t,e,i){super(new Int16Array(t),e,i)}}class xn extends pn{constructor(t,e,i){super(new Uint16Array(t),e,i)}}class bn extends pn{constructor(t,e,i){super(new Int32Array(t),e,i)}}class vn extends pn{constructor(t,e,i){super(new Uint32Array(t),e,i)}}class wn extends pn{constructor(t,e,i){super(new Uint16Array(t),e,i),this.isFloat16BufferAttribute=!0}getX(t){let e=hn(this.array[t*this.itemSize]);return this.normalized&&(e=Xi(e,this.array)),e}setX(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize]=on(e),this}getY(t){let e=hn(this.array[t*this.itemSize+1]);return this.normalized&&(e=Xi(e,this.array)),e}setY(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+1]=on(e),this}getZ(t){let e=hn(this.array[t*this.itemSize+2]);return this.normalized&&(e=Xi(e,this.array)),e}setZ(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+2]=on(e),this}getW(t){let e=hn(this.array[t*this.itemSize+3]);return this.normalized&&(e=Xi(e,this.array)),e}setW(t,e){return this.normalized&&(e=Yi(e,this.array)),this.array[t*this.itemSize+3]=on(e),this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array)),this.array[t+0]=on(e),this.array[t+1]=on(i),this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array)),this.array[t+0]=on(e),this.array[t+1]=on(i),this.array[t+2]=on(s),this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array),r=Yi(r,this.array)),this.array[t+0]=on(e),this.array[t+1]=on(i),this.array[t+2]=on(s),this.array[t+3]=on(r),this}}class Mn extends pn{constructor(t,e,i){super(new Float32Array(t),e,i)}}let Sn=0;const _n=new or,An=new Pr,Tn=new Qi,zn=new Ps,In=new Ps,Cn=new Qi;class Bn extends Vi{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:Sn++}),this.uuid=Di(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(ss(t)?vn:xn)(t,1):this.index=t,this}setIndirect(t){return this.indirect=t,this}getIndirect(){return this.indirect}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return void 0!==this.attributes[t]}addGroup(t,e,i=0){this.groups.push({start:t,count:e,materialIndex:i})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){const e=this.attributes.position;void 0!==e&&(e.applyMatrix4(t),e.needsUpdate=!0);const i=this.attributes.normal;if(void 0!==i){const e=(new es).getNormalMatrix(t);i.applyNormalMatrix(e),i.needsUpdate=!0}const s=this.attributes.tangent;return void 0!==s&&(s.transformDirection(t),s.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(t){return _n.makeRotationFromQuaternion(t),this.applyMatrix4(_n),this}rotateX(t){return _n.makeRotationX(t),this.applyMatrix4(_n),this}rotateY(t){return _n.makeRotationY(t),this.applyMatrix4(_n),this}rotateZ(t){return _n.makeRotationZ(t),this.applyMatrix4(_n),this}translate(t,e,i){return _n.makeTranslation(t,e,i),this.applyMatrix4(_n),this}scale(t,e,i){return _n.makeScale(t,e,i),this.applyMatrix4(_n),this}lookAt(t){return An.lookAt(t),An.updateMatrix(),this.applyMatrix4(An.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(Tn).negate(),this.translate(Tn.x,Tn.y,Tn.z),this}setFromPoints(t){const e=this.getAttribute("position");if(void 0===e){const e=[];for(let i=0,s=t.length;i<s;i++){const s=t[i];e.push(s.x,s.y,s.z||0)}this.setAttribute("position",new Mn(e,3))}else{const i=Math.min(t.length,e.count);for(let s=0;s<i;s++){const i=t[s];e.setXYZ(s,i.x,i.y,i.z||0)}t.length>e.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),e.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new Ps);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute)return console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),void this.boundingBox.set(new Qi(-1/0,-1/0,-1/0),new Qi(1/0,1/0,1/0));if(void 0!==t){if(this.boundingBox.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){const i=e[t];zn.setFromBufferAttribute(i),this.morphTargetsRelative?(Cn.addVectors(this.boundingBox.min,zn.min),this.boundingBox.expandByPoint(Cn),Cn.addVectors(this.boundingBox.max,zn.max),this.boundingBox.expandByPoint(Cn)):(this.boundingBox.expandByPoint(zn.min),this.boundingBox.expandByPoint(zn.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new Qs);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute)return console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),void this.boundingSphere.set(new Qi,1/0);if(t){const i=this.boundingSphere.center;if(zn.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){const i=e[t];In.setFromBufferAttribute(i),this.morphTargetsRelative?(Cn.addVectors(zn.min,In.min),zn.expandByPoint(Cn),Cn.addVectors(zn.max,In.max),zn.expandByPoint(Cn)):(zn.expandByPoint(In.min),zn.expandByPoint(In.max))}zn.getCenter(i);let s=0;for(let e=0,r=t.count;e<r;e++)Cn.fromBufferAttribute(t,e),s=Math.max(s,i.distanceToSquared(Cn));if(e)for(let r=0,n=e.length;r<n;r++){const n=e[r],a=this.morphTargetsRelative;for(let e=0,r=n.count;e<r;e++)Cn.fromBufferAttribute(n,e),a&&(Tn.fromBufferAttribute(t,e),Cn.add(Tn)),s=Math.max(s,i.distanceToSquared(Cn))}this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){const t=this.index,e=this.attributes;if(null===t||void 0===e.position||void 0===e.normal||void 0===e.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");const i=e.position,s=e.normal,r=e.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new pn(new Float32Array(4*i.count),4));const n=this.getAttribute("tangent"),a=[],o=[];for(let t=0;t<i.count;t++)a[t]=new Qi,o[t]=new Qi;const h=new Qi,l=new Qi,c=new Qi,u=new Gi,d=new Gi,p=new Gi,m=new Qi,y=new Qi;function g(t,e,s){h.fromBufferAttribute(i,t),l.fromBufferAttribute(i,e),c.fromBufferAttribute(i,s),u.fromBufferAttribute(r,t),d.fromBufferAttribute(r,e),p.fromBufferAttribute(r,s),l.sub(h),c.sub(h),d.sub(u),p.sub(u);const n=1/(d.x*p.y-p.x*d.y);isFinite(n)&&(m.copy(l).multiplyScalar(p.y).addScaledVector(c,-d.y).multiplyScalar(n),y.copy(c).multiplyScalar(d.x).addScaledVector(l,-p.x).multiplyScalar(n),a[t].add(m),a[e].add(m),a[s].add(m),o[t].add(y),o[e].add(y),o[s].add(y))}let f=this.groups;0===f.length&&(f=[{start:0,count:t.count}]);for(let e=0,i=f.length;e<i;++e){const i=f[e],s=i.start;for(let e=s,r=s+i.count;e<r;e+=3)g(t.getX(e+0),t.getX(e+1),t.getX(e+2))}const x=new Qi,b=new Qi,v=new Qi,w=new Qi;function M(t){v.fromBufferAttribute(s,t),w.copy(v);const e=a[t];x.copy(e),x.sub(v.multiplyScalar(v.dot(e))).normalize(),b.crossVectors(w,e);const i=b.dot(o[t])<0?-1:1;n.setXYZW(t,x.x,x.y,x.z,i)}for(let e=0,i=f.length;e<i;++e){const i=f[e],s=i.start;for(let e=s,r=s+i.count;e<r;e+=3)M(t.getX(e+0)),M(t.getX(e+1)),M(t.getX(e+2))}}computeVertexNormals(){const t=this.index,e=this.getAttribute("position");if(void 0!==e){let i=this.getAttribute("normal");if(void 0===i)i=new pn(new Float32Array(3*e.count),3),this.setAttribute("normal",i);else for(let t=0,e=i.count;t<e;t++)i.setXYZ(t,0,0,0);const s=new Qi,r=new Qi,n=new Qi,a=new Qi,o=new Qi,h=new Qi,l=new Qi,c=new Qi;if(t)for(let u=0,d=t.count;u<d;u+=3){const d=t.getX(u+0),p=t.getX(u+1),m=t.getX(u+2);s.fromBufferAttribute(e,d),r.fromBufferAttribute(e,p),n.fromBufferAttribute(e,m),l.subVectors(n,r),c.subVectors(s,r),l.cross(c),a.fromBufferAttribute(i,d),o.fromBufferAttribute(i,p),h.fromBufferAttribute(i,m),a.add(l),o.add(l),h.add(l),i.setXYZ(d,a.x,a.y,a.z),i.setXYZ(p,o.x,o.y,o.z),i.setXYZ(m,h.x,h.y,h.z)}else for(let t=0,a=e.count;t<a;t+=3)s.fromBufferAttribute(e,t+0),r.fromBufferAttribute(e,t+1),n.fromBufferAttribute(e,t+2),l.subVectors(n,r),c.subVectors(s,r),l.cross(c),i.setXYZ(t+0,l.x,l.y,l.z),i.setXYZ(t+1,l.x,l.y,l.z),i.setXYZ(t+2,l.x,l.y,l.z);this.normalizeNormals(),i.needsUpdate=!0}}normalizeNormals(){const t=this.attributes.normal;for(let e=0,i=t.count;e<i;e++)Cn.fromBufferAttribute(t,e),Cn.normalize(),t.setXYZ(e,Cn.x,Cn.y,Cn.z)}toNonIndexed(){function t(t,e){const i=t.array,s=t.itemSize,r=t.normalized,n=new i.constructor(e.length*s);let a=0,o=0;for(let r=0,h=e.length;r<h;r++){a=t.isInterleavedBufferAttribute?e[r]*t.data.stride+t.offset:e[r]*s;for(let t=0;t<s;t++)n[o++]=i[a++]}return new pn(n,s,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;const e=new Bn,i=this.index.array,s=this.attributes;for(const r in s){const n=t(s[r],i);e.setAttribute(r,n)}const r=this.morphAttributes;for(const s in r){const n=[],a=r[s];for(let e=0,s=a.length;e<s;e++){const s=t(a[e],i);n.push(s)}e.morphAttributes[s]=n}e.morphTargetsRelative=this.morphTargetsRelative;const n=this.groups;for(let t=0,i=n.length;t<i;t++){const i=n[t];e.addGroup(i.start,i.count,i.materialIndex)}return e}toJSON(){const t={metadata:{version:4.7,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),void 0!==this.parameters){const e=this.parameters;for(const i in e)void 0!==e[i]&&(t[i]=e[i]);return t}t.data={attributes:{}};const e=this.index;null!==e&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});const i=this.attributes;for(const e in i){const s=i[e];t.data.attributes[e]=s.toJSON(t.data)}const s={};let r=!1;for(const e in this.morphAttributes){const i=this.morphAttributes[e],n=[];for(let e=0,s=i.length;e<s;e++){const s=i[e];n.push(s.toJSON(t.data))}n.length>0&&(s[e]=n,r=!0)}r&&(t.data.morphAttributes=s,t.data.morphTargetsRelative=this.morphTargetsRelative);const n=this.groups;n.length>0&&(t.data.groups=JSON.parse(JSON.stringify(n)));const a=this.boundingSphere;return null!==a&&(t.data.boundingSphere=a.toJSON()),t}clone(){return(new this.constructor).copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;const e={};this.name=t.name;const i=t.index;null!==i&&this.setIndex(i.clone());const s=t.attributes;for(const t in s){const i=s[t];this.setAttribute(t,i.clone(e))}const r=t.morphAttributes;for(const t in r){const i=[],s=r[t];for(let t=0,r=s.length;t<r;t++)i.push(s[t].clone(e));this.morphAttributes[t]=i}this.morphTargetsRelative=t.morphTargetsRelative;const n=t.groups;for(let t=0,e=n.length;t<e;t++){const e=n[t];this.addGroup(e.start,e.count,e.materialIndex)}const a=t.boundingBox;null!==a&&(this.boundingBox=a.clone());const o=t.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}const kn=new or,En=new ar,Rn=new Qs,Pn=new Qi,On=new Qi,Nn=new Qi,Fn=new Qi,Vn=new Qi,Ln=new Qi,jn=new Qi,Wn=new Qi;class Un extends Pr{constructor(t=new Bn,e=new rn){super(),this.isMesh=!0,this.type="Mesh",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.count=1,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){const e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}getVertexPosition(t,e){const i=this.geometry,s=i.attributes.position,r=i.morphAttributes.position,n=i.morphTargetsRelative;e.fromBufferAttribute(s,t);const a=this.morphTargetInfluences;if(r&&a){Ln.set(0,0,0);for(let i=0,s=r.length;i<s;i++){const s=a[i],o=r[i];0!==s&&(Vn.fromBufferAttribute(o,t),n?Ln.addScaledVector(Vn,s):Ln.addScaledVector(Vn.sub(e),s))}e.add(Ln)}return e}raycast(t,e){const i=this.geometry,s=this.material,r=this.matrixWorld;if(void 0!==s){if(null===i.boundingSphere&&i.computeBoundingSphere(),Rn.copy(i.boundingSphere),Rn.applyMatrix4(r),En.copy(t.ray).recast(t.near),!1===Rn.containsPoint(En.origin)){if(null===En.intersectSphere(Rn,Pn))return;if(En.origin.distanceToSquared(Pn)>(t.far-t.near)**2)return}kn.copy(r).invert(),En.copy(t.ray).applyMatrix4(kn),null!==i.boundingBox&&!1===En.intersectsBox(i.boundingBox)||this._computeIntersections(t,e,En)}}_computeIntersections(t,e,i){let s;const r=this.geometry,n=this.material,a=r.index,o=r.attributes.position,h=r.attributes.uv,l=r.attributes.uv1,c=r.attributes.normal,u=r.groups,d=r.drawRange;if(null!==a)if(Array.isArray(n))for(let r=0,o=u.length;r<o;r++){const o=u[r],p=n[o.materialIndex];for(let r=Math.max(o.start,d.start),n=Math.min(a.count,Math.min(o.start+o.count,d.start+d.count));r<n;r+=3){s=Dn(this,p,t,i,h,l,c,a.getX(r),a.getX(r+1),a.getX(r+2)),s&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=o.materialIndex,e.push(s))}}else{for(let r=Math.max(0,d.start),o=Math.min(a.count,d.start+d.count);r<o;r+=3){s=Dn(this,n,t,i,h,l,c,a.getX(r),a.getX(r+1),a.getX(r+2)),s&&(s.faceIndex=Math.floor(r/3),e.push(s))}}else if(void 0!==o)if(Array.isArray(n))for(let r=0,a=u.length;r<a;r++){const a=u[r],p=n[a.materialIndex];for(let r=Math.max(a.start,d.start),n=Math.min(o.count,Math.min(a.start+a.count,d.start+d.count));r<n;r+=3){s=Dn(this,p,t,i,h,l,c,r,r+1,r+2),s&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=a.materialIndex,e.push(s))}}else{for(let r=Math.max(0,d.start),a=Math.min(o.count,d.start+d.count);r<a;r+=3){s=Dn(this,n,t,i,h,l,c,r,r+1,r+2),s&&(s.faceIndex=Math.floor(r/3),e.push(s))}}}}function Dn(t,e,i,s,r,n,a,o,h,l){t.getVertexPosition(o,On),t.getVertexPosition(h,Nn),t.getVertexPosition(l,Fn);const c=function(t,e,i,s,r,n,a,o){let h;if(h=1===e.side?s.intersectTriangle(a,n,r,!0,o):s.intersectTriangle(r,n,a,0===e.side,o),null===h)return null;Wn.copy(o),Wn.applyMatrix4(t.matrixWorld);const l=i.ray.origin.distanceTo(Wn);return l<i.near||l>i.far?null:{distance:l,point:Wn.clone(),object:t}}(t,e,i,s,On,Nn,Fn,jn);if(c){const t=new Qi;Yr.getBarycoord(jn,On,Nn,Fn,t),r&&(c.uv=Yr.getInterpolatedAttribute(r,o,h,l,t,new Gi)),n&&(c.uv1=Yr.getInterpolatedAttribute(n,o,h,l,t,new Gi)),a&&(c.normal=Yr.getInterpolatedAttribute(a,o,h,l,t,new Qi),c.normal.dot(s.direction)>0&&c.normal.multiplyScalar(-1));const e={a:o,b:h,c:l,normal:new Qi,materialIndex:0};Yr.getNormal(On,Nn,Fn,e.normal),c.face=e,c.barycoord=t}return c}class Hn extends Bn{constructor(t=1,e=1,i=1,s=1,r=1,n=1){super(),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:s,heightSegments:r,depthSegments:n};const a=this;s=Math.floor(s),r=Math.floor(r),n=Math.floor(n);const o=[],h=[],l=[],c=[];let u=0,d=0;function p(t,e,i,s,r,n,p,m,y,g,f){const x=n/y,b=p/g,v=n/2,w=p/2,M=m/2,S=y+1,_=g+1;let A=0,T=0;const z=new Qi;for(let n=0;n<_;n++){const a=n*b-w;for(let o=0;o<S;o++){const u=o*x-v;z[t]=u*s,z[e]=a*r,z[i]=M,h.push(z.x,z.y,z.z),z[t]=0,z[e]=0,z[i]=m>0?1:-1,l.push(z.x,z.y,z.z),c.push(o/y),c.push(1-n/g),A+=1}}for(let t=0;t<g;t++)for(let e=0;e<y;e++){const i=u+e+S*t,s=u+e+S*(t+1),r=u+(e+1)+S*(t+1),n=u+(e+1)+S*t;o.push(i,s,n),o.push(s,r,n),T+=6}a.addGroup(d,T,f),d+=T,u+=A}p("z","y","x",-1,-1,i,e,t,n,r,0),p("z","y","x",1,-1,i,e,-t,n,r,1),p("x","z","y",1,1,t,i,e,s,n,2),p("x","z","y",1,-1,t,i,-e,s,n,3),p("x","y","z",1,-1,t,e,i,s,r,4),p("x","y","z",-1,-1,t,e,-i,s,r,5),this.setIndex(o),this.setAttribute("position",new Mn(h,3)),this.setAttribute("normal",new Mn(l,3)),this.setAttribute("uv",new Mn(c,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Hn(t.width,t.height,t.depth,t.widthSegments,t.heightSegments,t.depthSegments)}}function qn(t){const e={};for(const i in t){e[i]={};for(const s in t[i]){const r=t[i][s];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),e[i][s]=null):e[i][s]=r.clone():Array.isArray(r)?e[i][s]=r.slice():e[i][s]=r}}return e}function Jn(t){const e={};for(let i=0;i<t.length;i++){const s=qn(t[i]);for(const t in s)e[t]=s[t]}return e}function Xn(t){const e=t.getRenderTarget();return null===e?t.outputColorSpace:!0===e.isXRRenderTarget?e.texture.colorSpace:gs.workingColorSpace}const Yn={clone:qn,merge:Jn};class Zn extends sn{constructor(t){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n\tgl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={clipCullDistance:!1,multiDraw:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==t&&this.setValues(t)}copy(t){return super.copy(t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=qn(t.uniforms),this.uniformsGroups=function(t){const e=[];for(let i=0;i<t.length;i++)e.push(t[i].clone());return e}(t.uniformsGroups),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.fog=t.fog,this.lights=t.lights,this.clipping=t.clipping,this.extensions=Object.assign({},t.extensions),this.glslVersion=t.glslVersion,this}toJSON(t){const e=super.toJSON(t);e.glslVersion=this.glslVersion,e.uniforms={};for(const i in this.uniforms){const s=this.uniforms[i].value;s&&s.isTexture?e.uniforms[i]={type:"t",value:s.toJSON(t).uuid}:s&&s.isColor?e.uniforms[i]={type:"c",value:s.getHex()}:s&&s.isVector2?e.uniforms[i]={type:"v2",value:s.toArray()}:s&&s.isVector3?e.uniforms[i]={type:"v3",value:s.toArray()}:s&&s.isVector4?e.uniforms[i]={type:"v4",value:s.toArray()}:s&&s.isMatrix3?e.uniforms[i]={type:"m3",value:s.toArray()}:s&&s.isMatrix4?e.uniforms[i]={type:"m4",value:s.toArray()}:e.uniforms[i]={value:s}}Object.keys(this.defines).length>0&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e.lights=this.lights,e.clipping=this.clipping;const i={};for(const t in this.extensions)!0===this.extensions[t]&&(i[t]=!0);return Object.keys(i).length>0&&(e.extensions=i),e}}class Gn extends Pr{constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new or,this.projectionMatrix=new or,this.projectionMatrixInverse=new or,this.coordinateSystem=Ri}copy(t,e){return super.copy(t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this.coordinateSystem=t.coordinateSystem,this}getWorldDirection(t){return super.getWorldDirection(t).negate()}updateMatrixWorld(t){super.updateMatrixWorld(t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(t,e){super.updateWorldMatrix(t,e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return(new this.constructor).copy(this)}}const $n=new Qi,Qn=new Gi,Kn=new Gi;class ta extends Gn{constructor(t=50,e=1,i=.1,s=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=t,this.zoom=1,this.near=i,this.far=s,this.focus=10,this.aspect=e,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this}setFocalLength(t){const e=.5*this.getFilmHeight()/t;this.fov=2*Ui*Math.atan(e),this.updateProjectionMatrix()}getFocalLength(){const t=Math.tan(.5*Wi*this.fov);return.5*this.getFilmHeight()/t}getEffectiveFOV(){return 2*Ui*Math.atan(Math.tan(.5*Wi*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(t,e,i){$n.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),e.set($n.x,$n.y).multiplyScalar(-t/$n.z),$n.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),i.set($n.x,$n.y).multiplyScalar(-t/$n.z)}getViewSize(t,e){return this.getViewBounds(t,Qn,Kn),e.subVectors(Kn,Qn)}setViewOffset(t,e,i,s,r,n){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){const t=this.near;let e=t*Math.tan(.5*Wi*this.fov)/this.zoom,i=2*e,s=this.aspect*i,r=-.5*s;const n=this.view;if(null!==this.view&&this.view.enabled){const t=n.fullWidth,a=n.fullHeight;r+=n.offsetX*s/t,e-=n.offsetY*i/a,s*=n.width/t,i*=n.height/a}const a=this.filmOffset;0!==a&&(r+=t*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+s,e,e-i,t,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){const e=super.toJSON(t);return e.object.fov=this.fov,e.object.zoom=this.zoom,e.object.near=this.near,e.object.far=this.far,e.object.focus=this.focus,e.object.aspect=this.aspect,null!==this.view&&(e.object.view=Object.assign({},this.view)),e.object.filmGauge=this.filmGauge,e.object.filmOffset=this.filmOffset,e}}const ea=-90;class ia extends Pr{constructor(t,e,i){super(),this.type="CubeCamera",this.renderTarget=i,this.coordinateSystem=null,this.activeMipmapLevel=0;const s=new ta(ea,1,t,e);s.layers=this.layers,this.add(s);const r=new ta(ea,1,t,e);r.layers=this.layers,this.add(r);const n=new ta(ea,1,t,e);n.layers=this.layers,this.add(n);const a=new ta(ea,1,t,e);a.layers=this.layers,this.add(a);const o=new ta(ea,1,t,e);o.layers=this.layers,this.add(o);const h=new ta(ea,1,t,e);h.layers=this.layers,this.add(h)}updateCoordinateSystem(){const t=this.coordinateSystem,e=this.children.concat(),[i,s,r,n,a,o]=e;for(const t of e)this.remove(t);if(t===Ri)i.up.set(0,1,0),i.lookAt(1,0,0),s.up.set(0,1,0),s.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),n.up.set(0,0,1),n.lookAt(0,-1,0),a.up.set(0,1,0),a.lookAt(0,0,1),o.up.set(0,1,0),o.lookAt(0,0,-1);else{if(t!==Pi)throw new Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+t);i.up.set(0,-1,0),i.lookAt(-1,0,0),s.up.set(0,-1,0),s.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),n.up.set(0,0,-1),n.lookAt(0,-1,0),a.up.set(0,-1,0),a.lookAt(0,0,1),o.up.set(0,-1,0),o.lookAt(0,0,-1)}for(const t of e)this.add(t),t.updateMatrixWorld()}update(t,e){null===this.parent&&this.updateMatrixWorld();const{renderTarget:i,activeMipmapLevel:s}=this;this.coordinateSystem!==t.coordinateSystem&&(this.coordinateSystem=t.coordinateSystem,this.updateCoordinateSystem());const[r,n,a,o,h,l]=this.children,c=t.getRenderTarget(),u=t.getActiveCubeFace(),d=t.getActiveMipmapLevel(),p=t.xr.enabled;t.xr.enabled=!1;const m=i.texture.generateMipmaps;i.texture.generateMipmaps=!1,t.setRenderTarget(i,0,s),t.render(e,r),t.setRenderTarget(i,1,s),t.render(e,n),t.setRenderTarget(i,2,s),t.render(e,a),t.setRenderTarget(i,3,s),t.render(e,o),t.setRenderTarget(i,4,s),t.render(e,h),i.texture.generateMipmaps=m,t.setRenderTarget(i,5,s),t.render(e,l),t.setRenderTarget(c,u,d),t.xr.enabled=p,i.texture.needsPMREMUpdate=!0}}class sa extends Ts{constructor(t=[],e=301,i,s,r,n,a,o,h,l){super(t,e,i,s,r,n,a,o,h,l),this.isCubeTexture=!0,this.flipY=!1}get images(){return this.image}set images(t){this.image=t}}class ra extends Cs{constructor(t=1,e={}){super(t,t,e),this.isWebGLCubeRenderTarget=!0;const i={width:t,height:t,depth:1},s=[i,i,i,i,i,i];this.texture=new sa(s),this._setTextureOptions(e),this.texture.isRenderTargetTexture=!0}fromEquirectangularTexture(t,e){this.texture.type=e.type,this.texture.colorSpace=e.colorSpace,this.texture.generateMipmaps=e.generateMipmaps,this.texture.minFilter=e.minFilter,this.texture.magFilter=e.magFilter;const i={uniforms:{tEquirect:{value:null}},vertexShader:"\n\n\t\t\t\tvarying vec3 vWorldDirection;\n\n\t\t\t\tvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\n\t\t\t\t\treturn normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvWorldDirection = transformDirection( position, modelMatrix );\n\n\t\t\t\t\t#include <begin_vertex>\n\t\t\t\t\t#include <project_vertex>\n\n\t\t\t\t}\n\t\t\t",fragmentShader:"\n\n\t\t\t\tuniform sampler2D tEquirect;\n\n\t\t\t\tvarying vec3 vWorldDirection;\n\n\t\t\t\t#include <common>\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec3 direction = normalize( vWorldDirection );\n\n\t\t\t\t\tvec2 sampleUV = equirectUv( direction );\n\n\t\t\t\t\tgl_FragColor = texture2D( tEquirect, sampleUV );\n\n\t\t\t\t}\n\t\t\t"},s=new Hn(5,5,5),r=new Zn({name:"CubemapFromEquirect",uniforms:qn(i.uniforms),vertexShader:i.vertexShader,fragmentShader:i.fragmentShader,side:1,blending:0});r.uniforms.tEquirect.value=e;const n=new Un(s,r),a=e.minFilter;e.minFilter===_t&&(e.minFilter=wt);return new ia(1,10,this).update(t,n),e.minFilter=a,n.geometry.dispose(),n.material.dispose(),this}clear(t,e=!0,i=!0,s=!0){const r=t.getRenderTarget();for(let r=0;r<6;r++)t.setRenderTarget(this,r),t.clear(e,i,s);t.setRenderTarget(r)}}class na extends Pr{constructor(){super(),this.isGroup=!0,this.type="Group"}}const aa={type:"move"};class oa{constructor(){this._targetRay=null,this._grip=null,this._hand=null}getHandSpace(){return null===this._hand&&(this._hand=new na,this._hand.matrixAutoUpdate=!1,this._hand.visible=!1,this._hand.joints={},this._hand.inputState={pinching:!1}),this._hand}getTargetRaySpace(){return null===this._targetRay&&(this._targetRay=new na,this._targetRay.matrixAutoUpdate=!1,this._targetRay.visible=!1,this._targetRay.hasLinearVelocity=!1,this._targetRay.linearVelocity=new Qi,this._targetRay.hasAngularVelocity=!1,this._targetRay.angularVelocity=new Qi),this._targetRay}getGripSpace(){return null===this._grip&&(this._grip=new na,this._grip.matrixAutoUpdate=!1,this._grip.visible=!1,this._grip.hasLinearVelocity=!1,this._grip.linearVelocity=new Qi,this._grip.hasAngularVelocity=!1,this._grip.angularVelocity=new Qi),this._grip}dispatchEvent(t){return null!==this._targetRay&&this._targetRay.dispatchEvent(t),null!==this._grip&&this._grip.dispatchEvent(t),null!==this._hand&&this._hand.dispatchEvent(t),this}connect(t){if(t&&t.hand){const e=this._hand;if(e)for(const i of t.hand.values())this._getHandJoint(e,i)}return this.dispatchEvent({type:"connected",data:t}),this}disconnect(t){return this.dispatchEvent({type:"disconnected",data:t}),null!==this._targetRay&&(this._targetRay.visible=!1),null!==this._grip&&(this._grip.visible=!1),null!==this._hand&&(this._hand.visible=!1),this}update(t,e,i){let s=null,r=null,n=null;const a=this._targetRay,o=this._grip,h=this._hand;if(t&&"visible-blurred"!==e.session.visibilityState){if(h&&t.hand){n=!0;for(const s of t.hand.values()){const t=e.getJointPose(s,i),r=this._getHandJoint(h,s);null!==t&&(r.matrix.fromArray(t.transform.matrix),r.matrix.decompose(r.position,r.rotation,r.scale),r.matrixWorldNeedsUpdate=!0,r.jointRadius=t.radius),r.visible=null!==t}const s=h.joints["index-finger-tip"],r=h.joints["thumb-tip"],a=s.position.distanceTo(r.position),o=.02,l=.005;h.inputState.pinching&&a>o+l?(h.inputState.pinching=!1,this.dispatchEvent({type:"pinchend",handedness:t.handedness,target:this})):!h.inputState.pinching&&a<=o-l&&(h.inputState.pinching=!0,this.dispatchEvent({type:"pinchstart",handedness:t.handedness,target:this}))}else null!==o&&t.gripSpace&&(r=e.getPose(t.gripSpace,i),null!==r&&(o.matrix.fromArray(r.transform.matrix),o.matrix.decompose(o.position,o.rotation,o.scale),o.matrixWorldNeedsUpdate=!0,r.linearVelocity?(o.hasLinearVelocity=!0,o.linearVelocity.copy(r.linearVelocity)):o.hasLinearVelocity=!1,r.angularVelocity?(o.hasAngularVelocity=!0,o.angularVelocity.copy(r.angularVelocity)):o.hasAngularVelocity=!1));null!==a&&(s=e.getPose(t.targetRaySpace,i),null===s&&null!==r&&(s=r),null!==s&&(a.matrix.fromArray(s.transform.matrix),a.matrix.decompose(a.position,a.rotation,a.scale),a.matrixWorldNeedsUpdate=!0,s.linearVelocity?(a.hasLinearVelocity=!0,a.linearVelocity.copy(s.linearVelocity)):a.hasLinearVelocity=!1,s.angularVelocity?(a.hasAngularVelocity=!0,a.angularVelocity.copy(s.angularVelocity)):a.hasAngularVelocity=!1,this.dispatchEvent(aa)))}return null!==a&&(a.visible=null!==s),null!==o&&(o.visible=null!==r),null!==h&&(h.visible=null!==n),this}_getHandJoint(t,e){if(void 0===t.joints[e.jointName]){const i=new na;i.matrixAutoUpdate=!1,i.visible=!1,t.joints[e.jointName]=i,t.add(i)}return t.joints[e.jointName]}}class ha{constructor(t,e=25e-5){this.isFogExp2=!0,this.name="",this.color=new Kr(t),this.density=e}clone(){return new ha(this.color,this.density)}toJSON(){return{type:"FogExp2",name:this.name,color:this.color.getHex(),density:this.density}}}class la{constructor(t,e=1,i=1e3){this.isFog=!0,this.name="",this.color=new Kr(t),this.near=e,this.far=i}clone(){return new la(this.color,this.near,this.far)}toJSON(){return{type:"Fog",name:this.name,color:this.color.getHex(),near:this.near,far:this.far}}}class ca extends Pr{constructor(){super(),this.isScene=!0,this.type="Scene",this.background=null,this.environment=null,this.fog=null,this.backgroundBlurriness=0,this.backgroundIntensity=1,this.backgroundRotation=new fr,this.environmentIntensity=1,this.environmentRotation=new fr,this.overrideMaterial=null,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}copy(t,e){return super.copy(t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.environment&&(this.environment=t.environment.clone()),null!==t.fog&&(this.fog=t.fog.clone()),this.backgroundBlurriness=t.backgroundBlurriness,this.backgroundIntensity=t.backgroundIntensity,this.backgroundRotation.copy(t.backgroundRotation),this.environmentIntensity=t.environmentIntensity,this.environmentRotation.copy(t.environmentRotation),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.matrixAutoUpdate=t.matrixAutoUpdate,this}toJSON(t){const e=super.toJSON(t);return null!==this.fog&&(e.object.fog=this.fog.toJSON()),this.backgroundBlurriness>0&&(e.object.backgroundBlurriness=this.backgroundBlurriness),1!==this.backgroundIntensity&&(e.object.backgroundIntensity=this.backgroundIntensity),e.object.backgroundRotation=this.backgroundRotation.toArray(),1!==this.environmentIntensity&&(e.object.environmentIntensity=this.environmentIntensity),e.object.environmentRotation=this.environmentRotation.toArray(),e}}class ua{constructor(t,e){this.isInterleavedBuffer=!0,this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.usage=Mi,this.updateRanges=[],this.version=0,this.uuid=Di()}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.usage=t.usage,this}copyAt(t,e,i){t*=this.stride,i*=e.stride;for(let s=0,r=this.stride;s<r;s++)this.array[t+s]=e.array[i+s];return this}set(t,e=0){return this.array.set(t,e),this}clone(t){void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=Di()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=this.array.slice(0).buffer);const e=new this.array.constructor(t.arrayBuffers[this.array.buffer._uuid]),i=new this.constructor(e,this.stride);return i.setUsage(this.usage),i}onUpload(t){return this.onUploadCallback=t,this}toJSON(t){return void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=Di()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=Array.from(new Uint32Array(this.array.buffer))),{uuid:this.uuid,buffer:this.array.buffer._uuid,type:this.array.constructor.name,stride:this.stride}}}const da=new Qi;class pa{constructor(t,e,i,s=!1){this.isInterleavedBufferAttribute=!0,this.name="",this.data=t,this.itemSize=e,this.offset=i,this.normalized=s}get count(){return this.data.count}get array(){return this.data.array}set needsUpdate(t){this.data.needsUpdate=t}applyMatrix4(t){for(let e=0,i=this.data.count;e<i;e++)da.fromBufferAttribute(this,e),da.applyMatrix4(t),this.setXYZ(e,da.x,da.y,da.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)da.fromBufferAttribute(this,e),da.applyNormalMatrix(t),this.setXYZ(e,da.x,da.y,da.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)da.fromBufferAttribute(this,e),da.transformDirection(t),this.setXYZ(e,da.x,da.y,da.z);return this}getComponent(t,e){let i=this.array[t*this.data.stride+this.offset+e];return this.normalized&&(i=Xi(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=Yi(i,this.array)),this.data.array[t*this.data.stride+this.offset+e]=i,this}setX(t,e){return this.normalized&&(e=Yi(e,this.array)),this.data.array[t*this.data.stride+this.offset]=e,this}setY(t,e){return this.normalized&&(e=Yi(e,this.array)),this.data.array[t*this.data.stride+this.offset+1]=e,this}setZ(t,e){return this.normalized&&(e=Yi(e,this.array)),this.data.array[t*this.data.stride+this.offset+2]=e,this}setW(t,e){return this.normalized&&(e=Yi(e,this.array)),this.data.array[t*this.data.stride+this.offset+3]=e,this}getX(t){let e=this.data.array[t*this.data.stride+this.offset];return this.normalized&&(e=Xi(e,this.array)),e}getY(t){let e=this.data.array[t*this.data.stride+this.offset+1];return this.normalized&&(e=Xi(e,this.array)),e}getZ(t){let e=this.data.array[t*this.data.stride+this.offset+2];return this.normalized&&(e=Xi(e,this.array)),e}getW(t){let e=this.data.array[t*this.data.stride+this.offset+3];return this.normalized&&(e=Xi(e,this.array)),e}setXY(t,e,i){return t=t*this.data.stride+this.offset,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this}setXYZ(t,e,i,s){return t=t*this.data.stride+this.offset,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t=t*this.data.stride+this.offset,this.normalized&&(e=Yi(e,this.array),i=Yi(i,this.array),s=Yi(s,this.array),r=Yi(r,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this.data.array[t+3]=r,this}clone(t){if(void 0===t){console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");const t=[];for(let e=0;e<this.count;e++){const i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return new pn(new this.array.constructor(t),this.itemSize,this.normalized)}return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.clone(t)),new pa(t.interleavedBuffers[this.data.uuid],this.itemSize,this.offset,this.normalized)}toJSON(t){if(void 0===t){console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");const t=[];for(let e=0;e<this.count;e++){const i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return{itemSize:this.itemSize,type:this.array.constructor.name,array:t,normalized:this.normalized}}return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.toJSON(t)),{isInterleavedBufferAttribute:!0,itemSize:this.itemSize,data:this.data.uuid,offset:this.offset,normalized:this.normalized}}}class ma extends sn{constructor(t){super(),this.isSpriteMaterial=!0,this.type="SpriteMaterial",this.color=new Kr(16777215),this.map=null,this.alphaMap=null,this.rotation=0,this.sizeAttenuation=!0,this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.rotation=t.rotation,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}let ya;const ga=new Qi,fa=new Qi,xa=new Qi,ba=new Gi,va=new Gi,wa=new or,Ma=new Qi,Sa=new Qi,_a=new Qi,Aa=new Gi,Ta=new Gi,za=new Gi;class Ia extends Pr{constructor(t=new ma){if(super(),this.isSprite=!0,this.type="Sprite",void 0===ya){ya=new Bn;const t=new Float32Array([-.5,-.5,0,0,0,.5,-.5,0,1,0,.5,.5,0,1,1,-.5,.5,0,0,1]),e=new ua(t,5);ya.setIndex([0,1,2,0,2,3]),ya.setAttribute("position",new pa(e,3,0,!1)),ya.setAttribute("uv",new pa(e,2,3,!1))}this.geometry=ya,this.material=t,this.center=new Gi(.5,.5),this.count=1}raycast(t,e){null===t.camera&&console.error('THREE.Sprite: "Raycaster.camera" needs to be set in order to raycast against sprites.'),fa.setFromMatrixScale(this.matrixWorld),wa.copy(t.camera.matrixWorld),this.modelViewMatrix.multiplyMatrices(t.camera.matrixWorldInverse,this.matrixWorld),xa.setFromMatrixPosition(this.modelViewMatrix),t.camera.isPerspectiveCamera&&!1===this.material.sizeAttenuation&&fa.multiplyScalar(-xa.z);const i=this.material.rotation;let s,r;0!==i&&(r=Math.cos(i),s=Math.sin(i));const n=this.center;Ca(Ma.set(-.5,-.5,0),xa,n,fa,s,r),Ca(Sa.set(.5,-.5,0),xa,n,fa,s,r),Ca(_a.set(.5,.5,0),xa,n,fa,s,r),Aa.set(0,0),Ta.set(1,0),za.set(1,1);let a=t.ray.intersectTriangle(Ma,Sa,_a,!1,ga);if(null===a&&(Ca(Sa.set(-.5,.5,0),xa,n,fa,s,r),Ta.set(0,1),a=t.ray.intersectTriangle(Ma,_a,Sa,!1,ga),null===a))return;const o=t.ray.origin.distanceTo(ga);o<t.near||o>t.far||e.push({distance:o,point:ga.clone(),uv:Yr.getInterpolation(ga,Ma,Sa,_a,Aa,Ta,za,new Gi),face:null,object:this})}copy(t,e){return super.copy(t,e),void 0!==t.center&&this.center.copy(t.center),this.material=t.material,this}}function Ca(t,e,i,s,r,n){ba.subVectors(t,i).addScalar(.5).multiply(s),void 0!==r?(va.x=n*ba.x-r*ba.y,va.y=r*ba.x+n*ba.y):va.copy(ba),t.copy(e),t.x+=va.x,t.y+=va.y,t.applyMatrix4(wa)}const Ba=new Qi,ka=new Qi;class Ea extends Pr{constructor(){super(),this.isLOD=!0,this._currentLevel=0,this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]}}),this.autoUpdate=!0}copy(t){super.copy(t,!1);const e=t.levels;for(let t=0,i=e.length;t<i;t++){const i=e[t];this.addLevel(i.object.clone(),i.distance,i.hysteresis)}return this.autoUpdate=t.autoUpdate,this}addLevel(t,e=0,i=0){e=Math.abs(e);const s=this.levels;let r;for(r=0;r<s.length&&!(e<s[r].distance);r++);return s.splice(r,0,{distance:e,hysteresis:i,object:t}),this.add(t),this}removeLevel(t){const e=this.levels;for(let i=0;i<e.length;i++)if(e[i].distance===t){const t=e.splice(i,1);return this.remove(t[0].object),!0}return!1}getCurrentLevel(){return this._currentLevel}getObjectForDistance(t){const e=this.levels;if(e.length>0){let i,s;for(i=1,s=e.length;i<s;i++){let s=e[i].distance;if(e[i].object.visible&&(s-=s*e[i].hysteresis),t<s)break}return e[i-1].object}return null}raycast(t,e){if(this.levels.length>0){Ba.setFromMatrixPosition(this.matrixWorld);const i=t.ray.origin.distanceTo(Ba);this.getObjectForDistance(i).raycast(t,e)}}update(t){const e=this.levels;if(e.length>1){Ba.setFromMatrixPosition(t.matrixWorld),ka.setFromMatrixPosition(this.matrixWorld);const i=Ba.distanceTo(ka)/t.zoom;let s,r;for(e[0].object.visible=!0,s=1,r=e.length;s<r;s++){let t=e[s].distance;if(e[s].object.visible&&(t-=t*e[s].hysteresis),!(i>=t))break;e[s-1].object.visible=!1,e[s].object.visible=!0}for(this._currentLevel=s-1;s<r;s++)e[s].object.visible=!1}}toJSON(t){const e=super.toJSON(t);!1===this.autoUpdate&&(e.object.autoUpdate=!1),e.object.levels=[];const i=this.levels;for(let t=0,s=i.length;t<s;t++){const s=i[t];e.object.levels.push({object:s.object.uuid,distance:s.distance,hysteresis:s.hysteresis})}return e}}const Ra=new Qi,Pa=new zs,Oa=new zs,Na=new Qi,Fa=new or,Va=new Qi,La=new Qs,ja=new or,Wa=new ar;class Ua extends Un{constructor(t,e){super(t,e),this.isSkinnedMesh=!0,this.type="SkinnedMesh",this.bindMode=nt,this.bindMatrix=new or,this.bindMatrixInverse=new or,this.boundingBox=null,this.boundingSphere=null}computeBoundingBox(){const t=this.geometry;null===this.boundingBox&&(this.boundingBox=new Ps),this.boundingBox.makeEmpty();const e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,Va),this.boundingBox.expandByPoint(Va)}computeBoundingSphere(){const t=this.geometry;null===this.boundingSphere&&(this.boundingSphere=new Qs),this.boundingSphere.makeEmpty();const e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,Va),this.boundingSphere.expandByPoint(Va)}copy(t,e){return super.copy(t,e),this.bindMode=t.bindMode,this.bindMatrix.copy(t.bindMatrix),this.bindMatrixInverse.copy(t.bindMatrixInverse),this.skeleton=t.skeleton,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}raycast(t,e){const i=this.material,s=this.matrixWorld;void 0!==i&&(null===this.boundingSphere&&this.computeBoundingSphere(),La.copy(this.boundingSphere),La.applyMatrix4(s),!1!==t.ray.intersectsSphere(La)&&(ja.copy(s).invert(),Wa.copy(t.ray).applyMatrix4(ja),null!==this.boundingBox&&!1===Wa.intersectsBox(this.boundingBox)||this._computeIntersections(t,e,Wa)))}getVertexPosition(t,e){return super.getVertexPosition(t,e),this.applyBoneTransform(t,e),e}bind(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.copy(e).invert()}pose(){this.skeleton.pose()}normalizeSkinWeights(){const t=new zs,e=this.geometry.attributes.skinWeight;for(let i=0,s=e.count;i<s;i++){t.fromBufferAttribute(e,i);const s=1/t.manhattanLength();s!==1/0?t.multiplyScalar(s):t.set(1,0,0,0),e.setXYZW(i,t.x,t.y,t.z,t.w)}}updateMatrixWorld(t){super.updateMatrixWorld(t),this.bindMode===nt?this.bindMatrixInverse.copy(this.matrixWorld).invert():this.bindMode===at?this.bindMatrixInverse.copy(this.bindMatrix).invert():console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}applyBoneTransform(t,e){const i=this.skeleton,s=this.geometry;Pa.fromBufferAttribute(s.attributes.skinIndex,t),Oa.fromBufferAttribute(s.attributes.skinWeight,t),Ra.copy(e).applyMatrix4(this.bindMatrix),e.set(0,0,0);for(let t=0;t<4;t++){const s=Oa.getComponent(t);if(0!==s){const r=Pa.getComponent(t);Fa.multiplyMatrices(i.bones[r].matrixWorld,i.boneInverses[r]),e.addScaledVector(Na.copy(Ra).applyMatrix4(Fa),s)}}return e.applyMatrix4(this.bindMatrixInverse)}}class Da extends Pr{constructor(){super(),this.isBone=!0,this.type="Bone"}}class Ha extends Ts{constructor(t=null,e=1,i=1,s,r,n,a,o,h=1003,l=1003,c,u){super(null,n,a,o,h,l,s,r,c,u),this.isDataTexture=!0,this.image={data:t,width:e,height:i},this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}const qa=new or,Ja=new or;class Xa{constructor(t=[],e=[]){this.uuid=Di(),this.bones=t.slice(0),this.boneInverses=e,this.boneMatrices=null,this.boneTexture=null,this.init()}init(){const t=this.bones,e=this.boneInverses;if(this.boneMatrices=new Float32Array(16*t.length),0===e.length)this.calculateInverses();else if(t.length!==e.length){console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."),this.boneInverses=[];for(let t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new or)}}calculateInverses(){this.boneInverses.length=0;for(let t=0,e=this.bones.length;t<e;t++){const e=new or;this.bones[t]&&e.copy(this.bones[t].matrixWorld).invert(),this.boneInverses.push(e)}}pose(){for(let t=0,e=this.bones.length;t<e;t++){const e=this.bones[t];e&&e.matrixWorld.copy(this.boneInverses[t]).invert()}for(let t=0,e=this.bones.length;t<e;t++){const e=this.bones[t];e&&(e.parent&&e.parent.isBone?(e.matrix.copy(e.parent.matrixWorld).invert(),e.matrix.multiply(e.matrixWorld)):e.matrix.copy(e.matrixWorld),e.matrix.decompose(e.position,e.quaternion,e.scale))}}update(){const t=this.bones,e=this.boneInverses,i=this.boneMatrices,s=this.boneTexture;for(let s=0,r=t.length;s<r;s++){const r=t[s]?t[s].matrixWorld:Ja;qa.multiplyMatrices(r,e[s]),qa.toArray(i,16*s)}null!==s&&(s.needsUpdate=!0)}clone(){return new Xa(this.bones,this.boneInverses)}computeBoneTexture(){let t=Math.sqrt(4*this.bones.length);t=4*Math.ceil(t/4),t=Math.max(t,4);const e=new Float32Array(t*t*4);e.set(this.boneMatrices);const i=new Ha(e,t,t,jt,Et);return i.needsUpdate=!0,this.boneMatrices=e,this.boneTexture=i,this}getBoneByName(t){for(let e=0,i=this.bones.length;e<i;e++){const i=this.bones[e];if(i.name===t)return i}}dispose(){null!==this.boneTexture&&(this.boneTexture.dispose(),this.boneTexture=null)}fromJSON(t,e){this.uuid=t.uuid;for(let i=0,s=t.bones.length;i<s;i++){const s=t.bones[i];let r=e[s];void 0===r&&(console.warn("THREE.Skeleton: No bone found with UUID:",s),r=new Da),this.bones.push(r),this.boneInverses.push((new or).fromArray(t.boneInverses[i]))}return this.init(),this}toJSON(){const t={metadata:{version:4.7,type:"Skeleton",generator:"Skeleton.toJSON"},bones:[],boneInverses:[]};t.uuid=this.uuid;const e=this.bones,i=this.boneInverses;for(let s=0,r=e.length;s<r;s++){const r=e[s];t.bones.push(r.uuid);const n=i[s];t.boneInverses.push(n.toArray())}return t}}class Ya extends pn{constructor(t,e,i,s=1){super(t,e,i),this.isInstancedBufferAttribute=!0,this.meshPerAttribute=s}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}toJSON(){const t=super.toJSON();return t.meshPerAttribute=this.meshPerAttribute,t.isInstancedBufferAttribute=!0,t}}const Za=new or,Ga=new or,$a=[],Qa=new Ps,Ka=new or,to=new Un,eo=new Qs;class io extends Un{constructor(t,e,i){super(t,e),this.isInstancedMesh=!0,this.instanceMatrix=new Ya(new Float32Array(16*i),16),this.instanceColor=null,this.morphTexture=null,this.count=i,this.boundingBox=null,this.boundingSphere=null;for(let t=0;t<i;t++)this.setMatrixAt(t,Ka)}computeBoundingBox(){const t=this.geometry,e=this.count;null===this.boundingBox&&(this.boundingBox=new Ps),null===t.boundingBox&&t.computeBoundingBox(),this.boundingBox.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,Za),Qa.copy(t.boundingBox).applyMatrix4(Za),this.boundingBox.union(Qa)}computeBoundingSphere(){const t=this.geometry,e=this.count;null===this.boundingSphere&&(this.boundingSphere=new Qs),null===t.boundingSphere&&t.computeBoundingSphere(),this.boundingSphere.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,Za),eo.copy(t.boundingSphere).applyMatrix4(Za),this.boundingSphere.union(eo)}copy(t,e){return super.copy(t,e),this.instanceMatrix.copy(t.instanceMatrix),null!==t.morphTexture&&(this.morphTexture=t.morphTexture.clone()),null!==t.instanceColor&&(this.instanceColor=t.instanceColor.clone()),this.count=t.count,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}getColorAt(t,e){e.fromArray(this.instanceColor.array,3*t)}getMatrixAt(t,e){e.fromArray(this.instanceMatrix.array,16*t)}getMorphAt(t,e){const i=e.morphTargetInfluences,s=this.morphTexture.source.data.data,r=t*(i.length+1)+1;for(let t=0;t<i.length;t++)i[t]=s[r+t]}raycast(t,e){const i=this.matrixWorld,s=this.count;if(to.geometry=this.geometry,to.material=this.material,void 0!==to.material&&(null===this.boundingSphere&&this.computeBoundingSphere(),eo.copy(this.boundingSphere),eo.applyMatrix4(i),!1!==t.ray.intersectsSphere(eo)))for(let r=0;r<s;r++){this.getMatrixAt(r,Za),Ga.multiplyMatrices(i,Za),to.matrixWorld=Ga,to.raycast(t,$a);for(let t=0,i=$a.length;t<i;t++){const i=$a[t];i.instanceId=r,i.object=this,e.push(i)}$a.length=0}}setColorAt(t,e){null===this.instanceColor&&(this.instanceColor=new Ya(new Float32Array(3*this.instanceMatrix.count).fill(1),3)),e.toArray(this.instanceColor.array,3*t)}setMatrixAt(t,e){e.toArray(this.instanceMatrix.array,16*t)}setMorphAt(t,e){const i=e.morphTargetInfluences,s=i.length+1;null===this.morphTexture&&(this.morphTexture=new Ha(new Float32Array(s*this.count),s,this.count,Dt,Et));const r=this.morphTexture.source.data.data;let n=0;for(let t=0;t<i.length;t++)n+=i[t];const a=this.geometry.morphTargetsRelative?1:1-n,o=s*t;r[o]=a,r.set(i,o+1)}updateMorphTargets(){}dispose(){this.dispatchEvent({type:"dispose"}),null!==this.morphTexture&&(this.morphTexture.dispose(),this.morphTexture=null)}}const so=new Qi,ro=new Qi,no=new es;class ao{constructor(t=new Qi(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,i,s){return this.normal.set(t,e,i),this.constant=s,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,i){const s=so.subVectors(i,e).cross(ro.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(s,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){const t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){const i=t.delta(so),s=this.normal.dot(i);if(0===s)return 0===this.distanceToPoint(t.start)?e.copy(t.start):null;const r=-(t.start.dot(this.normal)+this.constant)/s;return r<0||r>1?null:e.copy(t.start).addScaledVector(i,r)}intersectsLine(t){const e=this.distanceToPoint(t.start),i=this.distanceToPoint(t.end);return e<0&&i>0||i<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){const i=e||no.getNormalMatrix(t),s=this.coplanarPoint(so).applyMatrix4(t),r=this.normal.applyMatrix3(i).normalize();return this.constant=-s.dot(r),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return(new this.constructor).copy(this)}}const oo=new Qs,ho=new Gi(.5,.5),lo=new Qi;class co{constructor(t=new ao,e=new ao,i=new ao,s=new ao,r=new ao,n=new ao){this.planes=[t,e,i,s,r,n]}set(t,e,i,s,r,n){const a=this.planes;return a[0].copy(t),a[1].copy(e),a[2].copy(i),a[3].copy(s),a[4].copy(r),a[5].copy(n),this}copy(t){const e=this.planes;for(let i=0;i<6;i++)e[i].copy(t.planes[i]);return this}setFromProjectionMatrix(t,e=2e3){const i=this.planes,s=t.elements,r=s[0],n=s[1],a=s[2],o=s[3],h=s[4],l=s[5],c=s[6],u=s[7],d=s[8],p=s[9],m=s[10],y=s[11],g=s[12],f=s[13],x=s[14],b=s[15];if(i[0].setComponents(o-r,u-h,y-d,b-g).normalize(),i[1].setComponents(o+r,u+h,y+d,b+g).normalize(),i[2].setComponents(o+n,u+l,y+p,b+f).normalize(),i[3].setComponents(o-n,u-l,y-p,b-f).normalize(),i[4].setComponents(o-a,u-c,y-m,b-x).normalize(),e===Ri)i[5].setComponents(o+a,u+c,y+m,b+x).normalize();else{if(e!==Pi)throw new Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+e);i[5].setComponents(a,c,m,x).normalize()}return this}intersectsObject(t){if(void 0!==t.boundingSphere)null===t.boundingSphere&&t.computeBoundingSphere(),oo.copy(t.boundingSphere).applyMatrix4(t.matrixWorld);else{const e=t.geometry;null===e.boundingSphere&&e.computeBoundingSphere(),oo.copy(e.boundingSphere).applyMatrix4(t.matrixWorld)}return this.intersectsSphere(oo)}intersectsSprite(t){oo.center.set(0,0,0);const e=ho.distanceTo(t.center);return oo.radius=.*********1865476+e,oo.applyMatrix4(t.matrixWorld),this.intersectsSphere(oo)}intersectsSphere(t){const e=this.planes,i=t.center,s=-t.radius;for(let t=0;t<6;t++){if(e[t].distanceToPoint(i)<s)return!1}return!0}intersectsBox(t){const e=this.planes;for(let i=0;i<6;i++){const s=e[i];if(lo.x=s.normal.x>0?t.max.x:t.min.x,lo.y=s.normal.y>0?t.max.y:t.min.y,lo.z=s.normal.z>0?t.max.z:t.min.z,s.distanceToPoint(lo)<0)return!1}return!0}containsPoint(t){const e=this.planes;for(let i=0;i<6;i++)if(e[i].distanceToPoint(t)<0)return!1;return!0}clone(){return(new this.constructor).copy(this)}}const uo=new or,po=new co;class mo{constructor(){this.coordinateSystem=Ri}intersectsObject(t,e){if(!e.isArrayCamera||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){const s=e.cameras[i];if(uo.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),po.setFromProjectionMatrix(uo,this.coordinateSystem),po.intersectsObject(t))return!0}return!1}intersectsSprite(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){const s=e.cameras[i];if(uo.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),po.setFromProjectionMatrix(uo,this.coordinateSystem),po.intersectsSprite(t))return!0}return!1}intersectsSphere(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){const s=e.cameras[i];if(uo.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),po.setFromProjectionMatrix(uo,this.coordinateSystem),po.intersectsSphere(t))return!0}return!1}intersectsBox(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){const s=e.cameras[i];if(uo.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),po.setFromProjectionMatrix(uo,this.coordinateSystem),po.intersectsBox(t))return!0}return!1}containsPoint(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){const s=e.cameras[i];if(uo.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),po.setFromProjectionMatrix(uo,this.coordinateSystem),po.containsPoint(t))return!0}return!1}clone(){return new mo}}function yo(t,e){return t-e}function go(t,e){return t.z-e.z}function fo(t,e){return e.z-t.z}class xo{constructor(){this.index=0,this.pool=[],this.list=[]}push(t,e,i,s){const r=this.pool,n=this.list;this.index>=r.length&&r.push({start:-1,count:-1,z:-1,index:-1});const a=r[this.index];n.push(a),this.index++,a.start=t,a.count=e,a.z=i,a.index=s}reset(){this.list.length=0,this.index=0}}const bo=new or,vo=new Kr(1,1,1),wo=new co,Mo=new mo,So=new Ps,_o=new Qs,Ao=new Qi,To=new Qi,zo=new Qi,Io=new xo,Co=new Un,Bo=[];function ko(t,e,i=0){const s=e.itemSize;if(t.isInterleavedBufferAttribute||t.array.constructor!==e.array.constructor){const r=t.count;for(let n=0;n<r;n++)for(let r=0;r<s;r++)e.setComponent(n+i,r,t.getComponent(n,r))}else e.array.set(t.array,i*s);e.needsUpdate=!0}function Eo(t,e){if(t.constructor!==e.constructor){const i=Math.min(t.length,e.length);for(let s=0;s<i;s++)e[s]=t[s]}else{const i=Math.min(t.length,e.length);e.set(new t.constructor(t.buffer,0,i))}}class Ro extends Un{constructor(t,e,i=2*e,s){super(new Bn,s),this.isBatchedMesh=!0,this.perObjectFrustumCulled=!0,this.sortObjects=!0,this.boundingBox=null,this.boundingSphere=null,this.customSort=null,this._instanceInfo=[],this._geometryInfo=[],this._availableInstanceIds=[],this._availableGeometryIds=[],this._nextIndexStart=0,this._nextVertexStart=0,this._geometryCount=0,this._visibilityChanged=!0,this._geometryInitialized=!1,this._maxInstanceCount=t,this._maxVertexCount=e,this._maxIndexCount=i,this._multiDrawCounts=new Int32Array(t),this._multiDrawStarts=new Int32Array(t),this._multiDrawCount=0,this._multiDrawInstances=null,this._matricesTexture=null,this._indirectTexture=null,this._colorsTexture=null,this._initMatricesTexture(),this._initIndirectTexture()}get maxInstanceCount(){return this._maxInstanceCount}get instanceCount(){return this._instanceInfo.length-this._availableInstanceIds.length}get unusedVertexCount(){return this._maxVertexCount-this._nextVertexStart}get unusedIndexCount(){return this._maxIndexCount-this._nextIndexStart}_initMatricesTexture(){let t=Math.sqrt(4*this._maxInstanceCount);t=4*Math.ceil(t/4),t=Math.max(t,4);const e=new Float32Array(t*t*4),i=new Ha(e,t,t,jt,Et);this._matricesTexture=i}_initIndirectTexture(){let t=Math.sqrt(this._maxInstanceCount);t=Math.ceil(t);const e=new Uint32Array(t*t),i=new Ha(e,t,t,Ht,kt);this._indirectTexture=i}_initColorsTexture(){let t=Math.sqrt(this._maxInstanceCount);t=Math.ceil(t);const e=new Float32Array(t*t*4).fill(1),i=new Ha(e,t,t,jt,Et);i.colorSpace=gs.workingColorSpace,this._colorsTexture=i}_initializeGeometry(t){const e=this.geometry,i=this._maxVertexCount,s=this._maxIndexCount;if(!1===this._geometryInitialized){for(const s in t.attributes){const r=t.getAttribute(s),{array:n,itemSize:a,normalized:o}=r,h=new n.constructor(i*a),l=new pn(h,a,o);e.setAttribute(s,l)}if(null!==t.getIndex()){const t=i>65535?new Uint32Array(s):new Uint16Array(s);e.setIndex(new pn(t,1))}this._geometryInitialized=!0}}_validateGeometry(t){const e=this.geometry;if(Boolean(t.getIndex())!==Boolean(e.getIndex()))throw new Error('THREE.BatchedMesh: All geometries must consistently have "index".');for(const i in e.attributes){if(!t.hasAttribute(i))throw new Error(`THREE.BatchedMesh: Added geometry missing "${i}". All geometries must have consistent attributes.`);const s=t.getAttribute(i),r=e.getAttribute(i);if(s.itemSize!==r.itemSize||s.normalized!==r.normalized)throw new Error("THREE.BatchedMesh: All attributes must have a consistent itemSize and normalized value.")}}validateInstanceId(t){const e=this._instanceInfo;if(t<0||t>=e.length||!1===e[t].active)throw new Error(`THREE.BatchedMesh: Invalid instanceId ${t}. Instance is either out of range or has been deleted.`)}validateGeometryId(t){const e=this._geometryInfo;if(t<0||t>=e.length||!1===e[t].active)throw new Error(`THREE.BatchedMesh: Invalid geometryId ${t}. Geometry is either out of range or has been deleted.`)}setCustomSort(t){return this.customSort=t,this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new Ps);const t=this.boundingBox,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;const s=e[i].geometryIndex;this.getMatrixAt(i,bo),this.getBoundingBoxAt(s,So).applyMatrix4(bo),t.union(So)}}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new Qs);const t=this.boundingSphere,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;const s=e[i].geometryIndex;this.getMatrixAt(i,bo),this.getBoundingSphereAt(s,_o).applyMatrix4(bo),t.union(_o)}}addInstance(t){if(this._instanceInfo.length>=this.maxInstanceCount&&0===this._availableInstanceIds.length)throw new Error("THREE.BatchedMesh: Maximum item count reached.");const e={visible:!0,active:!0,geometryIndex:t};let i=null;this._availableInstanceIds.length>0?(this._availableInstanceIds.sort(yo),i=this._availableInstanceIds.shift(),this._instanceInfo[i]=e):(i=this._instanceInfo.length,this._instanceInfo.push(e));const s=this._matricesTexture;bo.identity().toArray(s.image.data,16*i),s.needsUpdate=!0;const r=this._colorsTexture;return r&&(vo.toArray(r.image.data,4*i),r.needsUpdate=!0),this._visibilityChanged=!0,i}addGeometry(t,e=-1,i=-1){this._initializeGeometry(t),this._validateGeometry(t);const s={vertexStart:-1,vertexCount:-1,reservedVertexCount:-1,indexStart:-1,indexCount:-1,reservedIndexCount:-1,start:-1,count:-1,boundingBox:null,boundingSphere:null,active:!0},r=this._geometryInfo;s.vertexStart=this._nextVertexStart,s.reservedVertexCount=-1===e?t.getAttribute("position").count:e;const n=t.getIndex();if(null!==n&&(s.indexStart=this._nextIndexStart,s.reservedIndexCount=-1===i?n.count:i),-1!==s.indexStart&&s.indexStart+s.reservedIndexCount>this._maxIndexCount||s.vertexStart+s.reservedVertexCount>this._maxVertexCount)throw new Error("THREE.BatchedMesh: Reserved space request exceeds the maximum buffer size.");let a;return this._availableGeometryIds.length>0?(this._availableGeometryIds.sort(yo),a=this._availableGeometryIds.shift(),r[a]=s):(a=this._geometryCount,this._geometryCount++,r.push(s)),this.setGeometryAt(a,t),this._nextIndexStart=s.indexStart+s.reservedIndexCount,this._nextVertexStart=s.vertexStart+s.reservedVertexCount,a}setGeometryAt(t,e){if(t>=this._geometryCount)throw new Error("THREE.BatchedMesh: Maximum geometry count reached.");this._validateGeometry(e);const i=this.geometry,s=null!==i.getIndex(),r=i.getIndex(),n=e.getIndex(),a=this._geometryInfo[t];if(s&&n.count>a.reservedIndexCount||e.attributes.position.count>a.reservedVertexCount)throw new Error("THREE.BatchedMesh: Reserved space not large enough for provided geometry.");const o=a.vertexStart,h=a.reservedVertexCount;a.vertexCount=e.getAttribute("position").count;for(const t in i.attributes){const s=e.getAttribute(t),r=i.getAttribute(t);ko(s,r,o);const n=s.itemSize;for(let t=s.count,e=h;t<e;t++){const e=o+t;for(let t=0;t<n;t++)r.setComponent(e,t,0)}r.needsUpdate=!0,r.addUpdateRange(o*n,h*n)}if(s){const t=a.indexStart,i=a.reservedIndexCount;a.indexCount=e.getIndex().count;for(let e=0;e<n.count;e++)r.setX(t+e,o+n.getX(e));for(let e=n.count,s=i;e<s;e++)r.setX(t+e,o);r.needsUpdate=!0,r.addUpdateRange(t,a.reservedIndexCount)}return a.start=s?a.indexStart:a.vertexStart,a.count=s?a.indexCount:a.vertexCount,a.boundingBox=null,null!==e.boundingBox&&(a.boundingBox=e.boundingBox.clone()),a.boundingSphere=null,null!==e.boundingSphere&&(a.boundingSphere=e.boundingSphere.clone()),this._visibilityChanged=!0,t}deleteGeometry(t){const e=this._geometryInfo;if(t>=e.length||!1===e[t].active)return this;const i=this._instanceInfo;for(let e=0,s=i.length;e<s;e++)i[e].active&&i[e].geometryIndex===t&&this.deleteInstance(e);return e[t].active=!1,this._availableGeometryIds.push(t),this._visibilityChanged=!0,this}deleteInstance(t){return this.validateInstanceId(t),this._instanceInfo[t].active=!1,this._availableInstanceIds.push(t),this._visibilityChanged=!0,this}optimize(){let t=0,e=0;const i=this._geometryInfo,s=i.map((t,e)=>e).sort((t,e)=>i[t].vertexStart-i[e].vertexStart),r=this.geometry;for(let n=0,a=i.length;n<a;n++){const a=s[n],o=i[a];if(!1!==o.active){if(null!==r.index){if(o.indexStart!==e){const{indexStart:i,vertexStart:s,reservedIndexCount:n}=o,a=r.index,h=a.array,l=t-s;for(let t=i;t<i+n;t++)h[t]=h[t]+l;a.array.copyWithin(e,i,i+n),a.addUpdateRange(e,n),o.indexStart=e}e+=o.reservedIndexCount}if(o.vertexStart!==t){const{vertexStart:e,reservedVertexCount:i}=o,s=r.attributes;for(const r in s){const n=s[r],{array:a,itemSize:o}=n;a.copyWithin(t*o,e*o,(e+i)*o),n.addUpdateRange(t*o,i*o)}o.vertexStart=t}t+=o.reservedVertexCount,o.start=r.index?o.indexStart:o.vertexStart,this._nextIndexStart=r.index?o.indexStart+o.reservedIndexCount:0,this._nextVertexStart=o.vertexStart+o.reservedVertexCount}}return this}getBoundingBoxAt(t,e){if(t>=this._geometryCount)return null;const i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingBox){const t=new Ps,e=i.index,r=i.attributes.position;for(let i=s.start,n=s.start+s.count;i<n;i++){let s=i;e&&(s=e.getX(s)),t.expandByPoint(Ao.fromBufferAttribute(r,s))}s.boundingBox=t}return e.copy(s.boundingBox),e}getBoundingSphereAt(t,e){if(t>=this._geometryCount)return null;const i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingSphere){const e=new Qs;this.getBoundingBoxAt(t,So),So.getCenter(e.center);const r=i.index,n=i.attributes.position;let a=0;for(let t=s.start,i=s.start+s.count;t<i;t++){let i=t;r&&(i=r.getX(i)),Ao.fromBufferAttribute(n,i),a=Math.max(a,e.center.distanceToSquared(Ao))}e.radius=Math.sqrt(a),s.boundingSphere=e}return e.copy(s.boundingSphere),e}setMatrixAt(t,e){this.validateInstanceId(t);const i=this._matricesTexture,s=this._matricesTexture.image.data;return e.toArray(s,16*t),i.needsUpdate=!0,this}getMatrixAt(t,e){return this.validateInstanceId(t),e.fromArray(this._matricesTexture.image.data,16*t)}setColorAt(t,e){return this.validateInstanceId(t),null===this._colorsTexture&&this._initColorsTexture(),e.toArray(this._colorsTexture.image.data,4*t),this._colorsTexture.needsUpdate=!0,this}getColorAt(t,e){return this.validateInstanceId(t),e.fromArray(this._colorsTexture.image.data,4*t)}setVisibleAt(t,e){return this.validateInstanceId(t),this._instanceInfo[t].visible===e||(this._instanceInfo[t].visible=e,this._visibilityChanged=!0),this}getVisibleAt(t){return this.validateInstanceId(t),this._instanceInfo[t].visible}setGeometryIdAt(t,e){return this.validateInstanceId(t),this.validateGeometryId(e),this._instanceInfo[t].geometryIndex=e,this}getGeometryIdAt(t){return this.validateInstanceId(t),this._instanceInfo[t].geometryIndex}getGeometryRangeAt(t,e={}){this.validateGeometryId(t);const i=this._geometryInfo[t];return e.vertexStart=i.vertexStart,e.vertexCount=i.vertexCount,e.reservedVertexCount=i.reservedVertexCount,e.indexStart=i.indexStart,e.indexCount=i.indexCount,e.reservedIndexCount=i.reservedIndexCount,e.start=i.start,e.count=i.count,e}setInstanceCount(t){const e=this._availableInstanceIds,i=this._instanceInfo;for(e.sort(yo);e[e.length-1]===i.length;)i.pop(),e.pop();if(t<i.length)throw new Error(`BatchedMesh: Instance ids outside the range ${t} are being used. Cannot shrink instance count.`);const s=new Int32Array(t),r=new Int32Array(t);Eo(this._multiDrawCounts,s),Eo(this._multiDrawStarts,r),this._multiDrawCounts=s,this._multiDrawStarts=r,this._maxInstanceCount=t;const n=this._indirectTexture,a=this._matricesTexture,o=this._colorsTexture;n.dispose(),this._initIndirectTexture(),Eo(n.image.data,this._indirectTexture.image.data),a.dispose(),this._initMatricesTexture(),Eo(a.image.data,this._matricesTexture.image.data),o&&(o.dispose(),this._initColorsTexture(),Eo(o.image.data,this._colorsTexture.image.data))}setGeometrySize(t,e){const i=[...this._geometryInfo].filter(t=>t.active);if(Math.max(...i.map(t=>t.vertexStart+t.reservedVertexCount))>t)throw new Error(`BatchedMesh: Geometry vertex values are being used outside the range ${e}. Cannot shrink further.`);if(this.geometry.index){if(Math.max(...i.map(t=>t.indexStart+t.reservedIndexCount))>e)throw new Error(`BatchedMesh: Geometry index values are being used outside the range ${e}. Cannot shrink further.`)}const s=this.geometry;s.dispose(),this._maxVertexCount=t,this._maxIndexCount=e,this._geometryInitialized&&(this._geometryInitialized=!1,this.geometry=new Bn,this._initializeGeometry(s));const r=this.geometry;s.index&&Eo(s.index.array,r.index.array);for(const t in s.attributes)Eo(s.attributes[t].array,r.attributes[t].array)}raycast(t,e){const i=this._instanceInfo,s=this._geometryInfo,r=this.matrixWorld,n=this.geometry;Co.material=this.material,Co.geometry.index=n.index,Co.geometry.attributes=n.attributes,null===Co.geometry.boundingBox&&(Co.geometry.boundingBox=new Ps),null===Co.geometry.boundingSphere&&(Co.geometry.boundingSphere=new Qs);for(let n=0,a=i.length;n<a;n++){if(!i[n].visible||!i[n].active)continue;const a=i[n].geometryIndex,o=s[a];Co.geometry.setDrawRange(o.start,o.count),this.getMatrixAt(n,Co.matrixWorld).premultiply(r),this.getBoundingBoxAt(a,Co.geometry.boundingBox),this.getBoundingSphereAt(a,Co.geometry.boundingSphere),Co.raycast(t,Bo);for(let t=0,i=Bo.length;t<i;t++){const i=Bo[t];i.object=this,i.batchId=n,e.push(i)}Bo.length=0}Co.material=null,Co.geometry.index=null,Co.geometry.attributes={},Co.geometry.setDrawRange(0,1/0)}copy(t){return super.copy(t),this.geometry=t.geometry.clone(),this.perObjectFrustumCulled=t.perObjectFrustumCulled,this.sortObjects=t.sortObjects,this.boundingBox=null!==t.boundingBox?t.boundingBox.clone():null,this.boundingSphere=null!==t.boundingSphere?t.boundingSphere.clone():null,this._geometryInfo=t._geometryInfo.map(t=>({...t,boundingBox:null!==t.boundingBox?t.boundingBox.clone():null,boundingSphere:null!==t.boundingSphere?t.boundingSphere.clone():null})),this._instanceInfo=t._instanceInfo.map(t=>({...t})),this._availableInstanceIds=t._availableInstanceIds.slice(),this._availableGeometryIds=t._availableGeometryIds.slice(),this._nextIndexStart=t._nextIndexStart,this._nextVertexStart=t._nextVertexStart,this._geometryCount=t._geometryCount,this._maxInstanceCount=t._maxInstanceCount,this._maxVertexCount=t._maxVertexCount,this._maxIndexCount=t._maxIndexCount,this._geometryInitialized=t._geometryInitialized,this._multiDrawCounts=t._multiDrawCounts.slice(),this._multiDrawStarts=t._multiDrawStarts.slice(),this._indirectTexture=t._indirectTexture.clone(),this._indirectTexture.image.data=this._indirectTexture.image.data.slice(),this._matricesTexture=t._matricesTexture.clone(),this._matricesTexture.image.data=this._matricesTexture.image.data.slice(),null!==this._colorsTexture&&(this._colorsTexture=t._colorsTexture.clone(),this._colorsTexture.image.data=this._colorsTexture.image.data.slice()),this}dispose(){this.geometry.dispose(),this._matricesTexture.dispose(),this._matricesTexture=null,this._indirectTexture.dispose(),this._indirectTexture=null,null!==this._colorsTexture&&(this._colorsTexture.dispose(),this._colorsTexture=null)}onBeforeRender(t,e,i,s,r){if(!this._visibilityChanged&&!this.perObjectFrustumCulled&&!this.sortObjects)return;const n=s.getIndex(),a=null===n?1:n.array.BYTES_PER_ELEMENT,o=this._instanceInfo,h=this._multiDrawStarts,l=this._multiDrawCounts,c=this._geometryInfo,u=this.perObjectFrustumCulled,d=this._indirectTexture,p=d.image.data,m=i.isArrayCamera?Mo:wo;u&&!i.isArrayCamera&&(bo.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse).multiply(this.matrixWorld),wo.setFromProjectionMatrix(bo,t.coordinateSystem));let y=0;if(this.sortObjects){bo.copy(this.matrixWorld).invert(),Ao.setFromMatrixPosition(i.matrixWorld).applyMatrix4(bo),To.set(0,0,-1).transformDirection(i.matrixWorld).transformDirection(bo);for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){const e=o[t].geometryIndex;this.getMatrixAt(t,bo),this.getBoundingSphereAt(e,_o).applyMatrix4(bo);let s=!1;if(u&&(s=!m.intersectsSphere(_o,i)),!s){const i=c[e],s=zo.subVectors(_o.center,Ao).dot(To);Io.push(i.start,i.count,s,t)}}const t=Io.list,e=this.customSort;null===e?t.sort(r.transparent?fo:go):e.call(this,t,i);for(let e=0,i=t.length;e<i;e++){const i=t[e];h[y]=i.start*a,l[y]=i.count,p[y]=i.index,y++}Io.reset()}else for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){const e=o[t].geometryIndex;let s=!1;if(u&&(this.getMatrixAt(t,bo),this.getBoundingSphereAt(e,_o).applyMatrix4(bo),s=!m.intersectsSphere(_o,i)),!s){const i=c[e];h[y]=i.start*a,l[y]=i.count,p[y]=t,y++}}d.needsUpdate=!0,this._multiDrawCount=y,this._visibilityChanged=!1}onBeforeShadow(t,e,i,s,r,n){this.onBeforeRender(t,null,s,r,n)}}class Po extends sn{constructor(t){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new Kr(16777215),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this.fog=t.fog,this}}const Oo=new Qi,No=new Qi,Fo=new or,Vo=new ar,Lo=new Qs,jo=new Qi,Wo=new Qi;class Uo extends Pr{constructor(t=new Bn,e=new Po){super(),this.isLine=!0,this.type="Line",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}computeLineDistances(){const t=this.geometry;if(null===t.index){const e=t.attributes.position,i=[0];for(let t=1,s=e.count;t<s;t++)Oo.fromBufferAttribute(e,t-1),No.fromBufferAttribute(e,t),i[t]=i[t-1],i[t]+=Oo.distanceTo(No);t.setAttribute("lineDistance",new Mn(i,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(t,e){const i=this.geometry,s=this.matrixWorld,r=t.params.Line.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),Lo.copy(i.boundingSphere),Lo.applyMatrix4(s),Lo.radius+=r,!1===t.ray.intersectsSphere(Lo))return;Fo.copy(s).invert(),Vo.copy(t.ray).applyMatrix4(Fo);const a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=this.isLineSegments?2:1,l=i.index,c=i.attributes.position;if(null!==l){const i=Math.max(0,n.start),s=Math.min(l.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){const i=l.getX(r),s=l.getX(r+1),n=Do(this,t,Vo,o,i,s,r);n&&e.push(n)}if(this.isLineLoop){const r=l.getX(s-1),n=l.getX(i),a=Do(this,t,Vo,o,r,n,s-1);a&&e.push(a)}}else{const i=Math.max(0,n.start),s=Math.min(c.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){const i=Do(this,t,Vo,o,r,r+1,r);i&&e.push(i)}if(this.isLineLoop){const r=Do(this,t,Vo,o,s-1,i,s-1);r&&e.push(r)}}}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){const e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function Do(t,e,i,s,r,n,a){const o=t.geometry.attributes.position;Oo.fromBufferAttribute(o,r),No.fromBufferAttribute(o,n);if(i.distanceSqToSegment(Oo,No,jo,Wo)>s)return;jo.applyMatrix4(t.matrixWorld);const h=e.ray.origin.distanceTo(jo);return h<e.near||h>e.far?void 0:{distance:h,point:Wo.clone().applyMatrix4(t.matrixWorld),index:a,face:null,faceIndex:null,barycoord:null,object:t}}const Ho=new Qi,qo=new Qi;class Jo extends Uo{constructor(t,e){super(t,e),this.isLineSegments=!0,this.type="LineSegments"}computeLineDistances(){const t=this.geometry;if(null===t.index){const e=t.attributes.position,i=[];for(let t=0,s=e.count;t<s;t+=2)Ho.fromBufferAttribute(e,t),qo.fromBufferAttribute(e,t+1),i[t]=0===t?0:i[t-1],i[t+1]=i[t]+Ho.distanceTo(qo);t.setAttribute("lineDistance",new Mn(i,1))}else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}}class Xo extends Uo{constructor(t,e){super(t,e),this.isLineLoop=!0,this.type="LineLoop"}}class Yo extends sn{constructor(t){super(),this.isPointsMaterial=!0,this.type="PointsMaterial",this.color=new Kr(16777215),this.map=null,this.alphaMap=null,this.size=1,this.sizeAttenuation=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}const Zo=new or,Go=new ar,$o=new Qs,Qo=new Qi;class Ko extends Pr{constructor(t=new Bn,e=new Yo){super(),this.isPoints=!0,this.type="Points",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}raycast(t,e){const i=this.geometry,s=this.matrixWorld,r=t.params.Points.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),$o.copy(i.boundingSphere),$o.applyMatrix4(s),$o.radius+=r,!1===t.ray.intersectsSphere($o))return;Zo.copy(s).invert(),Go.copy(t.ray).applyMatrix4(Zo);const a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=i.index,l=i.attributes.position;if(null!==h){for(let i=Math.max(0,n.start),r=Math.min(h.count,n.start+n.count);i<r;i++){const r=h.getX(i);Qo.fromBufferAttribute(l,r),th(Qo,r,o,s,t,e,this)}}else{for(let i=Math.max(0,n.start),r=Math.min(l.count,n.start+n.count);i<r;i++)Qo.fromBufferAttribute(l,i),th(Qo,i,o,s,t,e,this)}}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){const e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function th(t,e,i,s,r,n,a){const o=Go.distanceSqToPoint(t);if(o<i){const i=new Qi;Go.closestPointToPoint(t,i),i.applyMatrix4(s);const h=r.ray.origin.distanceTo(i);if(h<r.near||h>r.far)return;n.push({distance:h,distanceToRay:Math.sqrt(o),point:i,index:e,face:null,faceIndex:null,barycoord:null,object:a})}}class eh extends Ts{constructor(t,e,i,s,r=1006,n=1006,a,o,h){super(t,e,i,s,r,n,a,o,h),this.isVideoTexture=!0,this.generateMipmaps=!1;const l=this;"requestVideoFrameCallback"in t&&t.requestVideoFrameCallback(function e(){l.needsUpdate=!0,t.requestVideoFrameCallback(e)})}clone(){return new this.constructor(this.image).copy(this)}update(){const t=this.image;!1==="requestVideoFrameCallback"in t&&t.readyState>=t.HAVE_CURRENT_DATA&&(this.needsUpdate=!0)}}class ih extends eh{constructor(t,e,i,s,r,n,a,o){super({},t,e,i,s,r,n,a,o),this.isVideoFrameTexture=!0}update(){}clone(){return(new this.constructor).copy(this)}setFrame(t){this.image=t,this.needsUpdate=!0}}class sh extends Ts{constructor(t,e){super({width:t,height:e}),this.isFramebufferTexture=!0,this.magFilter=gt,this.minFilter=gt,this.generateMipmaps=!1,this.needsUpdate=!0}}class rh extends Ts{constructor(t,e,i,s,r,n,a,o,h,l,c,u){super(null,n,a,o,h,l,s,r,c,u),this.isCompressedTexture=!0,this.image={width:e,height:i},this.mipmaps=t,this.flipY=!1,this.generateMipmaps=!1}}class nh extends rh{constructor(t,e,i,s,r,n){super(t,e,i,r,n),this.isCompressedArrayTexture=!0,this.image.depth=s,this.wrapR=mt,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class ah extends rh{constructor(t,e,i){super(void 0,t[0].width,t[0].height,e,i,ht),this.isCompressedCubeTexture=!0,this.isCubeTexture=!0,this.image=t}}class oh extends Ts{constructor(t,e,i,s,r,n,a,o,h){super(t,e,i,s,r,n,a,o,h),this.isCanvasTexture=!0,this.needsUpdate=!0}}class hh extends Ts{constructor(t,e,i=1014,s,r,n,a=1003,o=1003,h,l=1026,c=1){if(l!==Wt&&1027!==l)throw new Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");super({width:t,height:e,depth:c},s,r,n,a,o,l,i,h),this.isDepthTexture=!0,this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}copy(t){return super.copy(t),this.source=new Ms(Object.assign({},t.image)),this.compareFunction=t.compareFunction,this}toJSON(t){const e=super.toJSON(t);return null!==this.compareFunction&&(e.compareFunction=this.compareFunction),e}}class lh extends Bn{constructor(t=1,e=1,i=4,s=8,r=1){super(),this.type="CapsuleGeometry",this.parameters={radius:t,height:e,capSegments:i,radialSegments:s,heightSegments:r},e=Math.max(0,e),i=Math.max(1,Math.floor(i)),s=Math.max(3,Math.floor(s)),r=Math.max(1,Math.floor(r));const n=[],a=[],o=[],h=[],l=e/2,c=Math.PI/2*t,u=e,d=2*c+u,p=2*i+r,m=s+1,y=new Qi,g=new Qi;for(let f=0;f<=p;f++){let x=0,b=0,v=0,w=0;if(f<=i){const e=f/i,s=e*Math.PI/2;b=-l-t*Math.cos(s),v=t*Math.sin(s),w=-t*Math.cos(s),x=e*c}else if(f<=i+r){const s=(f-i)/r;b=s*e-l,v=t,w=0,x=c+s*u}else{const e=(f-i-r)/i,s=e*Math.PI/2;b=l+t*Math.sin(s),v=t*Math.cos(s),w=t*Math.sin(s),x=c+u+e*c}const M=Math.max(0,Math.min(1,x/d));let S=0;0===f?S=.5/s:f===p&&(S=-.5/s);for(let t=0;t<=s;t++){const e=t/s,i=e*Math.PI*2,r=Math.sin(i),n=Math.cos(i);g.x=-v*n,g.y=b,g.z=v*r,a.push(g.x,g.y,g.z),y.set(-v*n,w,v*r),y.normalize(),o.push(y.x,y.y,y.z),h.push(e+S,M)}if(f>0){const t=(f-1)*m;for(let e=0;e<s;e++){const i=t+e,s=t+e+1,r=f*m+e,a=f*m+e+1;n.push(i,s,r),n.push(s,a,r)}}}this.setIndex(n),this.setAttribute("position",new Mn(a,3)),this.setAttribute("normal",new Mn(o,3)),this.setAttribute("uv",new Mn(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new lh(t.radius,t.height,t.capSegments,t.radialSegments,t.heightSegments)}}class ch extends Bn{constructor(t=1,e=32,i=0,s=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:i,thetaLength:s},e=Math.max(3,e);const r=[],n=[],a=[],o=[],h=new Qi,l=new Gi;n.push(0,0,0),a.push(0,0,1),o.push(.5,.5);for(let r=0,c=3;r<=e;r++,c+=3){const u=i+r/e*s;h.x=t*Math.cos(u),h.y=t*Math.sin(u),n.push(h.x,h.y,h.z),a.push(0,0,1),l.x=(n[c]/t+1)/2,l.y=(n[c+1]/t+1)/2,o.push(l.x,l.y)}for(let t=1;t<=e;t++)r.push(t,t+1,0);this.setIndex(r),this.setAttribute("position",new Mn(n,3)),this.setAttribute("normal",new Mn(a,3)),this.setAttribute("uv",new Mn(o,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ch(t.radius,t.segments,t.thetaStart,t.thetaLength)}}class uh extends Bn{constructor(t=1,e=1,i=1,s=32,r=1,n=!1,a=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:i,radialSegments:s,heightSegments:r,openEnded:n,thetaStart:a,thetaLength:o};const h=this;s=Math.floor(s),r=Math.floor(r);const l=[],c=[],u=[],d=[];let p=0;const m=[],y=i/2;let g=0;function f(i){const r=p,n=new Gi,m=new Qi;let f=0;const x=!0===i?t:e,b=!0===i?1:-1;for(let t=1;t<=s;t++)c.push(0,y*b,0),u.push(0,b,0),d.push(.5,.5),p++;const v=p;for(let t=0;t<=s;t++){const e=t/s*o+a,i=Math.cos(e),r=Math.sin(e);m.x=x*r,m.y=y*b,m.z=x*i,c.push(m.x,m.y,m.z),u.push(0,b,0),n.x=.5*i+.5,n.y=.5*r*b+.5,d.push(n.x,n.y),p++}for(let t=0;t<s;t++){const e=r+t,s=v+t;!0===i?l.push(s,s+1,e):l.push(s+1,s,e),f+=3}h.addGroup(g,f,!0===i?1:2),g+=f}!function(){const n=new Qi,f=new Qi;let x=0;const b=(e-t)/i;for(let h=0;h<=r;h++){const l=[],g=h/r,x=g*(e-t)+t;for(let t=0;t<=s;t++){const e=t/s,r=e*o+a,h=Math.sin(r),m=Math.cos(r);f.x=x*h,f.y=-g*i+y,f.z=x*m,c.push(f.x,f.y,f.z),n.set(h,b,m).normalize(),u.push(n.x,n.y,n.z),d.push(e,1-g),l.push(p++)}m.push(l)}for(let i=0;i<s;i++)for(let s=0;s<r;s++){const n=m[s][i],a=m[s+1][i],o=m[s+1][i+1],h=m[s][i+1];(t>0||0!==s)&&(l.push(n,a,h),x+=3),(e>0||s!==r-1)&&(l.push(a,o,h),x+=3)}h.addGroup(g,x,0),g+=x}(),!1===n&&(t>0&&f(!0),e>0&&f(!1)),this.setIndex(l),this.setAttribute("position",new Mn(c,3)),this.setAttribute("normal",new Mn(u,3)),this.setAttribute("uv",new Mn(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new uh(t.radiusTop,t.radiusBottom,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class dh extends uh{constructor(t=1,e=1,i=32,s=1,r=!1,n=0,a=2*Math.PI){super(0,t,e,i,s,r,n,a),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:s,openEnded:r,thetaStart:n,thetaLength:a}}static fromJSON(t){return new dh(t.radius,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class ph extends Bn{constructor(t=[],e=[],i=1,s=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:i,detail:s};const r=[],n=[];function a(t,e,i,s){const r=s+1,n=[];for(let s=0;s<=r;s++){n[s]=[];const a=t.clone().lerp(i,s/r),o=e.clone().lerp(i,s/r),h=r-s;for(let t=0;t<=h;t++)n[s][t]=0===t&&s===r?a:a.clone().lerp(o,t/h)}for(let t=0;t<r;t++)for(let e=0;e<2*(r-t)-1;e++){const i=Math.floor(e/2);e%2==0?(o(n[t][i+1]),o(n[t+1][i]),o(n[t][i])):(o(n[t][i+1]),o(n[t+1][i+1]),o(n[t+1][i]))}}function o(t){r.push(t.x,t.y,t.z)}function h(e,i){const s=3*e;i.x=t[s+0],i.y=t[s+1],i.z=t[s+2]}function l(t,e,i,s){s<0&&1===t.x&&(n[e]=t.x-1),0===i.x&&0===i.z&&(n[e]=s/2/Math.PI+.5)}function c(t){return Math.atan2(t.z,-t.x)}function u(t){return Math.atan2(-t.y,Math.sqrt(t.x*t.x+t.z*t.z))}!function(t){const i=new Qi,s=new Qi,r=new Qi;for(let n=0;n<e.length;n+=3)h(e[n+0],i),h(e[n+1],s),h(e[n+2],r),a(i,s,r,t)}(s),function(t){const e=new Qi;for(let i=0;i<r.length;i+=3)e.x=r[i+0],e.y=r[i+1],e.z=r[i+2],e.normalize().multiplyScalar(t),r[i+0]=e.x,r[i+1]=e.y,r[i+2]=e.z}(i),function(){const t=new Qi;for(let e=0;e<r.length;e+=3){t.x=r[e+0],t.y=r[e+1],t.z=r[e+2];const i=c(t)/2/Math.PI+.5,s=u(t)/Math.PI+.5;n.push(i,1-s)}(function(){const t=new Qi,e=new Qi,i=new Qi,s=new Qi,a=new Gi,o=new Gi,h=new Gi;for(let u=0,d=0;u<r.length;u+=9,d+=6){t.set(r[u+0],r[u+1],r[u+2]),e.set(r[u+3],r[u+4],r[u+5]),i.set(r[u+6],r[u+7],r[u+8]),a.set(n[d+0],n[d+1]),o.set(n[d+2],n[d+3]),h.set(n[d+4],n[d+5]),s.copy(t).add(e).add(i).divideScalar(3);const p=c(s);l(a,d+0,t,p),l(o,d+2,e,p),l(h,d+4,i,p)}})(),function(){for(let t=0;t<n.length;t+=6){const e=n[t+0],i=n[t+2],s=n[t+4],r=Math.max(e,i,s),a=Math.min(e,i,s);r>.9&&a<.1&&(e<.2&&(n[t+0]+=1),i<.2&&(n[t+2]+=1),s<.2&&(n[t+4]+=1))}}()}(),this.setAttribute("position",new Mn(r,3)),this.setAttribute("normal",new Mn(r.slice(),3)),this.setAttribute("uv",new Mn(n,2)),0===s?this.computeVertexNormals():this.normalizeNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ph(t.vertices,t.indices,t.radius,t.details)}}class mh extends ph{constructor(t=1,e=0){const i=(1+Math.sqrt(5))/2,s=1/i;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-s,-i,0,-s,i,0,s,-i,0,s,i,-s,-i,0,-s,i,0,s,-i,0,s,i,0,-i,0,-s,i,0,-s,-i,0,s,i,0,s],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new mh(t.radius,t.detail)}}const yh=new Qi,gh=new Qi,fh=new Qi,xh=new Yr;class bh extends Bn{constructor(t=null,e=1){if(super(),this.type="EdgesGeometry",this.parameters={geometry:t,thresholdAngle:e},null!==t){const i=4,s=Math.pow(10,i),r=Math.cos(Wi*e),n=t.getIndex(),a=t.getAttribute("position"),o=n?n.count:a.count,h=[0,0,0],l=["a","b","c"],c=new Array(3),u={},d=[];for(let t=0;t<o;t+=3){n?(h[0]=n.getX(t),h[1]=n.getX(t+1),h[2]=n.getX(t+2)):(h[0]=t,h[1]=t+1,h[2]=t+2);const{a:e,b:i,c:o}=xh;if(e.fromBufferAttribute(a,h[0]),i.fromBufferAttribute(a,h[1]),o.fromBufferAttribute(a,h[2]),xh.getNormal(fh),c[0]=`${Math.round(e.x*s)},${Math.round(e.y*s)},${Math.round(e.z*s)}`,c[1]=`${Math.round(i.x*s)},${Math.round(i.y*s)},${Math.round(i.z*s)}`,c[2]=`${Math.round(o.x*s)},${Math.round(o.y*s)},${Math.round(o.z*s)}`,c[0]!==c[1]&&c[1]!==c[2]&&c[2]!==c[0])for(let t=0;t<3;t++){const e=(t+1)%3,i=c[t],s=c[e],n=xh[l[t]],a=xh[l[e]],o=`${i}_${s}`,p=`${s}_${i}`;p in u&&u[p]?(fh.dot(u[p].normal)<=r&&(d.push(n.x,n.y,n.z),d.push(a.x,a.y,a.z)),u[p]=null):o in u||(u[o]={index0:h[t],index1:h[e],normal:fh.clone()})}}for(const t in u)if(u[t]){const{index0:e,index1:i}=u[t];yh.fromBufferAttribute(a,e),gh.fromBufferAttribute(a,i),d.push(yh.x,yh.y,yh.z),d.push(gh.x,gh.y,gh.z)}this.setAttribute("position",new Mn(d,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}class vh{constructor(){this.type="Curve",this.arcLengthDivisions=200,this.needsUpdate=!1,this.cacheArcLengths=null}getPoint(){console.warn("THREE.Curve: .getPoint() not implemented.")}getPointAt(t,e){const i=this.getUtoTmapping(t);return this.getPoint(i,e)}getPoints(t=5){const e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return e}getSpacedPoints(t=5){const e=[];for(let i=0;i<=t;i++)e.push(this.getPointAt(i/t));return e}getLength(){const t=this.getLengths();return t[t.length-1]}getLengths(t=this.arcLengthDivisions){if(this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;const e=[];let i,s=this.getPoint(0),r=0;e.push(0);for(let n=1;n<=t;n++)i=this.getPoint(n/t),r+=i.distanceTo(s),e.push(r),s=i;return this.cacheArcLengths=e,e}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(t,e=null){const i=this.getLengths();let s=0;const r=i.length;let n;n=e||t*i[r-1];let a,o=0,h=r-1;for(;o<=h;)if(s=Math.floor(o+(h-o)/2),a=i[s]-n,a<0)o=s+1;else{if(!(a>0)){h=s;break}h=s-1}if(s=h,i[s]===n)return s/(r-1);const l=i[s];return(s+(n-l)/(i[s+1]-l))/(r-1)}getTangent(t,e){const i=1e-4;let s=t-i,r=t+i;s<0&&(s=0),r>1&&(r=1);const n=this.getPoint(s),a=this.getPoint(r),o=e||(n.isVector2?new Gi:new Qi);return o.copy(a).sub(n).normalize(),o}getTangentAt(t,e){const i=this.getUtoTmapping(t);return this.getTangent(i,e)}computeFrenetFrames(t,e=!1){const i=new Qi,s=[],r=[],n=[],a=new Qi,o=new or;for(let e=0;e<=t;e++){const i=e/t;s[e]=this.getTangentAt(i,new Qi)}r[0]=new Qi,n[0]=new Qi;let h=Number.MAX_VALUE;const l=Math.abs(s[0].x),c=Math.abs(s[0].y),u=Math.abs(s[0].z);l<=h&&(h=l,i.set(1,0,0)),c<=h&&(h=c,i.set(0,1,0)),u<=h&&i.set(0,0,1),a.crossVectors(s[0],i).normalize(),r[0].crossVectors(s[0],a),n[0].crossVectors(s[0],r[0]);for(let e=1;e<=t;e++){if(r[e]=r[e-1].clone(),n[e]=n[e-1].clone(),a.crossVectors(s[e-1],s[e]),a.length()>Number.EPSILON){a.normalize();const t=Math.acos(Hi(s[e-1].dot(s[e]),-1,1));r[e].applyMatrix4(o.makeRotationAxis(a,t))}n[e].crossVectors(s[e],r[e])}if(!0===e){let e=Math.acos(Hi(r[0].dot(r[t]),-1,1));e/=t,s[0].dot(a.crossVectors(r[0],r[t]))>0&&(e=-e);for(let i=1;i<=t;i++)r[i].applyMatrix4(o.makeRotationAxis(s[i],e*i)),n[i].crossVectors(s[i],r[i])}return{tangents:s,normals:r,binormals:n}}clone(){return(new this.constructor).copy(this)}copy(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}toJSON(){const t={metadata:{version:4.7,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t}fromJSON(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}}class wh extends vh{constructor(t=0,e=0,i=1,s=1,r=0,n=2*Math.PI,a=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=t,this.aY=e,this.xRadius=i,this.yRadius=s,this.aStartAngle=r,this.aEndAngle=n,this.aClockwise=a,this.aRotation=o}getPoint(t,e=new Gi){const i=e,s=2*Math.PI;let r=this.aEndAngle-this.aStartAngle;const n=Math.abs(r)<Number.EPSILON;for(;r<0;)r+=s;for(;r>s;)r-=s;r<Number.EPSILON&&(r=n?0:s),!0!==this.aClockwise||n||(r===s?r=-s:r-=s);const a=this.aStartAngle+t*r;let o=this.aX+this.xRadius*Math.cos(a),h=this.aY+this.yRadius*Math.sin(a);if(0!==this.aRotation){const t=Math.cos(this.aRotation),e=Math.sin(this.aRotation),i=o-this.aX,s=h-this.aY;o=i*t-s*e+this.aX,h=i*e+s*t+this.aY}return i.set(o,h)}copy(t){return super.copy(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}toJSON(){const t=super.toJSON();return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t}fromJSON(t){return super.fromJSON(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}}class Mh extends wh{constructor(t,e,i,s,r,n){super(t,e,i,i,s,r,n),this.isArcCurve=!0,this.type="ArcCurve"}}function Sh(){let t=0,e=0,i=0,s=0;function r(r,n,a,o){t=r,e=a,i=-3*r+3*n-2*a-o,s=2*r-2*n+a+o}return{initCatmullRom:function(t,e,i,s,n){r(e,i,n*(i-t),n*(s-e))},initNonuniformCatmullRom:function(t,e,i,s,n,a,o){let h=(e-t)/n-(i-t)/(n+a)+(i-e)/a,l=(i-e)/a-(s-e)/(a+o)+(s-i)/o;h*=a,l*=a,r(e,i,h,l)},calc:function(r){const n=r*r;return t+e*r+i*n+s*(n*r)}}}const _h=new Qi,Ah=new Sh,Th=new Sh,zh=new Sh;class Ih extends vh{constructor(t=[],e=!1,i="centripetal",s=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=t,this.closed=e,this.curveType=i,this.tension=s}getPoint(t,e=new Qi){const i=e,s=this.points,r=s.length,n=(r-(this.closed?0:1))*t;let a,o,h=Math.floor(n),l=n-h;this.closed?h+=h>0?0:(Math.floor(Math.abs(h)/r)+1)*r:0===l&&h===r-1&&(h=r-2,l=1),this.closed||h>0?a=s[(h-1)%r]:(_h.subVectors(s[0],s[1]).add(s[0]),a=_h);const c=s[h%r],u=s[(h+1)%r];if(this.closed||h+2<r?o=s[(h+2)%r]:(_h.subVectors(s[r-1],s[r-2]).add(s[r-1]),o=_h),"centripetal"===this.curveType||"chordal"===this.curveType){const t="chordal"===this.curveType?.5:.25;let e=Math.pow(a.distanceToSquared(c),t),i=Math.pow(c.distanceToSquared(u),t),s=Math.pow(u.distanceToSquared(o),t);i<1e-4&&(i=1),e<1e-4&&(e=i),s<1e-4&&(s=i),Ah.initNonuniformCatmullRom(a.x,c.x,u.x,o.x,e,i,s),Th.initNonuniformCatmullRom(a.y,c.y,u.y,o.y,e,i,s),zh.initNonuniformCatmullRom(a.z,c.z,u.z,o.z,e,i,s)}else"catmullrom"===this.curveType&&(Ah.initCatmullRom(a.x,c.x,u.x,o.x,this.tension),Th.initCatmullRom(a.y,c.y,u.y,o.y,this.tension),zh.initCatmullRom(a.z,c.z,u.z,o.z,this.tension));return i.set(Ah.calc(l),Th.calc(l),zh.calc(l)),i}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){const i=t.points[e];this.points.push(i.clone())}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}toJSON(){const t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){const i=this.points[e];t.points.push(i.toArray())}return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){const i=t.points[e];this.points.push((new Qi).fromArray(i))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}}function Ch(t,e,i,s,r){const n=.5*(s-e),a=.5*(r-i),o=t*t;return(2*i-2*s+n+a)*(t*o)+(-3*i+3*s-2*n-a)*o+n*t+i}function Bh(t,e,i,s){return function(t,e){const i=1-t;return i*i*e}(t,e)+function(t,e){return 2*(1-t)*t*e}(t,i)+function(t,e){return t*t*e}(t,s)}function kh(t,e,i,s,r){return function(t,e){const i=1-t;return i*i*i*e}(t,e)+function(t,e){const i=1-t;return 3*i*i*t*e}(t,i)+function(t,e){return 3*(1-t)*t*t*e}(t,s)+function(t,e){return t*t*t*e}(t,r)}class Eh extends vh{constructor(t=new Gi,e=new Gi,i=new Gi,s=new Gi){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new Gi){const i=e,s=this.v0,r=this.v1,n=this.v2,a=this.v3;return i.set(kh(t,s.x,r.x,n.x,a.x),kh(t,s.y,r.y,n.y,a.y)),i}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class Rh extends vh{constructor(t=new Qi,e=new Qi,i=new Qi,s=new Qi){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new Qi){const i=e,s=this.v0,r=this.v1,n=this.v2,a=this.v3;return i.set(kh(t,s.x,r.x,n.x,a.x),kh(t,s.y,r.y,n.y,a.y),kh(t,s.z,r.z,n.z,a.z)),i}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class Ph extends vh{constructor(t=new Gi,e=new Gi){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=t,this.v2=e}getPoint(t,e=new Gi){const i=e;return 1===t?i.copy(this.v2):(i.copy(this.v2).sub(this.v1),i.multiplyScalar(t).add(this.v1)),i}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new Gi){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class Oh extends vh{constructor(t=new Qi,e=new Qi){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=t,this.v2=e}getPoint(t,e=new Qi){const i=e;return 1===t?i.copy(this.v2):(i.copy(this.v2).sub(this.v1),i.multiplyScalar(t).add(this.v1)),i}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new Qi){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class Nh extends vh{constructor(t=new Gi,e=new Gi,i=new Gi){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new Gi){const i=e,s=this.v0,r=this.v1,n=this.v2;return i.set(Bh(t,s.x,r.x,n.x),Bh(t,s.y,r.y,n.y)),i}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class Fh extends vh{constructor(t=new Qi,e=new Qi,i=new Qi){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new Qi){const i=e,s=this.v0,r=this.v1,n=this.v2;return i.set(Bh(t,s.x,r.x,n.x),Bh(t,s.y,r.y,n.y),Bh(t,s.z,r.z,n.z)),i}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class Vh extends vh{constructor(t=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=t}getPoint(t,e=new Gi){const i=e,s=this.points,r=(s.length-1)*t,n=Math.floor(r),a=r-n,o=s[0===n?n:n-1],h=s[n],l=s[n>s.length-2?s.length-1:n+1],c=s[n>s.length-3?s.length-1:n+2];return i.set(Ch(a,o.x,h.x,l.x,c.x),Ch(a,o.y,h.y,l.y,c.y)),i}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){const i=t.points[e];this.points.push(i.clone())}return this}toJSON(){const t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){const i=this.points[e];t.points.push(i.toArray())}return t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){const i=t.points[e];this.points.push((new Gi).fromArray(i))}return this}}var Lh=Object.freeze({__proto__:null,ArcCurve:Mh,CatmullRomCurve3:Ih,CubicBezierCurve:Eh,CubicBezierCurve3:Rh,EllipseCurve:wh,LineCurve:Ph,LineCurve3:Oh,QuadraticBezierCurve:Nh,QuadraticBezierCurve3:Fh,SplineCurve:Vh});class jh extends vh{constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}add(t){this.curves.push(t)}closePath(){const t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);if(!t.equals(e)){const i=!0===t.isVector2?"LineCurve":"LineCurve3";this.curves.push(new Lh[i](e,t))}return this}getPoint(t,e){const i=t*this.getLength(),s=this.getCurveLengths();let r=0;for(;r<s.length;){if(s[r]>=i){const t=s[r]-i,n=this.curves[r],a=n.getLength(),o=0===a?0:1-t/a;return n.getPointAt(o,e)}r++}return null}getLength(){const t=this.getCurveLengths();return t[t.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;const t=[];let e=0;for(let i=0,s=this.curves.length;i<s;i++)e+=this.curves[i].getLength(),t.push(e);return this.cacheLengths=t,t}getSpacedPoints(t=40){const e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return this.autoClose&&e.push(e[0]),e}getPoints(t=12){const e=[];let i;for(let s=0,r=this.curves;s<r.length;s++){const n=r[s],a=n.isEllipseCurve?2*t:n.isLineCurve||n.isLineCurve3?1:n.isSplineCurve?t*n.points.length:t,o=n.getPoints(a);for(let t=0;t<o.length;t++){const s=o[t];i&&i.equals(s)||(e.push(s),i=s)}}return this.autoClose&&e.length>1&&!e[e.length-1].equals(e[0])&&e.push(e[0]),e}copy(t){super.copy(t),this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){const i=t.curves[e];this.curves.push(i.clone())}return this.autoClose=t.autoClose,this}toJSON(){const t=super.toJSON();t.autoClose=this.autoClose,t.curves=[];for(let e=0,i=this.curves.length;e<i;e++){const i=this.curves[e];t.curves.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.autoClose=t.autoClose,this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){const i=t.curves[e];this.curves.push((new Lh[i.type]).fromJSON(i))}return this}}class Wh extends jh{constructor(t){super(),this.type="Path",this.currentPoint=new Gi,t&&this.setFromPoints(t)}setFromPoints(t){this.moveTo(t[0].x,t[0].y);for(let e=1,i=t.length;e<i;e++)this.lineTo(t[e].x,t[e].y);return this}moveTo(t,e){return this.currentPoint.set(t,e),this}lineTo(t,e){const i=new Ph(this.currentPoint.clone(),new Gi(t,e));return this.curves.push(i),this.currentPoint.set(t,e),this}quadraticCurveTo(t,e,i,s){const r=new Nh(this.currentPoint.clone(),new Gi(t,e),new Gi(i,s));return this.curves.push(r),this.currentPoint.set(i,s),this}bezierCurveTo(t,e,i,s,r,n){const a=new Eh(this.currentPoint.clone(),new Gi(t,e),new Gi(i,s),new Gi(r,n));return this.curves.push(a),this.currentPoint.set(r,n),this}splineThru(t){const e=[this.currentPoint.clone()].concat(t),i=new Vh(e);return this.curves.push(i),this.currentPoint.copy(t[t.length-1]),this}arc(t,e,i,s,r,n){const a=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(t+a,e+o,i,s,r,n),this}absarc(t,e,i,s,r,n){return this.absellipse(t,e,i,i,s,r,n),this}ellipse(t,e,i,s,r,n,a,o){const h=this.currentPoint.x,l=this.currentPoint.y;return this.absellipse(t+h,e+l,i,s,r,n,a,o),this}absellipse(t,e,i,s,r,n,a,o){const h=new wh(t,e,i,s,r,n,a,o);if(this.curves.length>0){const t=h.getPoint(0);t.equals(this.currentPoint)||this.lineTo(t.x,t.y)}this.curves.push(h);const l=h.getPoint(1);return this.currentPoint.copy(l),this}copy(t){return super.copy(t),this.currentPoint.copy(t.currentPoint),this}toJSON(){const t=super.toJSON();return t.currentPoint=this.currentPoint.toArray(),t}fromJSON(t){return super.fromJSON(t),this.currentPoint.fromArray(t.currentPoint),this}}class Uh extends Wh{constructor(t){super(t),this.uuid=Di(),this.type="Shape",this.holes=[]}getPointsHoles(t){const e=[];for(let i=0,s=this.holes.length;i<s;i++)e[i]=this.holes[i].getPoints(t);return e}extractPoints(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}}copy(t){super.copy(t),this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){const i=t.holes[e];this.holes.push(i.clone())}return this}toJSON(){const t=super.toJSON();t.uuid=this.uuid,t.holes=[];for(let e=0,i=this.holes.length;e<i;e++){const i=this.holes[e];t.holes.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.uuid=t.uuid,this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){const i=t.holes[e];this.holes.push((new Wh).fromJSON(i))}return this}}function Dh(t,e,i=2){const s=e&&e.length,r=s?e[0]*i:t.length;let n=Hh(t,0,r,i,!0);const a=[];if(!n||n.next===n.prev)return a;let o,h,l;if(s&&(n=function(t,e,i,s){const r=[];for(let i=0,n=e.length;i<n;i++){const a=Hh(t,e[i]*s,i<n-1?e[i+1]*s:t.length,s,!1);a===a.next&&(a.steiner=!0),r.push(el(a))}r.sort($h);for(let t=0;t<r.length;t++)i=Qh(r[t],i);return i}(t,e,n,i)),t.length>80*i){o=1/0,h=1/0;let e=-1/0,s=-1/0;for(let n=i;n<r;n+=i){const i=t[n],r=t[n+1];i<o&&(o=i),r<h&&(h=r),i>e&&(e=i),r>s&&(s=r)}l=Math.max(e-o,s-h),l=0!==l?32767/l:0}return Jh(n,a,i,o,h,l,0),a}function Hh(t,e,i,s,r){let n;if(r===function(t,e,i,s){let r=0;for(let n=e,a=i-s;n<i;n+=s)r+=(t[a]-t[n])*(t[n+1]+t[a+1]),a=n;return r}(t,e,i,s)>0)for(let r=e;r<i;r+=s)n=dl(r/s|0,t[r],t[r+1],n);else for(let r=i-s;r>=e;r-=s)n=dl(r/s|0,t[r],t[r+1],n);return n&&al(n,n.next)&&(pl(n),n=n.next),n}function qh(t,e){if(!t)return t;e||(e=t);let i,s=t;do{if(i=!1,s.steiner||!al(s,s.next)&&0!==nl(s.prev,s,s.next))s=s.next;else{if(pl(s),s=e=s.prev,s===s.next)break;i=!0}}while(i||s!==e);return e}function Jh(t,e,i,s,r,n,a){if(!t)return;!a&&n&&function(t,e,i,s){let r=t;do{0===r.z&&(r.z=tl(r.x,r.y,e,i,s)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next}while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,i=1;do{let s,r=t;t=null;let n=null;for(e=0;r;){e++;let a=r,o=0;for(let t=0;t<i&&(o++,a=a.nextZ,a);t++);let h=i;for(;o>0||h>0&&a;)0!==o&&(0===h||!a||r.z<=a.z)?(s=r,r=r.nextZ,o--):(s=a,a=a.nextZ,h--),n?n.nextZ=s:t=s,s.prevZ=n,n=s;r=a}n.nextZ=null,i*=2}while(e>1)}(r)}(t,s,r,n);let o=t;for(;t.prev!==t.next;){const h=t.prev,l=t.next;if(n?Yh(t,s,r,n):Xh(t))e.push(h.i,t.i,l.i),pl(t),t=l.next,o=l.next;else if((t=l)===o){a?1===a?Jh(t=Zh(qh(t),e),e,i,s,r,n,2):2===a&&Gh(t,e,i,s,r,n):Jh(qh(t),e,i,s,r,n,1);break}}}function Xh(t){const e=t.prev,i=t,s=t.next;if(nl(e,i,s)>=0)return!1;const r=e.x,n=i.x,a=s.x,o=e.y,h=i.y,l=s.y,c=Math.min(r,n,a),u=Math.min(o,h,l),d=Math.max(r,n,a),p=Math.max(o,h,l);let m=s.next;for(;m!==e;){if(m.x>=c&&m.x<=d&&m.y>=u&&m.y<=p&&sl(r,o,n,h,a,l,m.x,m.y)&&nl(m.prev,m,m.next)>=0)return!1;m=m.next}return!0}function Yh(t,e,i,s){const r=t.prev,n=t,a=t.next;if(nl(r,n,a)>=0)return!1;const o=r.x,h=n.x,l=a.x,c=r.y,u=n.y,d=a.y,p=Math.min(o,h,l),m=Math.min(c,u,d),y=Math.max(o,h,l),g=Math.max(c,u,d),f=tl(p,m,e,i,s),x=tl(y,g,e,i,s);let b=t.prevZ,v=t.nextZ;for(;b&&b.z>=f&&v&&v.z<=x;){if(b.x>=p&&b.x<=y&&b.y>=m&&b.y<=g&&b!==r&&b!==a&&sl(o,c,h,u,l,d,b.x,b.y)&&nl(b.prev,b,b.next)>=0)return!1;if(b=b.prevZ,v.x>=p&&v.x<=y&&v.y>=m&&v.y<=g&&v!==r&&v!==a&&sl(o,c,h,u,l,d,v.x,v.y)&&nl(v.prev,v,v.next)>=0)return!1;v=v.nextZ}for(;b&&b.z>=f;){if(b.x>=p&&b.x<=y&&b.y>=m&&b.y<=g&&b!==r&&b!==a&&sl(o,c,h,u,l,d,b.x,b.y)&&nl(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;v&&v.z<=x;){if(v.x>=p&&v.x<=y&&v.y>=m&&v.y<=g&&v!==r&&v!==a&&sl(o,c,h,u,l,d,v.x,v.y)&&nl(v.prev,v,v.next)>=0)return!1;v=v.nextZ}return!0}function Zh(t,e){let i=t;do{const s=i.prev,r=i.next.next;!al(s,r)&&ol(s,i,i.next,r)&&cl(s,r)&&cl(r,s)&&(e.push(s.i,i.i,r.i),pl(i),pl(i.next),i=t=r),i=i.next}while(i!==t);return qh(i)}function Gh(t,e,i,s,r,n){let a=t;do{let t=a.next.next;for(;t!==a.prev;){if(a.i!==t.i&&rl(a,t)){let o=ul(a,t);return a=qh(a,a.next),o=qh(o,o.next),Jh(a,e,i,s,r,n,0),void Jh(o,e,i,s,r,n,0)}t=t.next}a=a.next}while(a!==t)}function $h(t,e){let i=t.x-e.x;if(0===i&&(i=t.y-e.y,0===i)){i=(t.next.y-t.y)/(t.next.x-t.x)-(e.next.y-e.y)/(e.next.x-e.x)}return i}function Qh(t,e){const i=function(t,e){let i=e;const s=t.x,r=t.y;let n,a=-1/0;if(al(t,i))return i;do{if(al(t,i.next))return i.next;if(r<=i.y&&r>=i.next.y&&i.next.y!==i.y){const t=i.x+(r-i.y)*(i.next.x-i.x)/(i.next.y-i.y);if(t<=s&&t>a&&(a=t,n=i.x<i.next.x?i:i.next,t===s))return n}i=i.next}while(i!==e);if(!n)return null;const o=n,h=n.x,l=n.y;let c=1/0;i=n;do{if(s>=i.x&&i.x>=h&&s!==i.x&&il(r<l?s:a,r,h,l,r<l?a:s,r,i.x,i.y)){const e=Math.abs(r-i.y)/(s-i.x);cl(i,t)&&(e<c||e===c&&(i.x>n.x||i.x===n.x&&Kh(n,i)))&&(n=i,c=e)}i=i.next}while(i!==o);return n}(t,e);if(!i)return e;const s=ul(i,t);return qh(s,s.next),qh(i,i.next)}function Kh(t,e){return nl(t.prev,t,e.prev)<0&&nl(e.next,t,t.next)<0}function tl(t,e,i,s,r){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-i)*r|0)|t<<8))|t<<4))|t<<2))|t<<1))|(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-s)*r|0)|e<<8))|e<<4))|e<<2))|e<<1))<<1}function el(t){let e=t,i=t;do{(e.x<i.x||e.x===i.x&&e.y<i.y)&&(i=e),e=e.next}while(e!==t);return i}function il(t,e,i,s,r,n,a,o){return(r-a)*(e-o)>=(t-a)*(n-o)&&(t-a)*(s-o)>=(i-a)*(e-o)&&(i-a)*(n-o)>=(r-a)*(s-o)}function sl(t,e,i,s,r,n,a,o){return!(t===a&&e===o)&&il(t,e,i,s,r,n,a,o)}function rl(t,e){return t.next.i!==e.i&&t.prev.i!==e.i&&!function(t,e){let i=t;do{if(i.i!==t.i&&i.next.i!==t.i&&i.i!==e.i&&i.next.i!==e.i&&ol(i,i.next,t,e))return!0;i=i.next}while(i!==t);return!1}(t,e)&&(cl(t,e)&&cl(e,t)&&function(t,e){let i=t,s=!1;const r=(t.x+e.x)/2,n=(t.y+e.y)/2;do{i.y>n!=i.next.y>n&&i.next.y!==i.y&&r<(i.next.x-i.x)*(n-i.y)/(i.next.y-i.y)+i.x&&(s=!s),i=i.next}while(i!==t);return s}(t,e)&&(nl(t.prev,t,e.prev)||nl(t,e.prev,e))||al(t,e)&&nl(t.prev,t,t.next)>0&&nl(e.prev,e,e.next)>0)}function nl(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function al(t,e){return t.x===e.x&&t.y===e.y}function ol(t,e,i,s){const r=ll(nl(t,e,i)),n=ll(nl(t,e,s)),a=ll(nl(i,s,t)),o=ll(nl(i,s,e));return r!==n&&a!==o||(!(0!==r||!hl(t,i,e))||(!(0!==n||!hl(t,s,e))||(!(0!==a||!hl(i,t,s))||!(0!==o||!hl(i,e,s)))))}function hl(t,e,i){return e.x<=Math.max(t.x,i.x)&&e.x>=Math.min(t.x,i.x)&&e.y<=Math.max(t.y,i.y)&&e.y>=Math.min(t.y,i.y)}function ll(t){return t>0?1:t<0?-1:0}function cl(t,e){return nl(t.prev,t,t.next)<0?nl(t,e,t.next)>=0&&nl(t,t.prev,e)>=0:nl(t,e,t.prev)<0||nl(t,t.next,e)<0}function ul(t,e){const i=ml(t.i,t.x,t.y),s=ml(e.i,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,s.next=i,i.prev=s,n.next=s,s.prev=n,s}function dl(t,e,i,s){const r=ml(t,e,i);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function pl(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function ml(t,e,i){return{i:t,x:e,y:i,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}class yl{static triangulate(t,e,i=2){return Dh(t,e,i)}}class gl{static area(t){const e=t.length;let i=0;for(let s=e-1,r=0;r<e;s=r++)i+=t[s].x*t[r].y-t[r].x*t[s].y;return.5*i}static isClockWise(t){return gl.area(t)<0}static triangulateShape(t,e){const i=[],s=[],r=[];fl(t),xl(i,t);let n=t.length;e.forEach(fl);for(let t=0;t<e.length;t++)s.push(n),n+=e[t].length,xl(i,e[t]);const a=yl.triangulate(i,s);for(let t=0;t<a.length;t+=3)r.push(a.slice(t,t+3));return r}}function fl(t){const e=t.length;e>2&&t[e-1].equals(t[0])&&t.pop()}function xl(t,e){for(let i=0;i<e.length;i++)t.push(e[i].x),t.push(e[i].y)}class bl extends Bn{constructor(t=new Uh([new Gi(.5,.5),new Gi(-.5,.5),new Gi(-.5,-.5),new Gi(.5,-.5)]),e={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];const i=this,s=[],r=[];for(let e=0,i=t.length;e<i;e++){n(t[e])}function n(t){const n=[],a=void 0!==e.curveSegments?e.curveSegments:12,o=void 0!==e.steps?e.steps:1,h=void 0!==e.depth?e.depth:1;let l=void 0===e.bevelEnabled||e.bevelEnabled,c=void 0!==e.bevelThickness?e.bevelThickness:.2,u=void 0!==e.bevelSize?e.bevelSize:c-.1,d=void 0!==e.bevelOffset?e.bevelOffset:0,p=void 0!==e.bevelSegments?e.bevelSegments:3;const m=e.extrudePath,y=void 0!==e.UVGenerator?e.UVGenerator:vl;let g,f,x,b,v,w=!1;m&&(g=m.getSpacedPoints(o),w=!0,l=!1,f=m.computeFrenetFrames(o,!1),x=new Qi,b=new Qi,v=new Qi),l||(p=0,c=0,u=0,d=0);const M=t.extractPoints(a);let S=M.shape;const _=M.holes;if(!gl.isClockWise(S)){S=S.reverse();for(let t=0,e=_.length;t<e;t++){const e=_[t];gl.isClockWise(e)&&(_[t]=e.reverse())}}function A(t){const e=1e-10*1e-10;let i=t[0];for(let s=1;s<=t.length;s++){const r=s%t.length,n=t[r],a=n.x-i.x,o=n.y-i.y,h=a*a+o*o,l=Math.max(Math.abs(n.x),Math.abs(n.y),Math.abs(i.x),Math.abs(i.y));h<=e*l*l?(t.splice(r,1),s--):i=n}}A(S),_.forEach(A);const T=_.length,z=S;for(let t=0;t<T;t++){const e=_[t];S=S.concat(e)}function I(t,e,i){return e||console.error("THREE.ExtrudeGeometry: vec does not exist"),t.clone().addScaledVector(e,i)}const C=S.length;function B(t,e,i){let s,r,n;const a=t.x-e.x,o=t.y-e.y,h=i.x-t.x,l=i.y-t.y,c=a*a+o*o,u=a*l-o*h;if(Math.abs(u)>Number.EPSILON){const u=Math.sqrt(c),d=Math.sqrt(h*h+l*l),p=e.x-o/u,m=e.y+a/u,y=((i.x-l/d-p)*l-(i.y+h/d-m)*h)/(a*l-o*h);s=p+a*y-t.x,r=m+o*y-t.y;const g=s*s+r*r;if(g<=2)return new Gi(s,r);n=Math.sqrt(g/2)}else{let t=!1;a>Number.EPSILON?h>Number.EPSILON&&(t=!0):a<-Number.EPSILON?h<-Number.EPSILON&&(t=!0):Math.sign(o)===Math.sign(l)&&(t=!0),t?(s=-o,r=a,n=Math.sqrt(c)):(s=a,r=o,n=Math.sqrt(c/2))}return new Gi(s/n,r/n)}const k=[];for(let t=0,e=z.length,i=e-1,s=t+1;t<e;t++,i++,s++)i===e&&(i=0),s===e&&(s=0),k[t]=B(z[t],z[i],z[s]);const E=[];let R,P,O=k.concat();for(let t=0,e=T;t<e;t++){const e=_[t];R=[];for(let t=0,i=e.length,s=i-1,r=t+1;t<i;t++,s++,r++)s===i&&(s=0),r===i&&(r=0),R[t]=B(e[t],e[s],e[r]);E.push(R),O=O.concat(R)}if(0===p)P=gl.triangulateShape(z,_);else{const t=[],e=[];for(let i=0;i<p;i++){const s=i/p,r=c*Math.cos(s*Math.PI/2),n=u*Math.sin(s*Math.PI/2)+d;for(let e=0,i=z.length;e<i;e++){const i=I(z[e],k[e],n);L(i.x,i.y,-r),0===s&&t.push(i)}for(let t=0,i=T;t<i;t++){const i=_[t];R=E[t];const a=[];for(let t=0,e=i.length;t<e;t++){const e=I(i[t],R[t],n);L(e.x,e.y,-r),0===s&&a.push(e)}0===s&&e.push(a)}}P=gl.triangulateShape(t,e)}const N=P.length,F=u+d;for(let t=0;t<C;t++){const e=l?I(S[t],O[t],F):S[t];w?(b.copy(f.normals[0]).multiplyScalar(e.x),x.copy(f.binormals[0]).multiplyScalar(e.y),v.copy(g[0]).add(b).add(x),L(v.x,v.y,v.z)):L(e.x,e.y,0)}for(let t=1;t<=o;t++)for(let e=0;e<C;e++){const i=l?I(S[e],O[e],F):S[e];w?(b.copy(f.normals[t]).multiplyScalar(i.x),x.copy(f.binormals[t]).multiplyScalar(i.y),v.copy(g[t]).add(b).add(x),L(v.x,v.y,v.z)):L(i.x,i.y,h/o*t)}for(let t=p-1;t>=0;t--){const e=t/p,i=c*Math.cos(e*Math.PI/2),s=u*Math.sin(e*Math.PI/2)+d;for(let t=0,e=z.length;t<e;t++){const e=I(z[t],k[t],s);L(e.x,e.y,h+i)}for(let t=0,e=_.length;t<e;t++){const e=_[t];R=E[t];for(let t=0,r=e.length;t<r;t++){const r=I(e[t],R[t],s);w?L(r.x,r.y+g[o-1].y,g[o-1].x+i):L(r.x,r.y,h+i)}}}function V(t,e){let i=t.length;for(;--i>=0;){const s=i;let r=i-1;r<0&&(r=t.length-1);for(let t=0,i=o+2*p;t<i;t++){const i=C*t,n=C*(t+1);W(e+s+i,e+r+i,e+r+n,e+s+n)}}}function L(t,e,i){n.push(t),n.push(e),n.push(i)}function j(t,e,r){U(t),U(e),U(r);const n=s.length/3,a=y.generateTopUV(i,s,n-3,n-2,n-1);D(a[0]),D(a[1]),D(a[2])}function W(t,e,r,n){U(t),U(e),U(n),U(e),U(r),U(n);const a=s.length/3,o=y.generateSideWallUV(i,s,a-6,a-3,a-2,a-1);D(o[0]),D(o[1]),D(o[3]),D(o[1]),D(o[2]),D(o[3])}function U(t){s.push(n[3*t+0]),s.push(n[3*t+1]),s.push(n[3*t+2])}function D(t){r.push(t.x),r.push(t.y)}!function(){const t=s.length/3;if(l){let t=0,e=C*t;for(let t=0;t<N;t++){const i=P[t];j(i[2]+e,i[1]+e,i[0]+e)}t=o+2*p,e=C*t;for(let t=0;t<N;t++){const i=P[t];j(i[0]+e,i[1]+e,i[2]+e)}}else{for(let t=0;t<N;t++){const e=P[t];j(e[2],e[1],e[0])}for(let t=0;t<N;t++){const e=P[t];j(e[0]+C*o,e[1]+C*o,e[2]+C*o)}}i.addGroup(t,s.length/3-t,0)}(),function(){const t=s.length/3;let e=0;V(z,e),e+=z.length;for(let t=0,i=_.length;t<i;t++){const i=_[t];V(i,e),e+=i.length}i.addGroup(t,s.length/3-t,1)}()}this.setAttribute("position",new Mn(s,3)),this.setAttribute("uv",new Mn(r,2)),this.computeVertexNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return function(t,e,i){if(i.shapes=[],Array.isArray(t))for(let e=0,s=t.length;e<s;e++){const s=t[e];i.shapes.push(s.uuid)}else i.shapes.push(t.uuid);i.options=Object.assign({},e),void 0!==e.extrudePath&&(i.options.extrudePath=e.extrudePath.toJSON());return i}(this.parameters.shapes,this.parameters.options,t)}static fromJSON(t,e){const i=[];for(let s=0,r=t.shapes.length;s<r;s++){const r=e[t.shapes[s]];i.push(r)}const s=t.options.extrudePath;return void 0!==s&&(t.options.extrudePath=(new Lh[s.type]).fromJSON(s)),new bl(i,t.options)}}const vl={generateTopUV:function(t,e,i,s,r){const n=e[3*i],a=e[3*i+1],o=e[3*s],h=e[3*s+1],l=e[3*r],c=e[3*r+1];return[new Gi(n,a),new Gi(o,h),new Gi(l,c)]},generateSideWallUV:function(t,e,i,s,r,n){const a=e[3*i],o=e[3*i+1],h=e[3*i+2],l=e[3*s],c=e[3*s+1],u=e[3*s+2],d=e[3*r],p=e[3*r+1],m=e[3*r+2],y=e[3*n],g=e[3*n+1],f=e[3*n+2];return Math.abs(o-c)<Math.abs(a-l)?[new Gi(a,1-h),new Gi(l,1-u),new Gi(d,1-m),new Gi(y,1-f)]:[new Gi(o,1-h),new Gi(c,1-u),new Gi(p,1-m),new Gi(g,1-f)]}};class wl extends ph{constructor(t=1,e=0){const i=(1+Math.sqrt(5))/2;super([-1,i,0,1,i,0,-1,-i,0,1,-i,0,0,-1,i,0,1,i,0,-1,-i,0,1,-i,i,0,-1,i,0,1,-i,0,-1,-i,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new wl(t.radius,t.detail)}}class Ml extends Bn{constructor(t=[new Gi(0,-.5),new Gi(.5,0),new Gi(0,.5)],e=12,i=0,s=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:i,phiLength:s},e=Math.floor(e),s=Hi(s,0,2*Math.PI);const r=[],n=[],a=[],o=[],h=[],l=1/e,c=new Qi,u=new Gi,d=new Qi,p=new Qi,m=new Qi;let y=0,g=0;for(let e=0;e<=t.length-1;e++)switch(e){case 0:y=t[e+1].x-t[e].x,g=t[e+1].y-t[e].y,d.x=1*g,d.y=-y,d.z=0*g,m.copy(d),d.normalize(),o.push(d.x,d.y,d.z);break;case t.length-1:o.push(m.x,m.y,m.z);break;default:y=t[e+1].x-t[e].x,g=t[e+1].y-t[e].y,d.x=1*g,d.y=-y,d.z=0*g,p.copy(d),d.x+=m.x,d.y+=m.y,d.z+=m.z,d.normalize(),o.push(d.x,d.y,d.z),m.copy(p)}for(let r=0;r<=e;r++){const d=i+r*l*s,p=Math.sin(d),m=Math.cos(d);for(let i=0;i<=t.length-1;i++){c.x=t[i].x*p,c.y=t[i].y,c.z=t[i].x*m,n.push(c.x,c.y,c.z),u.x=r/e,u.y=i/(t.length-1),a.push(u.x,u.y);const s=o[3*i+0]*p,l=o[3*i+1],d=o[3*i+0]*m;h.push(s,l,d)}}for(let i=0;i<e;i++)for(let e=0;e<t.length-1;e++){const s=e+i*t.length,n=s,a=s+t.length,o=s+t.length+1,h=s+1;r.push(n,a,h),r.push(o,h,a)}this.setIndex(r),this.setAttribute("position",new Mn(n,3)),this.setAttribute("uv",new Mn(a,2)),this.setAttribute("normal",new Mn(h,3))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Ml(t.points,t.segments,t.phiStart,t.phiLength)}}class Sl extends ph{constructor(t=1,e=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new Sl(t.radius,t.detail)}}class _l extends Bn{constructor(t=1,e=1,i=1,s=1){super(),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:s};const r=t/2,n=e/2,a=Math.floor(i),o=Math.floor(s),h=a+1,l=o+1,c=t/a,u=e/o,d=[],p=[],m=[],y=[];for(let t=0;t<l;t++){const e=t*u-n;for(let i=0;i<h;i++){const s=i*c-r;p.push(s,-e,0),m.push(0,0,1),y.push(i/a),y.push(1-t/o)}}for(let t=0;t<o;t++)for(let e=0;e<a;e++){const i=e+h*t,s=e+h*(t+1),r=e+1+h*(t+1),n=e+1+h*t;d.push(i,s,n),d.push(s,r,n)}this.setIndex(d),this.setAttribute("position",new Mn(p,3)),this.setAttribute("normal",new Mn(m,3)),this.setAttribute("uv",new Mn(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new _l(t.width,t.height,t.widthSegments,t.heightSegments)}}class Al extends Bn{constructor(t=.5,e=1,i=32,s=1,r=0,n=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:i,phiSegments:s,thetaStart:r,thetaLength:n},i=Math.max(3,i);const a=[],o=[],h=[],l=[];let c=t;const u=(e-t)/(s=Math.max(1,s)),d=new Qi,p=new Gi;for(let t=0;t<=s;t++){for(let t=0;t<=i;t++){const s=r+t/i*n;d.x=c*Math.cos(s),d.y=c*Math.sin(s),o.push(d.x,d.y,d.z),h.push(0,0,1),p.x=(d.x/e+1)/2,p.y=(d.y/e+1)/2,l.push(p.x,p.y)}c+=u}for(let t=0;t<s;t++){const e=t*(i+1);for(let t=0;t<i;t++){const s=t+e,r=s,n=s+i+1,o=s+i+2,h=s+1;a.push(r,n,h),a.push(n,o,h)}}this.setIndex(a),this.setAttribute("position",new Mn(o,3)),this.setAttribute("normal",new Mn(h,3)),this.setAttribute("uv",new Mn(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Al(t.innerRadius,t.outerRadius,t.thetaSegments,t.phiSegments,t.thetaStart,t.thetaLength)}}class Tl extends Bn{constructor(t=new Uh([new Gi(0,.5),new Gi(-.5,-.5),new Gi(.5,-.5)]),e=12){super(),this.type="ShapeGeometry",this.parameters={shapes:t,curveSegments:e};const i=[],s=[],r=[],n=[];let a=0,o=0;if(!1===Array.isArray(t))h(t);else for(let e=0;e<t.length;e++)h(t[e]),this.addGroup(a,o,e),a+=o,o=0;function h(t){const a=s.length/3,h=t.extractPoints(e);let l=h.shape;const c=h.holes;!1===gl.isClockWise(l)&&(l=l.reverse());for(let t=0,e=c.length;t<e;t++){const e=c[t];!0===gl.isClockWise(e)&&(c[t]=e.reverse())}const u=gl.triangulateShape(l,c);for(let t=0,e=c.length;t<e;t++){const e=c[t];l=l.concat(e)}for(let t=0,e=l.length;t<e;t++){const e=l[t];s.push(e.x,e.y,0),r.push(0,0,1),n.push(e.x,e.y)}for(let t=0,e=u.length;t<e;t++){const e=u[t],s=e[0]+a,r=e[1]+a,n=e[2]+a;i.push(s,r,n),o+=3}}this.setIndex(i),this.setAttribute("position",new Mn(s,3)),this.setAttribute("normal",new Mn(r,3)),this.setAttribute("uv",new Mn(n,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return function(t,e){if(e.shapes=[],Array.isArray(t))for(let i=0,s=t.length;i<s;i++){const s=t[i];e.shapes.push(s.uuid)}else e.shapes.push(t.uuid);return e}(this.parameters.shapes,t)}static fromJSON(t,e){const i=[];for(let s=0,r=t.shapes.length;s<r;s++){const r=e[t.shapes[s]];i.push(r)}return new Tl(i,t.curveSegments)}}class zl extends Bn{constructor(t=1,e=32,i=16,s=0,r=2*Math.PI,n=0,a=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:s,phiLength:r,thetaStart:n,thetaLength:a},e=Math.max(3,Math.floor(e)),i=Math.max(2,Math.floor(i));const o=Math.min(n+a,Math.PI);let h=0;const l=[],c=new Qi,u=new Qi,d=[],p=[],m=[],y=[];for(let d=0;d<=i;d++){const g=[],f=d/i;let x=0;0===d&&0===n?x=.5/e:d===i&&o===Math.PI&&(x=-.5/e);for(let i=0;i<=e;i++){const o=i/e;c.x=-t*Math.cos(s+o*r)*Math.sin(n+f*a),c.y=t*Math.cos(n+f*a),c.z=t*Math.sin(s+o*r)*Math.sin(n+f*a),p.push(c.x,c.y,c.z),u.copy(c).normalize(),m.push(u.x,u.y,u.z),y.push(o+x,1-f),g.push(h++)}l.push(g)}for(let t=0;t<i;t++)for(let s=0;s<e;s++){const e=l[t][s+1],r=l[t][s],a=l[t+1][s],h=l[t+1][s+1];(0!==t||n>0)&&d.push(e,r,h),(t!==i-1||o<Math.PI)&&d.push(r,a,h)}this.setIndex(d),this.setAttribute("position",new Mn(p,3)),this.setAttribute("normal",new Mn(m,3)),this.setAttribute("uv",new Mn(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new zl(t.radius,t.widthSegments,t.heightSegments,t.phiStart,t.phiLength,t.thetaStart,t.thetaLength)}}class Il extends ph{constructor(t=1,e=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new Il(t.radius,t.detail)}}class Cl extends Bn{constructor(t=1,e=.4,i=12,s=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:s,arc:r},i=Math.floor(i),s=Math.floor(s);const n=[],a=[],o=[],h=[],l=new Qi,c=new Qi,u=new Qi;for(let n=0;n<=i;n++)for(let d=0;d<=s;d++){const p=d/s*r,m=n/i*Math.PI*2;c.x=(t+e*Math.cos(m))*Math.cos(p),c.y=(t+e*Math.cos(m))*Math.sin(p),c.z=e*Math.sin(m),a.push(c.x,c.y,c.z),l.x=t*Math.cos(p),l.y=t*Math.sin(p),u.subVectors(c,l).normalize(),o.push(u.x,u.y,u.z),h.push(d/s),h.push(n/i)}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){const i=(s+1)*t+e-1,r=(s+1)*(t-1)+e-1,a=(s+1)*(t-1)+e,o=(s+1)*t+e;n.push(i,r,o),n.push(r,a,o)}this.setIndex(n),this.setAttribute("position",new Mn(a,3)),this.setAttribute("normal",new Mn(o,3)),this.setAttribute("uv",new Mn(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Cl(t.radius,t.tube,t.radialSegments,t.tubularSegments,t.arc)}}class Bl extends Bn{constructor(t=1,e=.4,i=64,s=8,r=2,n=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:s,p:r,q:n},i=Math.floor(i),s=Math.floor(s);const a=[],o=[],h=[],l=[],c=new Qi,u=new Qi,d=new Qi,p=new Qi,m=new Qi,y=new Qi,g=new Qi;for(let a=0;a<=i;++a){const x=a/i*r*Math.PI*2;f(x,r,n,t,d),f(x+.01,r,n,t,p),y.subVectors(p,d),g.addVectors(p,d),m.crossVectors(y,g),g.crossVectors(m,y),m.normalize(),g.normalize();for(let t=0;t<=s;++t){const r=t/s*Math.PI*2,n=-e*Math.cos(r),p=e*Math.sin(r);c.x=d.x+(n*g.x+p*m.x),c.y=d.y+(n*g.y+p*m.y),c.z=d.z+(n*g.z+p*m.z),o.push(c.x,c.y,c.z),u.subVectors(c,d).normalize(),h.push(u.x,u.y,u.z),l.push(a/i),l.push(t/s)}}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){const i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,o=(s+1)*(t-1)+e;a.push(i,r,o),a.push(r,n,o)}function f(t,e,i,s,r){const n=Math.cos(t),a=Math.sin(t),o=i/e*t,h=Math.cos(o);r.x=s*(2+h)*.5*n,r.y=s*(2+h)*a*.5,r.z=s*Math.sin(o)*.5}this.setIndex(a),this.setAttribute("position",new Mn(o,3)),this.setAttribute("normal",new Mn(h,3)),this.setAttribute("uv",new Mn(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Bl(t.radius,t.tube,t.tubularSegments,t.radialSegments,t.p,t.q)}}class kl extends Bn{constructor(t=new Fh(new Qi(-1,-1,0),new Qi(-1,1,0),new Qi(1,1,0)),e=64,i=1,s=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:i,radialSegments:s,closed:r};const n=t.computeFrenetFrames(e,r);this.tangents=n.tangents,this.normals=n.normals,this.binormals=n.binormals;const a=new Qi,o=new Qi,h=new Gi;let l=new Qi;const c=[],u=[],d=[],p=[];function m(r){l=t.getPointAt(r/e,l);const h=n.normals[r],d=n.binormals[r];for(let t=0;t<=s;t++){const e=t/s*Math.PI*2,r=Math.sin(e),n=-Math.cos(e);o.x=n*h.x+r*d.x,o.y=n*h.y+r*d.y,o.z=n*h.z+r*d.z,o.normalize(),u.push(o.x,o.y,o.z),a.x=l.x+i*o.x,a.y=l.y+i*o.y,a.z=l.z+i*o.z,c.push(a.x,a.y,a.z)}}!function(){for(let t=0;t<e;t++)m(t);m(!1===r?e:0),function(){for(let t=0;t<=e;t++)for(let i=0;i<=s;i++)h.x=t/e,h.y=i/s,d.push(h.x,h.y)}(),function(){for(let t=1;t<=e;t++)for(let e=1;e<=s;e++){const i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,a=(s+1)*(t-1)+e;p.push(i,r,a),p.push(r,n,a)}}()}(),this.setIndex(p),this.setAttribute("position",new Mn(c,3)),this.setAttribute("normal",new Mn(u,3)),this.setAttribute("uv",new Mn(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return t.path=this.parameters.path.toJSON(),t}static fromJSON(t){return new kl((new Lh[t.path.type]).fromJSON(t.path),t.tubularSegments,t.radius,t.radialSegments,t.closed)}}class El extends Bn{constructor(t=null){if(super(),this.type="WireframeGeometry",this.parameters={geometry:t},null!==t){const e=[],i=new Set,s=new Qi,r=new Qi;if(null!==t.index){const n=t.attributes.position,a=t.index;let o=t.groups;0===o.length&&(o=[{start:0,count:a.count,materialIndex:0}]);for(let t=0,h=o.length;t<h;++t){const h=o[t],l=h.start;for(let t=l,o=l+h.count;t<o;t+=3)for(let o=0;o<3;o++){const h=a.getX(t+o),l=a.getX(t+(o+1)%3);s.fromBufferAttribute(n,h),r.fromBufferAttribute(n,l),!0===Rl(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}}else{const n=t.attributes.position;for(let t=0,a=n.count/3;t<a;t++)for(let a=0;a<3;a++){const o=3*t+a,h=3*t+(a+1)%3;s.fromBufferAttribute(n,o),r.fromBufferAttribute(n,h),!0===Rl(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}this.setAttribute("position",new Mn(e,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}function Rl(t,e,i){const s=`${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`,r=`${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`;return!0!==i.has(s)&&!0!==i.has(r)&&(i.add(s),i.add(r),!0)}var Pl=Object.freeze({__proto__:null,BoxGeometry:Hn,CapsuleGeometry:lh,CircleGeometry:ch,ConeGeometry:dh,CylinderGeometry:uh,DodecahedronGeometry:mh,EdgesGeometry:bh,ExtrudeGeometry:bl,IcosahedronGeometry:wl,LatheGeometry:Ml,OctahedronGeometry:Sl,PlaneGeometry:_l,PolyhedronGeometry:ph,RingGeometry:Al,ShapeGeometry:Tl,SphereGeometry:zl,TetrahedronGeometry:Il,TorusGeometry:Cl,TorusKnotGeometry:Bl,TubeGeometry:kl,WireframeGeometry:El});class Ol extends sn{constructor(t){super(),this.isShadowMaterial=!0,this.type="ShadowMaterial",this.color=new Kr(0),this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.fog=t.fog,this}}class Nl extends Zn{constructor(t){super(t),this.isRawShaderMaterial=!0,this.type="RawShaderMaterial"}}class Fl extends sn{constructor(t){super(),this.isMeshStandardMaterial=!0,this.type="MeshStandardMaterial",this.defines={STANDARD:""},this.color=new Kr(16777215),this.roughness=1,this.metalness=0,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new Kr(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.roughnessMap=null,this.metalnessMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new fr,this.envMapIntensity=1,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.envMapIntensity=t.envMapIntensity,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class Vl extends Fl{constructor(t){super(),this.isMeshPhysicalMaterial=!0,this.defines={STANDARD:"",PHYSICAL:""},this.type="MeshPhysicalMaterial",this.anisotropyRotation=0,this.anisotropyMap=null,this.clearcoatMap=null,this.clearcoatRoughness=0,this.clearcoatRoughnessMap=null,this.clearcoatNormalScale=new Gi(1,1),this.clearcoatNormalMap=null,this.ior=1.5,Object.defineProperty(this,"reflectivity",{get:function(){return Hi(2.5*(this.ior-1)/(this.ior+1),0,1)},set:function(t){this.ior=(1+.4*t)/(1-.4*t)}}),this.iridescenceMap=null,this.iridescenceIOR=1.3,this.iridescenceThicknessRange=[100,400],this.iridescenceThicknessMap=null,this.sheenColor=new Kr(0),this.sheenColorMap=null,this.sheenRoughness=1,this.sheenRoughnessMap=null,this.transmissionMap=null,this.thickness=0,this.thicknessMap=null,this.attenuationDistance=1/0,this.attenuationColor=new Kr(1,1,1),this.specularIntensity=1,this.specularIntensityMap=null,this.specularColor=new Kr(1,1,1),this.specularColorMap=null,this._anisotropy=0,this._clearcoat=0,this._dispersion=0,this._iridescence=0,this._sheen=0,this._transmission=0,this.setValues(t)}get anisotropy(){return this._anisotropy}set anisotropy(t){this._anisotropy>0!=t>0&&this.version++,this._anisotropy=t}get clearcoat(){return this._clearcoat}set clearcoat(t){this._clearcoat>0!=t>0&&this.version++,this._clearcoat=t}get iridescence(){return this._iridescence}set iridescence(t){this._iridescence>0!=t>0&&this.version++,this._iridescence=t}get dispersion(){return this._dispersion}set dispersion(t){this._dispersion>0!=t>0&&this.version++,this._dispersion=t}get sheen(){return this._sheen}set sheen(t){this._sheen>0!=t>0&&this.version++,this._sheen=t}get transmission(){return this._transmission}set transmission(t){this._transmission>0!=t>0&&this.version++,this._transmission=t}copy(t){return super.copy(t),this.defines={STANDARD:"",PHYSICAL:""},this.anisotropy=t.anisotropy,this.anisotropyRotation=t.anisotropyRotation,this.anisotropyMap=t.anisotropyMap,this.clearcoat=t.clearcoat,this.clearcoatMap=t.clearcoatMap,this.clearcoatRoughness=t.clearcoatRoughness,this.clearcoatRoughnessMap=t.clearcoatRoughnessMap,this.clearcoatNormalMap=t.clearcoatNormalMap,this.clearcoatNormalScale.copy(t.clearcoatNormalScale),this.dispersion=t.dispersion,this.ior=t.ior,this.iridescence=t.iridescence,this.iridescenceMap=t.iridescenceMap,this.iridescenceIOR=t.iridescenceIOR,this.iridescenceThicknessRange=[...t.iridescenceThicknessRange],this.iridescenceThicknessMap=t.iridescenceThicknessMap,this.sheen=t.sheen,this.sheenColor.copy(t.sheenColor),this.sheenColorMap=t.sheenColorMap,this.sheenRoughness=t.sheenRoughness,this.sheenRoughnessMap=t.sheenRoughnessMap,this.transmission=t.transmission,this.transmissionMap=t.transmissionMap,this.thickness=t.thickness,this.thicknessMap=t.thicknessMap,this.attenuationDistance=t.attenuationDistance,this.attenuationColor.copy(t.attenuationColor),this.specularIntensity=t.specularIntensity,this.specularIntensityMap=t.specularIntensityMap,this.specularColor.copy(t.specularColor),this.specularColorMap=t.specularColorMap,this}}class Ll extends sn{constructor(t){super(),this.isMeshPhongMaterial=!0,this.type="MeshPhongMaterial",this.color=new Kr(16777215),this.specular=new Kr(1118481),this.shininess=30,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new Kr(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new fr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.specular.copy(t.specular),this.shininess=t.shininess,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class jl extends sn{constructor(t){super(),this.isMeshToonMaterial=!0,this.defines={TOON:""},this.type="MeshToonMaterial",this.color=new Kr(16777215),this.map=null,this.gradientMap=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new Kr(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.gradientMap=t.gradientMap,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}class Wl extends sn{constructor(t){super(),this.isMeshNormalMaterial=!0,this.type="MeshNormalMaterial",this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.flatShading=!1,this.setValues(t)}copy(t){return super.copy(t),this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.flatShading=t.flatShading,this}}class Ul extends sn{constructor(t){super(),this.isMeshLambertMaterial=!0,this.type="MeshLambertMaterial",this.color=new Kr(16777215),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new Kr(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new fr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class Dl extends sn{constructor(t){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=3200,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(t)}copy(t){return super.copy(t),this.depthPacking=t.depthPacking,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this}}class Hl extends sn{constructor(t){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(t)}copy(t){return super.copy(t),this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this}}class ql extends sn{constructor(t){super(),this.isMeshMatcapMaterial=!0,this.defines={MATCAP:""},this.type="MeshMatcapMaterial",this.color=new Kr(16777215),this.matcap=null,this.map=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Gi(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={MATCAP:""},this.color.copy(t.color),this.matcap=t.matcap,this.map=t.map,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.flatShading=t.flatShading,this.fog=t.fog,this}}class Jl extends Po{constructor(t){super(),this.isLineDashedMaterial=!0,this.type="LineDashedMaterial",this.scale=1,this.dashSize=3,this.gapSize=1,this.setValues(t)}copy(t){return super.copy(t),this.scale=t.scale,this.dashSize=t.dashSize,this.gapSize=t.gapSize,this}}function Xl(t,e){return t&&t.constructor!==e?"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t):t}function Yl(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function Zl(t){const e=t.length,i=new Array(e);for(let t=0;t!==e;++t)i[t]=t;return i.sort(function(e,i){return t[e]-t[i]}),i}function Gl(t,e,i){const s=t.length,r=new t.constructor(s);for(let n=0,a=0;a!==s;++n){const s=i[n]*e;for(let i=0;i!==e;++i)r[a++]=t[s+i]}return r}function $l(t,e,i,s){let r=1,n=t[0];for(;void 0!==n&&void 0===n[s];)n=t[r++];if(void 0===n)return;let a=n[s];if(void 0!==a)if(Array.isArray(a))do{a=n[s],void 0!==a&&(e.push(n.time),i.push(...a)),n=t[r++]}while(void 0!==n);else if(void 0!==a.toArray)do{a=n[s],void 0!==a&&(e.push(n.time),a.toArray(i,i.length)),n=t[r++]}while(void 0!==n);else do{a=n[s],void 0!==a&&(e.push(n.time),i.push(a)),n=t[r++]}while(void 0!==n)}class Ql{static convertArray(t,e){return Xl(t,e)}static isTypedArray(t){return Yl(t)}static getKeyframeOrder(t){return Zl(t)}static sortedArray(t,e,i){return Gl(t,e,i)}static flattenJSON(t,e,i,s){$l(t,e,i,s)}static subclip(t,e,i,s,r=30){return function(t,e,i,s,r=30){const n=t.clone();n.name=e;const a=[];for(let t=0;t<n.tracks.length;++t){const e=n.tracks[t],o=e.getValueSize(),h=[],l=[];for(let t=0;t<e.times.length;++t){const n=e.times[t]*r;if(!(n<i||n>=s)){h.push(e.times[t]);for(let i=0;i<o;++i)l.push(e.values[t*o+i])}}0!==h.length&&(e.times=Xl(h,e.times.constructor),e.values=Xl(l,e.values.constructor),a.push(e))}n.tracks=a;let o=1/0;for(let t=0;t<n.tracks.length;++t)o>n.tracks[t].times[0]&&(o=n.tracks[t].times[0]);for(let t=0;t<n.tracks.length;++t)n.tracks[t].shift(-1*o);return n.resetDuration(),n}(t,e,i,s,r)}static makeClipAdditive(t,e=0,i=t,s=30){return function(t,e=0,i=t,s=30){s<=0&&(s=30);const r=i.tracks.length,n=e/s;for(let e=0;e<r;++e){const s=i.tracks[e],r=s.ValueTypeName;if("bool"===r||"string"===r)continue;const a=t.tracks.find(function(t){return t.name===s.name&&t.ValueTypeName===r});if(void 0===a)continue;let o=0;const h=s.getValueSize();s.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(o=h/3);let l=0;const c=a.getValueSize();a.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(l=c/3);const u=s.times.length-1;let d;if(n<=s.times[0]){const t=o,e=h-o;d=s.values.slice(t,e)}else if(n>=s.times[u]){const t=u*h+o,e=t+h-o;d=s.values.slice(t,e)}else{const t=s.createInterpolant(),e=o,i=h-o;t.evaluate(n),d=t.resultBuffer.slice(e,i)}"quaternion"===r&&(new $i).fromArray(d).normalize().conjugate().toArray(d);const p=a.times.length;for(let t=0;t<p;++t){const e=t*c+l;if("quaternion"===r)$i.multiplyQuaternionsFlat(a.values,e,d,0,a.values,e);else{const t=c-2*l;for(let i=0;i<t;++i)a.values[e+i]-=d[i]}}}return t.blendMode=Fe,t}(t,e,i,s)}}class Kl{constructor(t,e,i,s){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==s?s:new e.constructor(i),this.sampleValues=e,this.valueSize=i,this.settings=null,this.DefaultSettings_={}}evaluate(t){const e=this.parameterPositions;let i=this._cachedIndex,s=e[i],r=e[i-1];t:{e:{let n;i:{s:if(!(t<s)){for(let n=i+2;;){if(void 0===s){if(t<r)break s;return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}if(i===n)break;if(r=s,s=e[++i],t<s)break e}n=e.length;break i}if(!(t>=r)){const a=e[1];t<a&&(i=2,r=a);for(let n=i-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(i===n)break;if(s=r,r=e[--i-1],t>=r)break e}n=i,i=0;break i}break t}for(;i<n;){const s=i+n>>>1;t<e[s]?n=s:i=s+1}if(s=e[i],r=e[i-1],void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===s)return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}this._cachedIndex=i,this.intervalChanged_(i,r,s)}return this.interpolate_(i,r,t,s)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(t){const e=this.resultBuffer,i=this.sampleValues,s=this.valueSize,r=t*s;for(let t=0;t!==s;++t)e[t]=i[r+t];return e}interpolate_(){throw new Error("call to abstract method")}intervalChanged_(){}}class tc extends Kl{constructor(t,e,i,s){super(t,e,i,s),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:Re,endingEnd:Re}}intervalChanged_(t,e,i){const s=this.parameterPositions;let r=t-2,n=t+1,a=s[r],o=s[n];if(void 0===a)switch(this.getSettings_().endingStart){case Pe:r=t,a=2*e-i;break;case Oe:r=s.length-2,a=e+s[r]-s[r+1];break;default:r=t,a=i}if(void 0===o)switch(this.getSettings_().endingEnd){case Pe:n=t,o=2*i-e;break;case Oe:n=1,o=i+s[1]-s[0];break;default:n=t-1,o=e}const h=.5*(i-e),l=this.valueSize;this._weightPrev=h/(e-a),this._weightNext=h/(o-i),this._offsetPrev=r*l,this._offsetNext=n*l}interpolate_(t,e,i,s){const r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=this._offsetPrev,c=this._offsetNext,u=this._weightPrev,d=this._weightNext,p=(i-e)/(s-e),m=p*p,y=m*p,g=-u*y+2*u*m-u*p,f=(1+u)*y+(-1.5-2*u)*m+(-.5+u)*p+1,x=(-1-d)*y+(1.5+d)*m+.5*p,b=d*y-d*m;for(let t=0;t!==a;++t)r[t]=g*n[l+t]+f*n[h+t]+x*n[o+t]+b*n[c+t];return r}}class ec extends Kl{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){const r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=(i-e)/(s-e),c=1-l;for(let t=0;t!==a;++t)r[t]=n[h+t]*c+n[o+t]*l;return r}}class ic extends Kl{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t){return this.copySampleValue_(t-1)}}class sc{constructor(t,e,i,s){if(void 0===t)throw new Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw new Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=Xl(e,this.TimeBufferType),this.values=Xl(i,this.ValueBufferType),this.setInterpolation(s||this.DefaultInterpolation)}static toJSON(t){const e=t.constructor;let i;if(e.toJSON!==this.toJSON)i=e.toJSON(t);else{i={name:t.name,times:Xl(t.times,Array),values:Xl(t.values,Array)};const e=t.getInterpolation();e!==t.DefaultInterpolation&&(i.interpolation=e)}return i.type=t.ValueTypeName,i}InterpolantFactoryMethodDiscrete(t){return new ic(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodLinear(t){return new ec(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodSmooth(t){return new tc(this.times,this.values,this.getValueSize(),t)}setInterpolation(t){let e;switch(t){case Be:e=this.InterpolantFactoryMethodDiscrete;break;case ke:e=this.InterpolantFactoryMethodLinear;break;case Ee:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){const e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant){if(t===this.DefaultInterpolation)throw new Error(e);this.setInterpolation(this.DefaultInterpolation)}return console.warn("THREE.KeyframeTrack:",e),this}return this.createInterpolant=e,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return Be;case this.InterpolantFactoryMethodLinear:return ke;case this.InterpolantFactoryMethodSmooth:return Ee}}getValueSize(){return this.values.length/this.times.length}shift(t){if(0!==t){const e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]+=t}return this}scale(t){if(1!==t){const e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]*=t}return this}trim(t,e){const i=this.times,s=i.length;let r=0,n=s-1;for(;r!==s&&i[r]<t;)++r;for(;-1!==n&&i[n]>e;)--n;if(++n,0!==r||n!==s){r>=n&&(n=Math.max(n,1),r=n-1);const t=this.getValueSize();this.times=i.slice(r,n),this.values=this.values.slice(r*t,n*t)}return this}validate(){let t=!0;const e=this.getValueSize();e-Math.floor(e)!==0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),t=!1);const i=this.times,s=this.values,r=i.length;0===r&&(console.error("THREE.KeyframeTrack: Track is empty.",this),t=!1);let n=null;for(let e=0;e!==r;e++){const s=i[e];if("number"==typeof s&&isNaN(s)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,e,s),t=!1;break}if(null!==n&&n>s){console.error("THREE.KeyframeTrack: Out of order keys.",this,e,s,n),t=!1;break}n=s}if(void 0!==s&&Yl(s))for(let e=0,i=s.length;e!==i;++e){const i=s[e];if(isNaN(i)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,e,i),t=!1;break}}return t}optimize(){const t=this.times.slice(),e=this.values.slice(),i=this.getValueSize(),s=this.getInterpolation()===Ee,r=t.length-1;let n=1;for(let a=1;a<r;++a){let r=!1;const o=t[a];if(o!==t[a+1]&&(1!==a||o!==t[0]))if(s)r=!0;else{const t=a*i,s=t-i,n=t+i;for(let a=0;a!==i;++a){const i=e[t+a];if(i!==e[s+a]||i!==e[n+a]){r=!0;break}}}if(r){if(a!==n){t[n]=t[a];const s=a*i,r=n*i;for(let t=0;t!==i;++t)e[r+t]=e[s+t]}++n}}if(r>0){t[n]=t[r];for(let t=r*i,s=n*i,a=0;a!==i;++a)e[s+a]=e[t+a];++n}return n!==t.length?(this.times=t.slice(0,n),this.values=e.slice(0,n*i)):(this.times=t,this.values=e),this}clone(){const t=this.times.slice(),e=this.values.slice(),i=new(0,this.constructor)(this.name,t,e);return i.createInterpolant=this.createInterpolant,i}}sc.prototype.ValueTypeName="",sc.prototype.TimeBufferType=Float32Array,sc.prototype.ValueBufferType=Float32Array,sc.prototype.DefaultInterpolation=ke;class rc extends sc{constructor(t,e,i){super(t,e,i)}}rc.prototype.ValueTypeName="bool",rc.prototype.ValueBufferType=Array,rc.prototype.DefaultInterpolation=Be,rc.prototype.InterpolantFactoryMethodLinear=void 0,rc.prototype.InterpolantFactoryMethodSmooth=void 0;class nc extends sc{constructor(t,e,i,s){super(t,e,i,s)}}nc.prototype.ValueTypeName="color";class ac extends sc{constructor(t,e,i,s){super(t,e,i,s)}}ac.prototype.ValueTypeName="number";class oc extends Kl{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){const r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=(i-e)/(s-e);let h=t*a;for(let t=h+a;h!==t;h+=4)$i.slerpFlat(r,0,n,h-a,n,h,o);return r}}class hc extends sc{constructor(t,e,i,s){super(t,e,i,s)}InterpolantFactoryMethodLinear(t){return new oc(this.times,this.values,this.getValueSize(),t)}}hc.prototype.ValueTypeName="quaternion",hc.prototype.InterpolantFactoryMethodSmooth=void 0;class lc extends sc{constructor(t,e,i){super(t,e,i)}}lc.prototype.ValueTypeName="string",lc.prototype.ValueBufferType=Array,lc.prototype.DefaultInterpolation=Be,lc.prototype.InterpolantFactoryMethodLinear=void 0,lc.prototype.InterpolantFactoryMethodSmooth=void 0;class cc extends sc{constructor(t,e,i,s){super(t,e,i,s)}}cc.prototype.ValueTypeName="vector";class uc{constructor(t="",e=-1,i=[],s=2500){this.name=t,this.tracks=i,this.duration=e,this.blendMode=s,this.uuid=Di(),this.duration<0&&this.resetDuration()}static parse(t){const e=[],i=t.tracks,s=1/(t.fps||1);for(let t=0,r=i.length;t!==r;++t)e.push(dc(i[t]).scale(s));const r=new this(t.name,t.duration,e,t.blendMode);return r.uuid=t.uuid,r}static toJSON(t){const e=[],i=t.tracks,s={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid,blendMode:t.blendMode};for(let t=0,s=i.length;t!==s;++t)e.push(sc.toJSON(i[t]));return s}static CreateFromMorphTargetSequence(t,e,i,s){const r=e.length,n=[];for(let t=0;t<r;t++){let a=[],o=[];a.push((t+r-1)%r,t,(t+1)%r),o.push(0,1,0);const h=Zl(a);a=Gl(a,1,h),o=Gl(o,1,h),s||0!==a[0]||(a.push(r),o.push(o[0])),n.push(new ac(".morphTargetInfluences["+e[t].name+"]",a,o).scale(1/i))}return new this(t,-1,n)}static findByName(t,e){let i=t;if(!Array.isArray(t)){const e=t;i=e.geometry&&e.geometry.animations||e.animations}for(let t=0;t<i.length;t++)if(i[t].name===e)return i[t];return null}static CreateClipsFromMorphTargetSequences(t,e,i){const s={},r=/^([\w-]*?)([\d]+)$/;for(let e=0,i=t.length;e<i;e++){const i=t[e],n=i.name.match(r);if(n&&n.length>1){const t=n[1];let e=s[t];e||(s[t]=e=[]),e.push(i)}}const n=[];for(const t in s)n.push(this.CreateFromMorphTargetSequence(t,s[t],e,i));return n}static parseAnimation(t,e){if(console.warn("THREE.AnimationClip: parseAnimation() is deprecated and will be removed with r185"),!t)return console.error("THREE.AnimationClip: No animation in JSONLoader data."),null;const i=function(t,e,i,s,r){if(0!==i.length){const n=[],a=[];$l(i,n,a,s),0!==n.length&&r.push(new t(e,n,a))}},s=[],r=t.name||"default",n=t.fps||30,a=t.blendMode;let o=t.length||-1;const h=t.hierarchy||[];for(let t=0;t<h.length;t++){const r=h[t].keys;if(r&&0!==r.length)if(r[0].morphTargets){const t={};let e;for(e=0;e<r.length;e++)if(r[e].morphTargets)for(let i=0;i<r[e].morphTargets.length;i++)t[r[e].morphTargets[i]]=-1;for(const i in t){const t=[],n=[];for(let s=0;s!==r[e].morphTargets.length;++s){const s=r[e];t.push(s.time),n.push(s.morphTarget===i?1:0)}s.push(new ac(".morphTargetInfluence["+i+"]",t,n))}o=t.length*n}else{const n=".bones["+e[t].name+"]";i(cc,n+".position",r,"pos",s),i(hc,n+".quaternion",r,"rot",s),i(cc,n+".scale",r,"scl",s)}}if(0===s.length)return null;return new this(r,o,s,a)}resetDuration(){let t=0;for(let e=0,i=this.tracks.length;e!==i;++e){const i=this.tracks[e];t=Math.max(t,i.times[i.times.length-1])}return this.duration=t,this}trim(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this}validate(){let t=!0;for(let e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t}optimize(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}clone(){const t=[];for(let e=0;e<this.tracks.length;e++)t.push(this.tracks[e].clone());return new this.constructor(this.name,this.duration,t,this.blendMode)}toJSON(){return this.constructor.toJSON(this)}}function dc(t){if(void 0===t.type)throw new Error("THREE.KeyframeTrack: track type undefined, can not parse");const e=function(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return ac;case"vector":case"vector2":case"vector3":case"vector4":return cc;case"color":return nc;case"quaternion":return hc;case"bool":case"boolean":return rc;case"string":return lc}throw new Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}(t.type);if(void 0===t.times){const e=[],i=[];$l(t.keys,e,i,"value"),t.times=e,t.values=i}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)}const pc={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}};class mc{constructor(t,e,i){const s=this;let r,n=!1,a=0,o=0;const h=[];this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=i,this.itemStart=function(t){o++,!1===n&&void 0!==s.onStart&&s.onStart(t,a,o),n=!0},this.itemEnd=function(t){a++,void 0!==s.onProgress&&s.onProgress(t,a,o),a===o&&(n=!1,void 0!==s.onLoad&&s.onLoad())},this.itemError=function(t){void 0!==s.onError&&s.onError(t)},this.resolveURL=function(t){return r?r(t):t},this.setURLModifier=function(t){return r=t,this},this.addHandler=function(t,e){return h.push(t,e),this},this.removeHandler=function(t){const e=h.indexOf(t);return-1!==e&&h.splice(e,2),this},this.getHandler=function(t){for(let e=0,i=h.length;e<i;e+=2){const i=h[e],s=h[e+1];if(i.global&&(i.lastIndex=0),i.test(t))return s}return null}}}const yc=new mc;class gc{constructor(t){this.manager=void 0!==t?t:yc,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}load(){}loadAsync(t,e){const i=this;return new Promise(function(s,r){i.load(t,s,e,r)})}parse(){}setCrossOrigin(t){return this.crossOrigin=t,this}setWithCredentials(t){return this.withCredentials=t,this}setPath(t){return this.path=t,this}setResourcePath(t){return this.resourcePath=t,this}setRequestHeader(t){return this.requestHeader=t,this}}gc.DEFAULT_MATERIAL_NAME="__DEFAULT";const fc={};class xc extends Error{constructor(t,e){super(t),this.response=e}}class bc extends gc{constructor(t){super(t),this.mimeType="",this.responseType=""}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=pc.get(`file:${t}`);if(void 0!==r)return this.manager.itemStart(t),setTimeout(()=>{e&&e(r),this.manager.itemEnd(t)},0),r;if(void 0!==fc[t])return void fc[t].push({onLoad:e,onProgress:i,onError:s});fc[t]=[],fc[t].push({onLoad:e,onProgress:i,onError:s});const n=new Request(t,{headers:new Headers(this.requestHeader),credentials:this.withCredentials?"include":"same-origin"}),a=this.mimeType,o=this.responseType;fetch(n).then(e=>{if(200===e.status||0===e.status){if(0===e.status&&console.warn("THREE.FileLoader: HTTP Status 0 received."),"undefined"==typeof ReadableStream||void 0===e.body||void 0===e.body.getReader)return e;const i=fc[t],s=e.body.getReader(),r=e.headers.get("X-File-Size")||e.headers.get("Content-Length"),n=r?parseInt(r):0,a=0!==n;let o=0;const h=new ReadableStream({start(t){!function e(){s.read().then(({done:s,value:r})=>{if(s)t.close();else{o+=r.byteLength;const s=new ProgressEvent("progress",{lengthComputable:a,loaded:o,total:n});for(let t=0,e=i.length;t<e;t++){const e=i[t];e.onProgress&&e.onProgress(s)}t.enqueue(r),e()}},e=>{t.error(e)})}()}});return new Response(h)}throw new xc(`fetch for "${e.url}" responded with ${e.status}: ${e.statusText}`,e)}).then(t=>{switch(o){case"arraybuffer":return t.arrayBuffer();case"blob":return t.blob();case"document":return t.text().then(t=>(new DOMParser).parseFromString(t,a));case"json":return t.json();default:if(""===a)return t.text();{const e=/charset="?([^;"\s]*)"?/i.exec(a),i=e&&e[1]?e[1].toLowerCase():void 0,s=new TextDecoder(i);return t.arrayBuffer().then(t=>s.decode(t))}}}).then(e=>{pc.add(`file:${t}`,e);const i=fc[t];delete fc[t];for(let t=0,s=i.length;t<s;t++){const s=i[t];s.onLoad&&s.onLoad(e)}}).catch(e=>{const i=fc[t];if(void 0===i)throw this.manager.itemError(t),e;delete fc[t];for(let t=0,s=i.length;t<s;t++){const s=i[t];s.onError&&s.onError(e)}this.manager.itemError(t)}).finally(()=>{this.manager.itemEnd(t)}),this.manager.itemStart(t)}setResponseType(t){return this.responseType=t,this}setMimeType(t){return this.mimeType=t,this}}class vc extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=new bc(this.manager);n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){const e=[];for(let i=0;i<t.length;i++){const s=uc.parse(t[i]);e.push(s)}return e}}class wc extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=[],a=new rh,o=new bc(this.manager);o.setPath(this.path),o.setResponseType("arraybuffer"),o.setRequestHeader(this.requestHeader),o.setWithCredentials(r.withCredentials);let h=0;function l(l){o.load(t[l],function(t){const i=r.parse(t,!0);n[l]={width:i.width,height:i.height,format:i.format,mipmaps:i.mipmaps},h+=1,6===h&&(1===i.mipmapCount&&(a.minFilter=wt),a.image=n,a.format=i.format,a.needsUpdate=!0,e&&e(a))},i,s)}if(Array.isArray(t))for(let e=0,i=t.length;e<i;++e)l(e);else o.load(t,function(t){const i=r.parse(t,!0);if(i.isCubemap){const t=i.mipmaps.length/i.mipmapCount;for(let e=0;e<t;e++){n[e]={mipmaps:[]};for(let t=0;t<i.mipmapCount;t++)n[e].mipmaps.push(i.mipmaps[e*i.mipmapCount+t]),n[e].format=i.format,n[e].width=i.width,n[e].height=i.height}a.image=n}else a.image.width=i.width,a.image.height=i.height,a.mipmaps=i.mipmaps;1===i.mipmapCount&&(a.minFilter=wt),a.format=i.format,a.needsUpdate=!0,e&&e(a)},i,s);return a}}const Mc=new WeakMap;class Sc extends gc{constructor(t){super(t)}load(t,e,i,s){void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=this,n=pc.get(`image:${t}`);if(void 0!==n){if(!0===n.complete)r.manager.itemStart(t),setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0);else{let t=Mc.get(n);void 0===t&&(t=[],Mc.set(n,t)),t.push({onLoad:e,onError:s})}return n}const a=as("img");function o(){l(),e&&e(this);const i=Mc.get(this)||[];for(let t=0;t<i.length;t++){const e=i[t];e.onLoad&&e.onLoad(this)}Mc.delete(this),r.manager.itemEnd(t)}function h(e){l(),s&&s(e),pc.remove(`image:${t}`);const i=Mc.get(this)||[];for(let t=0;t<i.length;t++){const s=i[t];s.onError&&s.onError(e)}Mc.delete(this),r.manager.itemError(t),r.manager.itemEnd(t)}function l(){a.removeEventListener("load",o,!1),a.removeEventListener("error",h,!1)}return a.addEventListener("load",o,!1),a.addEventListener("error",h,!1),"data:"!==t.slice(0,5)&&void 0!==this.crossOrigin&&(a.crossOrigin=this.crossOrigin),pc.add(`image:${t}`,a),r.manager.itemStart(t),a.src=t,a}}class _c extends gc{constructor(t){super(t)}load(t,e,i,s){const r=new sa;r.colorSpace=Ye;const n=new Sc(this.manager);n.setCrossOrigin(this.crossOrigin),n.setPath(this.path);let a=0;function o(i){n.load(t[i],function(t){r.images[i]=t,a++,6===a&&(r.needsUpdate=!0,e&&e(r))},void 0,s)}for(let e=0;e<t.length;++e)o(e);return r}}class Ac extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=new Ha,a=new bc(this.manager);return a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setPath(this.path),a.setWithCredentials(r.withCredentials),a.load(t,function(t){let i;try{i=r.parse(t)}catch(t){if(void 0===s)return void console.error(t);s(t)}void 0!==i.image?n.image=i.image:void 0!==i.data&&(n.image.width=i.width,n.image.height=i.height,n.image.data=i.data),n.wrapS=void 0!==i.wrapS?i.wrapS:mt,n.wrapT=void 0!==i.wrapT?i.wrapT:mt,n.magFilter=void 0!==i.magFilter?i.magFilter:wt,n.minFilter=void 0!==i.minFilter?i.minFilter:wt,n.anisotropy=void 0!==i.anisotropy?i.anisotropy:1,void 0!==i.colorSpace&&(n.colorSpace=i.colorSpace),void 0!==i.flipY&&(n.flipY=i.flipY),void 0!==i.format&&(n.format=i.format),void 0!==i.type&&(n.type=i.type),void 0!==i.mipmaps&&(n.mipmaps=i.mipmaps,n.minFilter=_t),1===i.mipmapCount&&(n.minFilter=wt),void 0!==i.generateMipmaps&&(n.generateMipmaps=i.generateMipmaps),n.needsUpdate=!0,e&&e(n,i)},i,s),n}}class Tc extends gc{constructor(t){super(t)}load(t,e,i,s){const r=new Ts,n=new Sc(this.manager);return n.setCrossOrigin(this.crossOrigin),n.setPath(this.path),n.load(t,function(t){r.image=t,r.needsUpdate=!0,void 0!==e&&e(r)},i,s),r}}class zc extends Pr{constructor(t,e=1){super(),this.isLight=!0,this.type="Light",this.color=new Kr(t),this.intensity=e}dispose(){}copy(t,e){return super.copy(t,e),this.color.copy(t.color),this.intensity=t.intensity,this}toJSON(t){const e=super.toJSON(t);return e.object.color=this.color.getHex(),e.object.intensity=this.intensity,void 0!==this.groundColor&&(e.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(e.object.distance=this.distance),void 0!==this.angle&&(e.object.angle=this.angle),void 0!==this.decay&&(e.object.decay=this.decay),void 0!==this.penumbra&&(e.object.penumbra=this.penumbra),void 0!==this.shadow&&(e.object.shadow=this.shadow.toJSON()),void 0!==this.target&&(e.object.target=this.target.uuid),e}}class Ic extends zc{constructor(t,e,i){super(t,i),this.isHemisphereLight=!0,this.type="HemisphereLight",this.position.copy(Pr.DEFAULT_UP),this.updateMatrix(),this.groundColor=new Kr(e)}copy(t,e){return super.copy(t,e),this.groundColor.copy(t.groundColor),this}}const Cc=new or,Bc=new Qi,kc=new Qi;class Ec{constructor(t){this.camera=t,this.intensity=1,this.bias=0,this.normalBias=0,this.radius=1,this.blurSamples=8,this.mapSize=new Gi(512,512),this.mapType=Tt,this.map=null,this.mapPass=null,this.matrix=new or,this.autoUpdate=!0,this.needsUpdate=!1,this._frustum=new co,this._frameExtents=new Gi(1,1),this._viewportCount=1,this._viewports=[new zs(0,0,1,1)]}getViewportCount(){return this._viewportCount}getFrustum(){return this._frustum}updateMatrices(t){const e=this.camera,i=this.matrix;Bc.setFromMatrixPosition(t.matrixWorld),e.position.copy(Bc),kc.setFromMatrixPosition(t.target.matrixWorld),e.lookAt(kc),e.updateMatrixWorld(),Cc.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),this._frustum.setFromProjectionMatrix(Cc),i.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),i.multiply(Cc)}getViewport(t){return this._viewports[t]}getFrameExtents(){return this._frameExtents}dispose(){this.map&&this.map.dispose(),this.mapPass&&this.mapPass.dispose()}copy(t){return this.camera=t.camera.clone(),this.intensity=t.intensity,this.bias=t.bias,this.radius=t.radius,this.autoUpdate=t.autoUpdate,this.needsUpdate=t.needsUpdate,this.normalBias=t.normalBias,this.blurSamples=t.blurSamples,this.mapSize.copy(t.mapSize),this}clone(){return(new this.constructor).copy(this)}toJSON(){const t={};return 1!==this.intensity&&(t.intensity=this.intensity),0!==this.bias&&(t.bias=this.bias),0!==this.normalBias&&(t.normalBias=this.normalBias),1!==this.radius&&(t.radius=this.radius),512===this.mapSize.x&&512===this.mapSize.y||(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}}class Rc extends Ec{constructor(){super(new ta(50,1,.5,500)),this.isSpotLightShadow=!0,this.focus=1,this.aspect=1}updateMatrices(t){const e=this.camera,i=2*Ui*t.angle*this.focus,s=this.mapSize.width/this.mapSize.height*this.aspect,r=t.distance||e.far;i===e.fov&&s===e.aspect&&r===e.far||(e.fov=i,e.aspect=s,e.far=r,e.updateProjectionMatrix()),super.updateMatrices(t)}copy(t){return super.copy(t),this.focus=t.focus,this}}class Pc extends zc{constructor(t,e,i=0,s=Math.PI/3,r=0,n=2){super(t,e),this.isSpotLight=!0,this.type="SpotLight",this.position.copy(Pr.DEFAULT_UP),this.updateMatrix(),this.target=new Pr,this.distance=i,this.angle=s,this.penumbra=r,this.decay=n,this.map=null,this.shadow=new Rc}get power(){return this.intensity*Math.PI}set power(t){this.intensity=t/Math.PI}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}const Oc=new or,Nc=new Qi,Fc=new Qi;class Vc extends Ec{constructor(){super(new ta(90,1,.5,500)),this.isPointLightShadow=!0,this._frameExtents=new Gi(4,2),this._viewportCount=6,this._viewports=[new zs(2,1,1,1),new zs(0,1,1,1),new zs(3,1,1,1),new zs(1,1,1,1),new zs(3,0,1,1),new zs(1,0,1,1)],this._cubeDirections=[new Qi(1,0,0),new Qi(-1,0,0),new Qi(0,0,1),new Qi(0,0,-1),new Qi(0,1,0),new Qi(0,-1,0)],this._cubeUps=[new Qi(0,1,0),new Qi(0,1,0),new Qi(0,1,0),new Qi(0,1,0),new Qi(0,0,1),new Qi(0,0,-1)]}updateMatrices(t,e=0){const i=this.camera,s=this.matrix,r=t.distance||i.far;r!==i.far&&(i.far=r,i.updateProjectionMatrix()),Nc.setFromMatrixPosition(t.matrixWorld),i.position.copy(Nc),Fc.copy(i.position),Fc.add(this._cubeDirections[e]),i.up.copy(this._cubeUps[e]),i.lookAt(Fc),i.updateMatrixWorld(),s.makeTranslation(-Nc.x,-Nc.y,-Nc.z),Oc.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse),this._frustum.setFromProjectionMatrix(Oc)}}class Lc extends zc{constructor(t,e,i=0,s=2){super(t,e),this.isPointLight=!0,this.type="PointLight",this.distance=i,this.decay=s,this.shadow=new Vc}get power(){return 4*this.intensity*Math.PI}set power(t){this.intensity=t/(4*Math.PI)}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}}class jc extends Gn{constructor(t=-1,e=1,i=1,s=-1,r=.1,n=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=i,this.bottom=s,this.near=r,this.far=n,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this}setViewOffset(t,e,i,s,r,n){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){const t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),i=(this.right+this.left)/2,s=(this.top+this.bottom)/2;let r=i-t,n=i+t,a=s+e,o=s-e;if(null!==this.view&&this.view.enabled){const t=(this.right-this.left)/this.view.fullWidth/this.zoom,e=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=t*this.view.offsetX,n=r+t*this.view.width,a-=e*this.view.offsetY,o=a-e*this.view.height}this.projectionMatrix.makeOrthographic(r,n,a,o,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){const e=super.toJSON(t);return e.object.zoom=this.zoom,e.object.left=this.left,e.object.right=this.right,e.object.top=this.top,e.object.bottom=this.bottom,e.object.near=this.near,e.object.far=this.far,null!==this.view&&(e.object.view=Object.assign({},this.view)),e}}class Wc extends Ec{constructor(){super(new jc(-5,5,5,-5,.5,500)),this.isDirectionalLightShadow=!0}}class Uc extends zc{constructor(t,e){super(t,e),this.isDirectionalLight=!0,this.type="DirectionalLight",this.position.copy(Pr.DEFAULT_UP),this.updateMatrix(),this.target=new Pr,this.shadow=new Wc}dispose(){this.shadow.dispose()}copy(t){return super.copy(t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}class Dc extends zc{constructor(t,e){super(t,e),this.isAmbientLight=!0,this.type="AmbientLight"}}class Hc extends zc{constructor(t,e,i=10,s=10){super(t,e),this.isRectAreaLight=!0,this.type="RectAreaLight",this.width=i,this.height=s}get power(){return this.intensity*this.width*this.height*Math.PI}set power(t){this.intensity=t/(this.width*this.height*Math.PI)}copy(t){return super.copy(t),this.width=t.width,this.height=t.height,this}toJSON(t){const e=super.toJSON(t);return e.object.width=this.width,e.object.height=this.height,e}}class qc{constructor(){this.isSphericalHarmonics3=!0,this.coefficients=[];for(let t=0;t<9;t++)this.coefficients.push(new Qi)}set(t){for(let e=0;e<9;e++)this.coefficients[e].copy(t[e]);return this}zero(){for(let t=0;t<9;t++)this.coefficients[t].set(0,0,0);return this}getAt(t,e){const i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.282095),e.addScaledVector(n[1],.488603*s),e.addScaledVector(n[2],.488603*r),e.addScaledVector(n[3],.488603*i),e.addScaledVector(n[4],i*s*1.092548),e.addScaledVector(n[5],s*r*1.092548),e.addScaledVector(n[6],.315392*(3*r*r-1)),e.addScaledVector(n[7],i*r*1.092548),e.addScaledVector(n[8],.546274*(i*i-s*s)),e}getIrradianceAt(t,e){const i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.886227),e.addScaledVector(n[1],1.023328*s),e.addScaledVector(n[2],1.023328*r),e.addScaledVector(n[3],1.023328*i),e.addScaledVector(n[4],.858086*i*s),e.addScaledVector(n[5],.858086*s*r),e.addScaledVector(n[6],.743125*r*r-.247708),e.addScaledVector(n[7],.858086*i*r),e.addScaledVector(n[8],.429043*(i*i-s*s)),e}add(t){for(let e=0;e<9;e++)this.coefficients[e].add(t.coefficients[e]);return this}addScaledSH(t,e){for(let i=0;i<9;i++)this.coefficients[i].addScaledVector(t.coefficients[i],e);return this}scale(t){for(let e=0;e<9;e++)this.coefficients[e].multiplyScalar(t);return this}lerp(t,e){for(let i=0;i<9;i++)this.coefficients[i].lerp(t.coefficients[i],e);return this}equals(t){for(let e=0;e<9;e++)if(!this.coefficients[e].equals(t.coefficients[e]))return!1;return!0}copy(t){return this.set(t.coefficients)}clone(){return(new this.constructor).copy(this)}fromArray(t,e=0){const i=this.coefficients;for(let s=0;s<9;s++)i[s].fromArray(t,e+3*s);return this}toArray(t=[],e=0){const i=this.coefficients;for(let s=0;s<9;s++)i[s].toArray(t,e+3*s);return t}static getBasisAt(t,e){const i=t.x,s=t.y,r=t.z;e[0]=.282095,e[1]=.488603*s,e[2]=.488603*r,e[3]=.488603*i,e[4]=1.092548*i*s,e[5]=1.092548*s*r,e[6]=.315392*(3*r*r-1),e[7]=1.092548*i*r,e[8]=.546274*(i*i-s*s)}}class Jc extends zc{constructor(t=new qc,e=1){super(void 0,e),this.isLightProbe=!0,this.sh=t}copy(t){return super.copy(t),this.sh.copy(t.sh),this}fromJSON(t){return this.intensity=t.intensity,this.sh.fromArray(t.sh),this}toJSON(t){const e=super.toJSON(t);return e.object.sh=this.sh.toArray(),e}}class Xc extends gc{constructor(t){super(t),this.textures={}}load(t,e,i,s){const r=this,n=new bc(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){const e=this.textures;function i(t){return void 0===e[t]&&console.warn("THREE.MaterialLoader: Undefined texture",t),e[t]}const s=this.createMaterialFromType(t.type);if(void 0!==t.uuid&&(s.uuid=t.uuid),void 0!==t.name&&(s.name=t.name),void 0!==t.color&&void 0!==s.color&&s.color.setHex(t.color),void 0!==t.roughness&&(s.roughness=t.roughness),void 0!==t.metalness&&(s.metalness=t.metalness),void 0!==t.sheen&&(s.sheen=t.sheen),void 0!==t.sheenColor&&(s.sheenColor=(new Kr).setHex(t.sheenColor)),void 0!==t.sheenRoughness&&(s.sheenRoughness=t.sheenRoughness),void 0!==t.emissive&&void 0!==s.emissive&&s.emissive.setHex(t.emissive),void 0!==t.specular&&void 0!==s.specular&&s.specular.setHex(t.specular),void 0!==t.specularIntensity&&(s.specularIntensity=t.specularIntensity),void 0!==t.specularColor&&void 0!==s.specularColor&&s.specularColor.setHex(t.specularColor),void 0!==t.shininess&&(s.shininess=t.shininess),void 0!==t.clearcoat&&(s.clearcoat=t.clearcoat),void 0!==t.clearcoatRoughness&&(s.clearcoatRoughness=t.clearcoatRoughness),void 0!==t.dispersion&&(s.dispersion=t.dispersion),void 0!==t.iridescence&&(s.iridescence=t.iridescence),void 0!==t.iridescenceIOR&&(s.iridescenceIOR=t.iridescenceIOR),void 0!==t.iridescenceThicknessRange&&(s.iridescenceThicknessRange=t.iridescenceThicknessRange),void 0!==t.transmission&&(s.transmission=t.transmission),void 0!==t.thickness&&(s.thickness=t.thickness),void 0!==t.attenuationDistance&&(s.attenuationDistance=t.attenuationDistance),void 0!==t.attenuationColor&&void 0!==s.attenuationColor&&s.attenuationColor.setHex(t.attenuationColor),void 0!==t.anisotropy&&(s.anisotropy=t.anisotropy),void 0!==t.anisotropyRotation&&(s.anisotropyRotation=t.anisotropyRotation),void 0!==t.fog&&(s.fog=t.fog),void 0!==t.flatShading&&(s.flatShading=t.flatShading),void 0!==t.blending&&(s.blending=t.blending),void 0!==t.combine&&(s.combine=t.combine),void 0!==t.side&&(s.side=t.side),void 0!==t.shadowSide&&(s.shadowSide=t.shadowSide),void 0!==t.opacity&&(s.opacity=t.opacity),void 0!==t.transparent&&(s.transparent=t.transparent),void 0!==t.alphaTest&&(s.alphaTest=t.alphaTest),void 0!==t.alphaHash&&(s.alphaHash=t.alphaHash),void 0!==t.depthFunc&&(s.depthFunc=t.depthFunc),void 0!==t.depthTest&&(s.depthTest=t.depthTest),void 0!==t.depthWrite&&(s.depthWrite=t.depthWrite),void 0!==t.colorWrite&&(s.colorWrite=t.colorWrite),void 0!==t.blendSrc&&(s.blendSrc=t.blendSrc),void 0!==t.blendDst&&(s.blendDst=t.blendDst),void 0!==t.blendEquation&&(s.blendEquation=t.blendEquation),void 0!==t.blendSrcAlpha&&(s.blendSrcAlpha=t.blendSrcAlpha),void 0!==t.blendDstAlpha&&(s.blendDstAlpha=t.blendDstAlpha),void 0!==t.blendEquationAlpha&&(s.blendEquationAlpha=t.blendEquationAlpha),void 0!==t.blendColor&&void 0!==s.blendColor&&s.blendColor.setHex(t.blendColor),void 0!==t.blendAlpha&&(s.blendAlpha=t.blendAlpha),void 0!==t.stencilWriteMask&&(s.stencilWriteMask=t.stencilWriteMask),void 0!==t.stencilFunc&&(s.stencilFunc=t.stencilFunc),void 0!==t.stencilRef&&(s.stencilRef=t.stencilRef),void 0!==t.stencilFuncMask&&(s.stencilFuncMask=t.stencilFuncMask),void 0!==t.stencilFail&&(s.stencilFail=t.stencilFail),void 0!==t.stencilZFail&&(s.stencilZFail=t.stencilZFail),void 0!==t.stencilZPass&&(s.stencilZPass=t.stencilZPass),void 0!==t.stencilWrite&&(s.stencilWrite=t.stencilWrite),void 0!==t.wireframe&&(s.wireframe=t.wireframe),void 0!==t.wireframeLinewidth&&(s.wireframeLinewidth=t.wireframeLinewidth),void 0!==t.wireframeLinecap&&(s.wireframeLinecap=t.wireframeLinecap),void 0!==t.wireframeLinejoin&&(s.wireframeLinejoin=t.wireframeLinejoin),void 0!==t.rotation&&(s.rotation=t.rotation),void 0!==t.linewidth&&(s.linewidth=t.linewidth),void 0!==t.dashSize&&(s.dashSize=t.dashSize),void 0!==t.gapSize&&(s.gapSize=t.gapSize),void 0!==t.scale&&(s.scale=t.scale),void 0!==t.polygonOffset&&(s.polygonOffset=t.polygonOffset),void 0!==t.polygonOffsetFactor&&(s.polygonOffsetFactor=t.polygonOffsetFactor),void 0!==t.polygonOffsetUnits&&(s.polygonOffsetUnits=t.polygonOffsetUnits),void 0!==t.dithering&&(s.dithering=t.dithering),void 0!==t.alphaToCoverage&&(s.alphaToCoverage=t.alphaToCoverage),void 0!==t.premultipliedAlpha&&(s.premultipliedAlpha=t.premultipliedAlpha),void 0!==t.forceSinglePass&&(s.forceSinglePass=t.forceSinglePass),void 0!==t.visible&&(s.visible=t.visible),void 0!==t.toneMapped&&(s.toneMapped=t.toneMapped),void 0!==t.userData&&(s.userData=t.userData),void 0!==t.vertexColors&&("number"==typeof t.vertexColors?s.vertexColors=t.vertexColors>0:s.vertexColors=t.vertexColors),void 0!==t.uniforms)for(const e in t.uniforms){const r=t.uniforms[e];switch(s.uniforms[e]={},r.type){case"t":s.uniforms[e].value=i(r.value);break;case"c":s.uniforms[e].value=(new Kr).setHex(r.value);break;case"v2":s.uniforms[e].value=(new Gi).fromArray(r.value);break;case"v3":s.uniforms[e].value=(new Qi).fromArray(r.value);break;case"v4":s.uniforms[e].value=(new zs).fromArray(r.value);break;case"m3":s.uniforms[e].value=(new es).fromArray(r.value);break;case"m4":s.uniforms[e].value=(new or).fromArray(r.value);break;default:s.uniforms[e].value=r.value}}if(void 0!==t.defines&&(s.defines=t.defines),void 0!==t.vertexShader&&(s.vertexShader=t.vertexShader),void 0!==t.fragmentShader&&(s.fragmentShader=t.fragmentShader),void 0!==t.glslVersion&&(s.glslVersion=t.glslVersion),void 0!==t.extensions)for(const e in t.extensions)s.extensions[e]=t.extensions[e];if(void 0!==t.lights&&(s.lights=t.lights),void 0!==t.clipping&&(s.clipping=t.clipping),void 0!==t.size&&(s.size=t.size),void 0!==t.sizeAttenuation&&(s.sizeAttenuation=t.sizeAttenuation),void 0!==t.map&&(s.map=i(t.map)),void 0!==t.matcap&&(s.matcap=i(t.matcap)),void 0!==t.alphaMap&&(s.alphaMap=i(t.alphaMap)),void 0!==t.bumpMap&&(s.bumpMap=i(t.bumpMap)),void 0!==t.bumpScale&&(s.bumpScale=t.bumpScale),void 0!==t.normalMap&&(s.normalMap=i(t.normalMap)),void 0!==t.normalMapType&&(s.normalMapType=t.normalMapType),void 0!==t.normalScale){let e=t.normalScale;!1===Array.isArray(e)&&(e=[e,e]),s.normalScale=(new Gi).fromArray(e)}return void 0!==t.displacementMap&&(s.displacementMap=i(t.displacementMap)),void 0!==t.displacementScale&&(s.displacementScale=t.displacementScale),void 0!==t.displacementBias&&(s.displacementBias=t.displacementBias),void 0!==t.roughnessMap&&(s.roughnessMap=i(t.roughnessMap)),void 0!==t.metalnessMap&&(s.metalnessMap=i(t.metalnessMap)),void 0!==t.emissiveMap&&(s.emissiveMap=i(t.emissiveMap)),void 0!==t.emissiveIntensity&&(s.emissiveIntensity=t.emissiveIntensity),void 0!==t.specularMap&&(s.specularMap=i(t.specularMap)),void 0!==t.specularIntensityMap&&(s.specularIntensityMap=i(t.specularIntensityMap)),void 0!==t.specularColorMap&&(s.specularColorMap=i(t.specularColorMap)),void 0!==t.envMap&&(s.envMap=i(t.envMap)),void 0!==t.envMapRotation&&s.envMapRotation.fromArray(t.envMapRotation),void 0!==t.envMapIntensity&&(s.envMapIntensity=t.envMapIntensity),void 0!==t.reflectivity&&(s.reflectivity=t.reflectivity),void 0!==t.refractionRatio&&(s.refractionRatio=t.refractionRatio),void 0!==t.lightMap&&(s.lightMap=i(t.lightMap)),void 0!==t.lightMapIntensity&&(s.lightMapIntensity=t.lightMapIntensity),void 0!==t.aoMap&&(s.aoMap=i(t.aoMap)),void 0!==t.aoMapIntensity&&(s.aoMapIntensity=t.aoMapIntensity),void 0!==t.gradientMap&&(s.gradientMap=i(t.gradientMap)),void 0!==t.clearcoatMap&&(s.clearcoatMap=i(t.clearcoatMap)),void 0!==t.clearcoatRoughnessMap&&(s.clearcoatRoughnessMap=i(t.clearcoatRoughnessMap)),void 0!==t.clearcoatNormalMap&&(s.clearcoatNormalMap=i(t.clearcoatNormalMap)),void 0!==t.clearcoatNormalScale&&(s.clearcoatNormalScale=(new Gi).fromArray(t.clearcoatNormalScale)),void 0!==t.iridescenceMap&&(s.iridescenceMap=i(t.iridescenceMap)),void 0!==t.iridescenceThicknessMap&&(s.iridescenceThicknessMap=i(t.iridescenceThicknessMap)),void 0!==t.transmissionMap&&(s.transmissionMap=i(t.transmissionMap)),void 0!==t.thicknessMap&&(s.thicknessMap=i(t.thicknessMap)),void 0!==t.anisotropyMap&&(s.anisotropyMap=i(t.anisotropyMap)),void 0!==t.sheenColorMap&&(s.sheenColorMap=i(t.sheenColorMap)),void 0!==t.sheenRoughnessMap&&(s.sheenRoughnessMap=i(t.sheenRoughnessMap)),s}setTextures(t){return this.textures=t,this}createMaterialFromType(t){return Xc.createMaterialFromType(t)}static createMaterialFromType(t){return new{ShadowMaterial:Ol,SpriteMaterial:ma,RawShaderMaterial:Nl,ShaderMaterial:Zn,PointsMaterial:Yo,MeshPhysicalMaterial:Vl,MeshStandardMaterial:Fl,MeshPhongMaterial:Ll,MeshToonMaterial:jl,MeshNormalMaterial:Wl,MeshLambertMaterial:Ul,MeshDepthMaterial:Dl,MeshDistanceMaterial:Hl,MeshBasicMaterial:rn,MeshMatcapMaterial:ql,LineDashedMaterial:Jl,LineBasicMaterial:Po,Material:sn}[t]}}class Yc{static extractUrlBase(t){const e=t.lastIndexOf("/");return-1===e?"./":t.slice(0,e+1)}static resolveURL(t,e){return"string"!=typeof t||""===t?"":(/^https?:\/\//i.test(e)&&/^\//.test(t)&&(e=e.replace(/(^https?:\/\/[^\/]+).*/i,"$1")),/^(https?:)?\/\//i.test(t)||/^data:.*,.*$/i.test(t)||/^blob:.*$/i.test(t)?t:e+t)}}class Zc extends Bn{constructor(){super(),this.isInstancedBufferGeometry=!0,this.type="InstancedBufferGeometry",this.instanceCount=1/0}copy(t){return super.copy(t),this.instanceCount=t.instanceCount,this}toJSON(){const t=super.toJSON();return t.instanceCount=this.instanceCount,t.isInstancedBufferGeometry=!0,t}}class Gc extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=new bc(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){const e={},i={};function s(t,s){if(void 0!==e[s])return e[s];const r=t.interleavedBuffers[s],n=function(t,e){if(void 0!==i[e])return i[e];const s=t.arrayBuffers,r=s[e],n=new Uint32Array(r).buffer;return i[e]=n,n}(t,r.buffer),a=ns(r.type,n),o=new ua(a,r.stride);return o.uuid=r.uuid,e[s]=o,o}const r=t.isInstancedBufferGeometry?new Zc:new Bn,n=t.data.index;if(void 0!==n){const t=ns(n.type,n.array);r.setIndex(new pn(t,1))}const a=t.data.attributes;for(const e in a){const i=a[e];let n;if(i.isInterleavedBufferAttribute){const e=s(t.data,i.data);n=new pa(e,i.itemSize,i.offset,i.normalized)}else{const t=ns(i.type,i.array);n=new(i.isInstancedBufferAttribute?Ya:pn)(t,i.itemSize,i.normalized)}void 0!==i.name&&(n.name=i.name),void 0!==i.usage&&n.setUsage(i.usage),r.setAttribute(e,n)}const o=t.data.morphAttributes;if(o)for(const e in o){const i=o[e],n=[];for(let e=0,r=i.length;e<r;e++){const r=i[e];let a;if(r.isInterleavedBufferAttribute){const e=s(t.data,r.data);a=new pa(e,r.itemSize,r.offset,r.normalized)}else{const t=ns(r.type,r.array);a=new pn(t,r.itemSize,r.normalized)}void 0!==r.name&&(a.name=r.name),n.push(a)}r.morphAttributes[e]=n}t.data.morphTargetsRelative&&(r.morphTargetsRelative=!0);const h=t.data.groups||t.data.drawcalls||t.data.offsets;if(void 0!==h)for(let t=0,e=h.length;t!==e;++t){const e=h[t];r.addGroup(e.start,e.count,e.materialIndex)}const l=t.data.boundingSphere;return void 0!==l&&(r.boundingSphere=(new Qs).fromJSON(l)),t.name&&(r.name=t.name),t.userData&&(r.userData=t.userData),r}}class $c extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=""===this.path?Yc.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||n;const a=new bc(this.manager);a.setPath(this.path),a.setRequestHeader(this.requestHeader),a.setWithCredentials(this.withCredentials),a.load(t,function(i){let n=null;try{n=JSON.parse(i)}catch(e){return void 0!==s&&s(e),void console.error("THREE:ObjectLoader: Can't parse "+t+".",e.message)}const a=n.metadata;if(void 0===a||void 0===a.type||"geometry"===a.type.toLowerCase())return void 0!==s&&s(new Error("THREE.ObjectLoader: Can't load "+t)),void console.error("THREE.ObjectLoader: Can't load "+t);r.parse(n,e)},i,s)}async loadAsync(t,e){const i=""===this.path?Yc.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||i;const s=new bc(this.manager);s.setPath(this.path),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials);const r=await s.loadAsync(t,e),n=JSON.parse(r),a=n.metadata;if(void 0===a||void 0===a.type||"geometry"===a.type.toLowerCase())throw new Error("THREE.ObjectLoader: Can't load "+t);return await this.parseAsync(n)}parse(t,e){const i=this.parseAnimations(t.animations),s=this.parseShapes(t.shapes),r=this.parseGeometries(t.geometries,s),n=this.parseImages(t.images,function(){void 0!==e&&e(h)}),a=this.parseTextures(t.textures,n),o=this.parseMaterials(t.materials,a),h=this.parseObject(t.object,r,o,a,i),l=this.parseSkeletons(t.skeletons,h);if(this.bindSkeletons(h,l),this.bindLightTargets(h),void 0!==e){let t=!1;for(const e in n)if(n[e].data instanceof HTMLImageElement){t=!0;break}!1===t&&e(h)}return h}async parseAsync(t){const e=this.parseAnimations(t.animations),i=this.parseShapes(t.shapes),s=this.parseGeometries(t.geometries,i),r=await this.parseImagesAsync(t.images),n=this.parseTextures(t.textures,r),a=this.parseMaterials(t.materials,n),o=this.parseObject(t.object,s,a,n,e),h=this.parseSkeletons(t.skeletons,o);return this.bindSkeletons(o,h),this.bindLightTargets(o),o}parseShapes(t){const e={};if(void 0!==t)for(let i=0,s=t.length;i<s;i++){const s=(new Uh).fromJSON(t[i]);e[s.uuid]=s}return e}parseSkeletons(t,e){const i={},s={};if(e.traverse(function(t){t.isBone&&(s[t.uuid]=t)}),void 0!==t)for(let e=0,r=t.length;e<r;e++){const r=(new Xa).fromJSON(t[e],s);i[r.uuid]=r}return i}parseGeometries(t,e){const i={};if(void 0!==t){const s=new Gc;for(let r=0,n=t.length;r<n;r++){let n;const a=t[r];switch(a.type){case"BufferGeometry":case"InstancedBufferGeometry":n=s.parse(a);break;default:a.type in Pl?n=Pl[a.type].fromJSON(a,e):console.warn(`THREE.ObjectLoader: Unsupported geometry type "${a.type}"`)}n.uuid=a.uuid,void 0!==a.name&&(n.name=a.name),void 0!==a.userData&&(n.userData=a.userData),i[a.uuid]=n}}return i}parseMaterials(t,e){const i={},s={};if(void 0!==t){const r=new Xc;r.setTextures(e);for(let e=0,n=t.length;e<n;e++){const n=t[e];void 0===i[n.uuid]&&(i[n.uuid]=r.parse(n)),s[n.uuid]=i[n.uuid]}}return s}parseAnimations(t){const e={};if(void 0!==t)for(let i=0;i<t.length;i++){const s=t[i],r=uc.parse(s);e[r.uuid]=r}return e}parseImages(t,e){const i=this,s={};let r;function n(t){if("string"==typeof t){const e=t;return function(t){return i.manager.itemStart(t),r.load(t,function(){i.manager.itemEnd(t)},void 0,function(){i.manager.itemError(t),i.manager.itemEnd(t)})}(/^(\/\/)|([a-z]+:(\/\/)?)/i.test(e)?e:i.resourcePath+e)}return t.data?{data:ns(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){const i=new mc(e);r=new Sc(i),r.setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){const i=t[e],r=i.url;if(Array.isArray(r)){const t=[];for(let e=0,i=r.length;e<i;e++){const i=n(r[e]);null!==i&&(i instanceof HTMLImageElement?t.push(i):t.push(new Ha(i.data,i.width,i.height)))}s[i.uuid]=new Ms(t)}else{const t=n(i.url);s[i.uuid]=new Ms(t)}}}return s}async parseImagesAsync(t){const e=this,i={};let s;async function r(t){if("string"==typeof t){const i=t,r=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(i)?i:e.resourcePath+i;return await s.loadAsync(r)}return t.data?{data:ns(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){s=new Sc(this.manager),s.setCrossOrigin(this.crossOrigin);for(let e=0,s=t.length;e<s;e++){const s=t[e],n=s.url;if(Array.isArray(n)){const t=[];for(let e=0,i=n.length;e<i;e++){const i=n[e],s=await r(i);null!==s&&(s instanceof HTMLImageElement?t.push(s):t.push(new Ha(s.data,s.width,s.height)))}i[s.uuid]=new Ms(t)}else{const t=await r(s.url);i[s.uuid]=new Ms(t)}}}return i}parseTextures(t,e){function i(t,e){return"number"==typeof t?t:(console.warn("THREE.ObjectLoader.parseTexture: Constant should be in numeric form.",t),e[t])}const s={};if(void 0!==t)for(let r=0,n=t.length;r<n;r++){const n=t[r];void 0===n.image&&console.warn('THREE.ObjectLoader: No "image" specified for',n.uuid),void 0===e[n.image]&&console.warn("THREE.ObjectLoader: Undefined image",n.image);const a=e[n.image],o=a.data;let h;Array.isArray(o)?(h=new sa,6===o.length&&(h.needsUpdate=!0)):(h=o&&o.data?new Ha:new Ts,o&&(h.needsUpdate=!0)),h.source=a,h.uuid=n.uuid,void 0!==n.name&&(h.name=n.name),void 0!==n.mapping&&(h.mapping=i(n.mapping,Qc)),void 0!==n.channel&&(h.channel=n.channel),void 0!==n.offset&&h.offset.fromArray(n.offset),void 0!==n.repeat&&h.repeat.fromArray(n.repeat),void 0!==n.center&&h.center.fromArray(n.center),void 0!==n.rotation&&(h.rotation=n.rotation),void 0!==n.wrap&&(h.wrapS=i(n.wrap[0],Kc),h.wrapT=i(n.wrap[1],Kc)),void 0!==n.format&&(h.format=n.format),void 0!==n.internalFormat&&(h.internalFormat=n.internalFormat),void 0!==n.type&&(h.type=n.type),void 0!==n.colorSpace&&(h.colorSpace=n.colorSpace),void 0!==n.minFilter&&(h.minFilter=i(n.minFilter,tu)),void 0!==n.magFilter&&(h.magFilter=i(n.magFilter,tu)),void 0!==n.anisotropy&&(h.anisotropy=n.anisotropy),void 0!==n.flipY&&(h.flipY=n.flipY),void 0!==n.generateMipmaps&&(h.generateMipmaps=n.generateMipmaps),void 0!==n.premultiplyAlpha&&(h.premultiplyAlpha=n.premultiplyAlpha),void 0!==n.unpackAlignment&&(h.unpackAlignment=n.unpackAlignment),void 0!==n.compareFunction&&(h.compareFunction=n.compareFunction),void 0!==n.userData&&(h.userData=n.userData),s[n.uuid]=h}return s}parseObject(t,e,i,s,r){let n,a,o;function h(t){return void 0===e[t]&&console.warn("THREE.ObjectLoader: Undefined geometry",t),e[t]}function l(t){if(void 0!==t){if(Array.isArray(t)){const e=[];for(let s=0,r=t.length;s<r;s++){const r=t[s];void 0===i[r]&&console.warn("THREE.ObjectLoader: Undefined material",r),e.push(i[r])}return e}return void 0===i[t]&&console.warn("THREE.ObjectLoader: Undefined material",t),i[t]}}function c(t){return void 0===s[t]&&console.warn("THREE.ObjectLoader: Undefined texture",t),s[t]}switch(t.type){case"Scene":n=new ca,void 0!==t.background&&(Number.isInteger(t.background)?n.background=new Kr(t.background):n.background=c(t.background)),void 0!==t.environment&&(n.environment=c(t.environment)),void 0!==t.fog&&("Fog"===t.fog.type?n.fog=new la(t.fog.color,t.fog.near,t.fog.far):"FogExp2"===t.fog.type&&(n.fog=new ha(t.fog.color,t.fog.density)),""!==t.fog.name&&(n.fog.name=t.fog.name)),void 0!==t.backgroundBlurriness&&(n.backgroundBlurriness=t.backgroundBlurriness),void 0!==t.backgroundIntensity&&(n.backgroundIntensity=t.backgroundIntensity),void 0!==t.backgroundRotation&&n.backgroundRotation.fromArray(t.backgroundRotation),void 0!==t.environmentIntensity&&(n.environmentIntensity=t.environmentIntensity),void 0!==t.environmentRotation&&n.environmentRotation.fromArray(t.environmentRotation);break;case"PerspectiveCamera":n=new ta(t.fov,t.aspect,t.near,t.far),void 0!==t.focus&&(n.focus=t.focus),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.filmGauge&&(n.filmGauge=t.filmGauge),void 0!==t.filmOffset&&(n.filmOffset=t.filmOffset),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"OrthographicCamera":n=new jc(t.left,t.right,t.top,t.bottom,t.near,t.far),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"AmbientLight":n=new Dc(t.color,t.intensity);break;case"DirectionalLight":n=new Uc(t.color,t.intensity),n.target=t.target||"";break;case"PointLight":n=new Lc(t.color,t.intensity,t.distance,t.decay);break;case"RectAreaLight":n=new Hc(t.color,t.intensity,t.width,t.height);break;case"SpotLight":n=new Pc(t.color,t.intensity,t.distance,t.angle,t.penumbra,t.decay),n.target=t.target||"";break;case"HemisphereLight":n=new Ic(t.color,t.groundColor,t.intensity);break;case"LightProbe":n=(new Jc).fromJSON(t);break;case"SkinnedMesh":a=h(t.geometry),o=l(t.material),n=new Ua(a,o),void 0!==t.bindMode&&(n.bindMode=t.bindMode),void 0!==t.bindMatrix&&n.bindMatrix.fromArray(t.bindMatrix),void 0!==t.skeleton&&(n.skeleton=t.skeleton);break;case"Mesh":a=h(t.geometry),o=l(t.material),n=new Un(a,o);break;case"InstancedMesh":a=h(t.geometry),o=l(t.material);const e=t.count,i=t.instanceMatrix,s=t.instanceColor;n=new io(a,o,e),n.instanceMatrix=new Ya(new Float32Array(i.array),16),void 0!==s&&(n.instanceColor=new Ya(new Float32Array(s.array),s.itemSize));break;case"BatchedMesh":a=h(t.geometry),o=l(t.material),n=new Ro(t.maxInstanceCount,t.maxVertexCount,t.maxIndexCount,o),n.geometry=a,n.perObjectFrustumCulled=t.perObjectFrustumCulled,n.sortObjects=t.sortObjects,n._drawRanges=t.drawRanges,n._reservedRanges=t.reservedRanges,n._geometryInfo=t.geometryInfo.map(t=>{let e=null,i=null;return void 0!==t.boundingBox&&(e=(new Ps).fromJSON(t.boundingBox)),void 0!==t.boundingSphere&&(i=(new Qs).fromJSON(t.boundingSphere)),{...t,boundingBox:e,boundingSphere:i}}),n._instanceInfo=t.instanceInfo,n._availableInstanceIds=t._availableInstanceIds,n._availableGeometryIds=t._availableGeometryIds,n._nextIndexStart=t.nextIndexStart,n._nextVertexStart=t.nextVertexStart,n._geometryCount=t.geometryCount,n._maxInstanceCount=t.maxInstanceCount,n._maxVertexCount=t.maxVertexCount,n._maxIndexCount=t.maxIndexCount,n._geometryInitialized=t.geometryInitialized,n._matricesTexture=c(t.matricesTexture.uuid),n._indirectTexture=c(t.indirectTexture.uuid),void 0!==t.colorsTexture&&(n._colorsTexture=c(t.colorsTexture.uuid)),void 0!==t.boundingSphere&&(n.boundingSphere=(new Qs).fromJSON(t.boundingSphere)),void 0!==t.boundingBox&&(n.boundingBox=(new Ps).fromJSON(t.boundingBox));break;case"LOD":n=new Ea;break;case"Line":n=new Uo(h(t.geometry),l(t.material));break;case"LineLoop":n=new Xo(h(t.geometry),l(t.material));break;case"LineSegments":n=new Jo(h(t.geometry),l(t.material));break;case"PointCloud":case"Points":n=new Ko(h(t.geometry),l(t.material));break;case"Sprite":n=new Ia(l(t.material));break;case"Group":n=new na;break;case"Bone":n=new Da;break;default:n=new Pr}if(n.uuid=t.uuid,void 0!==t.name&&(n.name=t.name),void 0!==t.matrix?(n.matrix.fromArray(t.matrix),void 0!==t.matrixAutoUpdate&&(n.matrixAutoUpdate=t.matrixAutoUpdate),n.matrixAutoUpdate&&n.matrix.decompose(n.position,n.quaternion,n.scale)):(void 0!==t.position&&n.position.fromArray(t.position),void 0!==t.rotation&&n.rotation.fromArray(t.rotation),void 0!==t.quaternion&&n.quaternion.fromArray(t.quaternion),void 0!==t.scale&&n.scale.fromArray(t.scale)),void 0!==t.up&&n.up.fromArray(t.up),void 0!==t.castShadow&&(n.castShadow=t.castShadow),void 0!==t.receiveShadow&&(n.receiveShadow=t.receiveShadow),t.shadow&&(void 0!==t.shadow.intensity&&(n.shadow.intensity=t.shadow.intensity),void 0!==t.shadow.bias&&(n.shadow.bias=t.shadow.bias),void 0!==t.shadow.normalBias&&(n.shadow.normalBias=t.shadow.normalBias),void 0!==t.shadow.radius&&(n.shadow.radius=t.shadow.radius),void 0!==t.shadow.mapSize&&n.shadow.mapSize.fromArray(t.shadow.mapSize),void 0!==t.shadow.camera&&(n.shadow.camera=this.parseObject(t.shadow.camera))),void 0!==t.visible&&(n.visible=t.visible),void 0!==t.frustumCulled&&(n.frustumCulled=t.frustumCulled),void 0!==t.renderOrder&&(n.renderOrder=t.renderOrder),void 0!==t.userData&&(n.userData=t.userData),void 0!==t.layers&&(n.layers.mask=t.layers),void 0!==t.children){const a=t.children;for(let t=0;t<a.length;t++)n.add(this.parseObject(a[t],e,i,s,r))}if(void 0!==t.animations){const e=t.animations;for(let t=0;t<e.length;t++){const i=e[t];n.animations.push(r[i])}}if("LOD"===t.type){void 0!==t.autoUpdate&&(n.autoUpdate=t.autoUpdate);const e=t.levels;for(let t=0;t<e.length;t++){const i=e[t],s=n.getObjectByProperty("uuid",i.object);void 0!==s&&n.addLevel(s,i.distance,i.hysteresis)}}return n}bindSkeletons(t,e){0!==Object.keys(e).length&&t.traverse(function(t){if(!0===t.isSkinnedMesh&&void 0!==t.skeleton){const i=e[t.skeleton];void 0===i?console.warn("THREE.ObjectLoader: No skeleton found with UUID:",t.skeleton):t.bind(i,t.bindMatrix)}})}bindLightTargets(t){t.traverse(function(e){if(e.isDirectionalLight||e.isSpotLight){const i=e.target,s=t.getObjectByProperty("uuid",i);e.target=void 0!==s?s:new Pr}})}}const Qc={UVMapping:ot,CubeReflectionMapping:ht,CubeRefractionMapping:302,EquirectangularReflectionMapping:303,EquirectangularRefractionMapping:304,CubeUVReflectionMapping:306},Kc={RepeatWrapping:pt,ClampToEdgeWrapping:mt,MirroredRepeatWrapping:yt},tu={NearestFilter:gt,NearestMipmapNearestFilter:1004,NearestMipmapLinearFilter:1005,LinearFilter:wt,LinearMipmapNearestFilter:1007,LinearMipmapLinearFilter:_t},eu=new WeakMap;class iu extends gc{constructor(t){super(t),this.isImageBitmapLoader=!0,"undefined"==typeof createImageBitmap&&console.warn("THREE.ImageBitmapLoader: createImageBitmap() not supported."),"undefined"==typeof fetch&&console.warn("THREE.ImageBitmapLoader: fetch() not supported."),this.options={premultiplyAlpha:"none"}}setOptions(t){return this.options=t,this}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=this,n=pc.get(`image-bitmap:${t}`);if(void 0!==n)return r.manager.itemStart(t),n.then?void n.then(i=>{if(!0!==eu.has(n))return e&&e(i),r.manager.itemEnd(t),i;s&&s(eu.get(n)),r.manager.itemError(t),r.manager.itemEnd(t)}):(setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0),n);const a={};a.credentials="anonymous"===this.crossOrigin?"same-origin":"include",a.headers=this.requestHeader;const o=fetch(t,a).then(function(t){return t.blob()}).then(function(t){return createImageBitmap(t,Object.assign(r.options,{colorSpaceConversion:"none"}))}).then(function(i){return pc.add(`image-bitmap:${t}`,i),e&&e(i),r.manager.itemEnd(t),i}).catch(function(e){s&&s(e),eu.set(o,e),pc.remove(`image-bitmap:${t}`),r.manager.itemError(t),r.manager.itemEnd(t)});pc.add(`image-bitmap:${t}`,o),r.manager.itemStart(t)}}let su;class ru{static getContext(){return void 0===su&&(su=new(window.AudioContext||window.webkitAudioContext)),su}static setContext(t){su=t}}class nu extends gc{constructor(t){super(t)}load(t,e,i,s){const r=this,n=new bc(this.manager);function a(e){s?s(e):console.error(e),r.manager.itemError(t)}n.setResponseType("arraybuffer"),n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,function(t){try{const i=t.slice(0);ru.getContext().decodeAudioData(i,function(t){e(t)}).catch(a)}catch(t){a(t)}},i,s)}}const au=new or,ou=new or,hu=new or;class lu{constructor(){this.type="StereoCamera",this.aspect=1,this.eyeSep=.064,this.cameraL=new ta,this.cameraL.layers.enable(1),this.cameraL.matrixAutoUpdate=!1,this.cameraR=new ta,this.cameraR.layers.enable(2),this.cameraR.matrixAutoUpdate=!1,this._cache={focus:null,fov:null,aspect:null,near:null,far:null,zoom:null,eyeSep:null}}update(t){const e=this._cache;if(e.focus!==t.focus||e.fov!==t.fov||e.aspect!==t.aspect*this.aspect||e.near!==t.near||e.far!==t.far||e.zoom!==t.zoom||e.eyeSep!==this.eyeSep){e.focus=t.focus,e.fov=t.fov,e.aspect=t.aspect*this.aspect,e.near=t.near,e.far=t.far,e.zoom=t.zoom,e.eyeSep=this.eyeSep,hu.copy(t.projectionMatrix);const i=e.eyeSep/2,s=i*e.near/e.focus,r=e.near*Math.tan(Wi*e.fov*.5)/e.zoom;let n,a;ou.elements[12]=-i,au.elements[12]=i,n=-r*e.aspect+s,a=r*e.aspect+s,hu.elements[0]=2*e.near/(a-n),hu.elements[8]=(a+n)/(a-n),this.cameraL.projectionMatrix.copy(hu),n=-r*e.aspect-s,a=r*e.aspect-s,hu.elements[0]=2*e.near/(a-n),hu.elements[8]=(a+n)/(a-n),this.cameraR.projectionMatrix.copy(hu)}this.cameraL.matrixWorld.copy(t.matrixWorld).multiply(ou),this.cameraR.matrixWorld.copy(t.matrixWorld).multiply(au)}}class cu extends ta{constructor(t=[]){super(),this.isArrayCamera=!0,this.isMultiViewCamera=!1,this.cameras=t}}class uu{constructor(t=!0){this.autoStart=t,this.startTime=0,this.oldTime=0,this.elapsedTime=0,this.running=!1}start(){this.startTime=performance.now(),this.oldTime=this.startTime,this.elapsedTime=0,this.running=!0}stop(){this.getElapsedTime(),this.running=!1,this.autoStart=!1}getElapsedTime(){return this.getDelta(),this.elapsedTime}getDelta(){let t=0;if(this.autoStart&&!this.running)return this.start(),0;if(this.running){const e=performance.now();t=(e-this.oldTime)/1e3,this.oldTime=e,this.elapsedTime+=t}return t}}const du=new Qi,pu=new $i,mu=new Qi,yu=new Qi,gu=new Qi;class fu extends Pr{constructor(){super(),this.type="AudioListener",this.context=ru.getContext(),this.gain=this.context.createGain(),this.gain.connect(this.context.destination),this.filter=null,this.timeDelta=0,this._clock=new uu}getInput(){return this.gain}removeFilter(){return null!==this.filter&&(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination),this.gain.connect(this.context.destination),this.filter=null),this}getFilter(){return this.filter}setFilter(t){return null!==this.filter?(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination)):this.gain.disconnect(this.context.destination),this.filter=t,this.gain.connect(this.filter),this.filter.connect(this.context.destination),this}getMasterVolume(){return this.gain.gain.value}setMasterVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}updateMatrixWorld(t){super.updateMatrixWorld(t);const e=this.context.listener;if(this.timeDelta=this._clock.getDelta(),this.matrixWorld.decompose(du,pu,mu),yu.set(0,0,-1).applyQuaternion(pu),gu.set(0,1,0).applyQuaternion(pu),e.positionX){const t=this.context.currentTime+this.timeDelta;e.positionX.linearRampToValueAtTime(du.x,t),e.positionY.linearRampToValueAtTime(du.y,t),e.positionZ.linearRampToValueAtTime(du.z,t),e.forwardX.linearRampToValueAtTime(yu.x,t),e.forwardY.linearRampToValueAtTime(yu.y,t),e.forwardZ.linearRampToValueAtTime(yu.z,t),e.upX.linearRampToValueAtTime(gu.x,t),e.upY.linearRampToValueAtTime(gu.y,t),e.upZ.linearRampToValueAtTime(gu.z,t)}else e.setPosition(du.x,du.y,du.z),e.setOrientation(yu.x,yu.y,yu.z,gu.x,gu.y,gu.z)}}class xu extends Pr{constructor(t){super(),this.type="Audio",this.listener=t,this.context=t.context,this.gain=this.context.createGain(),this.gain.connect(t.getInput()),this.autoplay=!1,this.buffer=null,this.detune=0,this.loop=!1,this.loopStart=0,this.loopEnd=0,this.offset=0,this.duration=void 0,this.playbackRate=1,this.isPlaying=!1,this.hasPlaybackControl=!0,this.source=null,this.sourceType="empty",this._startedAt=0,this._progress=0,this._connected=!1,this.filters=[]}getOutput(){return this.gain}setNodeSource(t){return this.hasPlaybackControl=!1,this.sourceType="audioNode",this.source=t,this.connect(),this}setMediaElementSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaNode",this.source=this.context.createMediaElementSource(t),this.connect(),this}setMediaStreamSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaStreamNode",this.source=this.context.createMediaStreamSource(t),this.connect(),this}setBuffer(t){return this.buffer=t,this.sourceType="buffer",this.autoplay&&this.play(),this}play(t=0){if(!0===this.isPlaying)return void console.warn("THREE.Audio: Audio is already playing.");if(!1===this.hasPlaybackControl)return void console.warn("THREE.Audio: this Audio has no playback control.");this._startedAt=this.context.currentTime+t;const e=this.context.createBufferSource();return e.buffer=this.buffer,e.loop=this.loop,e.loopStart=this.loopStart,e.loopEnd=this.loopEnd,e.onended=this.onEnded.bind(this),e.start(this._startedAt,this._progress+this.offset,this.duration),this.isPlaying=!0,this.source=e,this.setDetune(this.detune),this.setPlaybackRate(this.playbackRate),this.connect()}pause(){if(!1!==this.hasPlaybackControl)return!0===this.isPlaying&&(this._progress+=Math.max(this.context.currentTime-this._startedAt,0)*this.playbackRate,!0===this.loop&&(this._progress=this._progress%(this.duration||this.buffer.duration)),this.source.stop(),this.source.onended=null,this.isPlaying=!1),this;console.warn("THREE.Audio: this Audio has no playback control.")}stop(t=0){if(!1!==this.hasPlaybackControl)return this._progress=0,null!==this.source&&(this.source.stop(this.context.currentTime+t),this.source.onended=null),this.isPlaying=!1,this;console.warn("THREE.Audio: this Audio has no playback control.")}connect(){if(this.filters.length>0){this.source.connect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].connect(this.filters[t]);this.filters[this.filters.length-1].connect(this.getOutput())}else this.source.connect(this.getOutput());return this._connected=!0,this}disconnect(){if(!1!==this._connected){if(this.filters.length>0){this.source.disconnect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].disconnect(this.filters[t]);this.filters[this.filters.length-1].disconnect(this.getOutput())}else this.source.disconnect(this.getOutput());return this._connected=!1,this}}getFilters(){return this.filters}setFilters(t){return t||(t=[]),!0===this._connected?(this.disconnect(),this.filters=t.slice(),this.connect()):this.filters=t.slice(),this}setDetune(t){return this.detune=t,!0===this.isPlaying&&void 0!==this.source.detune&&this.source.detune.setTargetAtTime(this.detune,this.context.currentTime,.01),this}getDetune(){return this.detune}getFilter(){return this.getFilters()[0]}setFilter(t){return this.setFilters(t?[t]:[])}setPlaybackRate(t){if(!1!==this.hasPlaybackControl)return this.playbackRate=t,!0===this.isPlaying&&this.source.playbackRate.setTargetAtTime(this.playbackRate,this.context.currentTime,.01),this;console.warn("THREE.Audio: this Audio has no playback control.")}getPlaybackRate(){return this.playbackRate}onEnded(){this.isPlaying=!1,this._progress=0}getLoop(){return!1===this.hasPlaybackControl?(console.warn("THREE.Audio: this Audio has no playback control."),!1):this.loop}setLoop(t){if(!1!==this.hasPlaybackControl)return this.loop=t,!0===this.isPlaying&&(this.source.loop=this.loop),this;console.warn("THREE.Audio: this Audio has no playback control.")}setLoopStart(t){return this.loopStart=t,this}setLoopEnd(t){return this.loopEnd=t,this}getVolume(){return this.gain.gain.value}setVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}copy(t,e){return super.copy(t,e),"buffer"!==t.sourceType?(console.warn("THREE.Audio: Audio source type cannot be copied."),this):(this.autoplay=t.autoplay,this.buffer=t.buffer,this.detune=t.detune,this.loop=t.loop,this.loopStart=t.loopStart,this.loopEnd=t.loopEnd,this.offset=t.offset,this.duration=t.duration,this.playbackRate=t.playbackRate,this.hasPlaybackControl=t.hasPlaybackControl,this.sourceType=t.sourceType,this.filters=t.filters.slice(),this)}clone(t){return new this.constructor(this.listener).copy(this,t)}}const bu=new Qi,vu=new $i,wu=new Qi,Mu=new Qi;class Su extends xu{constructor(t){super(t),this.panner=this.context.createPanner(),this.panner.panningModel="HRTF",this.panner.connect(this.gain)}connect(){return super.connect(),this.panner.connect(this.gain),this}disconnect(){return super.disconnect(),this.panner.disconnect(this.gain),this}getOutput(){return this.panner}getRefDistance(){return this.panner.refDistance}setRefDistance(t){return this.panner.refDistance=t,this}getRolloffFactor(){return this.panner.rolloffFactor}setRolloffFactor(t){return this.panner.rolloffFactor=t,this}getDistanceModel(){return this.panner.distanceModel}setDistanceModel(t){return this.panner.distanceModel=t,this}getMaxDistance(){return this.panner.maxDistance}setMaxDistance(t){return this.panner.maxDistance=t,this}setDirectionalCone(t,e,i){return this.panner.coneInnerAngle=t,this.panner.coneOuterAngle=e,this.panner.coneOuterGain=i,this}updateMatrixWorld(t){if(super.updateMatrixWorld(t),!0===this.hasPlaybackControl&&!1===this.isPlaying)return;this.matrixWorld.decompose(bu,vu,wu),Mu.set(0,0,1).applyQuaternion(vu);const e=this.panner;if(e.positionX){const t=this.context.currentTime+this.listener.timeDelta;e.positionX.linearRampToValueAtTime(bu.x,t),e.positionY.linearRampToValueAtTime(bu.y,t),e.positionZ.linearRampToValueAtTime(bu.z,t),e.orientationX.linearRampToValueAtTime(Mu.x,t),e.orientationY.linearRampToValueAtTime(Mu.y,t),e.orientationZ.linearRampToValueAtTime(Mu.z,t)}else e.setPosition(bu.x,bu.y,bu.z),e.setOrientation(Mu.x,Mu.y,Mu.z)}}class _u{constructor(t,e=2048){this.analyser=t.context.createAnalyser(),this.analyser.fftSize=e,this.data=new Uint8Array(this.analyser.frequencyBinCount),t.getOutput().connect(this.analyser)}getFrequencyData(){return this.analyser.getByteFrequencyData(this.data),this.data}getAverageFrequency(){let t=0;const e=this.getFrequencyData();for(let i=0;i<e.length;i++)t+=e[i];return t/e.length}}class Au{constructor(t,e,i){let s,r,n;switch(this.binding=t,this.valueSize=i,e){case"quaternion":s=this._slerp,r=this._slerpAdditive,n=this._setAdditiveIdentityQuaternion,this.buffer=new Float64Array(6*i),this._workIndex=5;break;case"string":case"bool":s=this._select,r=this._select,n=this._setAdditiveIdentityOther,this.buffer=new Array(5*i);break;default:s=this._lerp,r=this._lerpAdditive,n=this._setAdditiveIdentityNumeric,this.buffer=new Float64Array(5*i)}this._mixBufferRegion=s,this._mixBufferRegionAdditive=r,this._setIdentity=n,this._origIndex=3,this._addIndex=4,this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,this.useCount=0,this.referenceCount=0}accumulate(t,e){const i=this.buffer,s=this.valueSize,r=t*s+s;let n=this.cumulativeWeight;if(0===n){for(let t=0;t!==s;++t)i[r+t]=i[t];n=e}else{n+=e;const t=e/n;this._mixBufferRegion(i,r,0,t,s)}this.cumulativeWeight=n}accumulateAdditive(t){const e=this.buffer,i=this.valueSize,s=i*this._addIndex;0===this.cumulativeWeightAdditive&&this._setIdentity(),this._mixBufferRegionAdditive(e,s,0,t,i),this.cumulativeWeightAdditive+=t}apply(t){const e=this.valueSize,i=this.buffer,s=t*e+e,r=this.cumulativeWeight,n=this.cumulativeWeightAdditive,a=this.binding;if(this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,r<1){const t=e*this._origIndex;this._mixBufferRegion(i,s,t,1-r,e)}n>0&&this._mixBufferRegionAdditive(i,s,this._addIndex*e,1,e);for(let t=e,r=e+e;t!==r;++t)if(i[t]!==i[t+e]){a.setValue(i,s);break}}saveOriginalState(){const t=this.binding,e=this.buffer,i=this.valueSize,s=i*this._origIndex;t.getValue(e,s);for(let t=i,r=s;t!==r;++t)e[t]=e[s+t%i];this._setIdentity(),this.cumulativeWeight=0,this.cumulativeWeightAdditive=0}restoreOriginalState(){const t=3*this.valueSize;this.binding.setValue(this.buffer,t)}_setAdditiveIdentityNumeric(){const t=this._addIndex*this.valueSize,e=t+this.valueSize;for(let i=t;i<e;i++)this.buffer[i]=0}_setAdditiveIdentityQuaternion(){this._setAdditiveIdentityNumeric(),this.buffer[this._addIndex*this.valueSize+3]=1}_setAdditiveIdentityOther(){const t=this._origIndex*this.valueSize,e=this._addIndex*this.valueSize;for(let i=0;i<this.valueSize;i++)this.buffer[e+i]=this.buffer[t+i]}_select(t,e,i,s,r){if(s>=.5)for(let s=0;s!==r;++s)t[e+s]=t[i+s]}_slerp(t,e,i,s){$i.slerpFlat(t,e,t,e,t,i,s)}_slerpAdditive(t,e,i,s,r){const n=this._workIndex*r;$i.multiplyQuaternionsFlat(t,n,t,e,t,i),$i.slerpFlat(t,e,t,e,t,n,s)}_lerp(t,e,i,s,r){const n=1-s;for(let a=0;a!==r;++a){const r=e+a;t[r]=t[r]*n+t[i+a]*s}}_lerpAdditive(t,e,i,s,r){for(let n=0;n!==r;++n){const r=e+n;t[r]=t[r]+t[i+n]*s}}}const Tu="\\[\\]\\.:\\/",zu=new RegExp("["+Tu+"]","g"),Iu="[^"+Tu+"]",Cu="[^"+Tu.replace("\\.","")+"]",Bu=new RegExp("^"+/((?:WC+[\/:])*)/.source.replace("WC",Iu)+/(WCOD+)?/.source.replace("WCOD",Cu)+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",Iu)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",Iu)+"$"),ku=["material","materials","bones","map"];class Eu{constructor(t,e,i){this.path=e,this.parsedPath=i||Eu.parseTrackName(e),this.node=Eu.findNode(t,this.parsedPath.nodeName),this.rootNode=t,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}static create(t,e,i){return t&&t.isAnimationObjectGroup?new Eu.Composite(t,e,i):new Eu(t,e,i)}static sanitizeNodeName(t){return t.replace(/\s/g,"_").replace(zu,"")}static parseTrackName(t){const e=Bu.exec(t);if(null===e)throw new Error("PropertyBinding: Cannot parse trackName: "+t);const i={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]},s=i.nodeName&&i.nodeName.lastIndexOf(".");if(void 0!==s&&-1!==s){const t=i.nodeName.substring(s+1);-1!==ku.indexOf(t)&&(i.nodeName=i.nodeName.substring(0,s),i.objectName=t)}if(null===i.propertyName||0===i.propertyName.length)throw new Error("PropertyBinding: can not parse propertyName from trackName: "+t);return i}static findNode(t,e){if(void 0===e||""===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){const i=t.skeleton.getBoneByName(e);if(void 0!==i)return i}if(t.children){const i=function(t){for(let s=0;s<t.length;s++){const r=t[s];if(r.name===e||r.uuid===e)return r;const n=i(r.children);if(n)return n}return null},s=i(t.children);if(s)return s}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(t,e){t[e]=this.targetObject[this.propertyName]}_getValue_array(t,e){const i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)t[e++]=i[s]}_getValue_arrayElement(t,e){t[e]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(t,e){this.resolvedProperty.toArray(t,e)}_setValue_direct(t,e){this.targetObject[this.propertyName]=t[e]}_setValue_direct_setNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(t,e){const i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++]}_setValue_array_setNeedsUpdate(t,e){const i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(t,e){const i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(t,e){this.resolvedProperty[this.propertyIndex]=t[e]}_setValue_arrayElement_setNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(t,e){this.resolvedProperty.fromArray(t,e)}_setValue_fromArray_setNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(t,e){this.bind(),this.getValue(t,e)}_setValue_unbound(t,e){this.bind(),this.setValue(t,e)}bind(){let t=this.node;const e=this.parsedPath,i=e.objectName,s=e.propertyName;let r=e.propertyIndex;if(t||(t=Eu.findNode(this.rootNode,e.nodeName),this.node=t),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!t)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(i){let s=e.objectIndex;switch(i){case"materials":if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);t=t.material.materials;break;case"bones":if(!t.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);t=t.skeleton.bones;for(let e=0;e<t.length;e++)if(t[e].name===s){s=e;break}break;case"map":if("map"in t){t=t.map;break}if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);t=t.material.map;break;default:if(void 0===t[i])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);t=t[i]}if(void 0!==s){if(void 0===t[s])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,t);t=t[s]}}const n=t[s];if(void 0===n){const i=e.nodeName;return void console.error("THREE.PropertyBinding: Trying to update property for track: "+i+"."+s+" but it wasn't found.",t)}let a=this.Versioning.None;this.targetObject=t,!0===t.isMaterial?a=this.Versioning.NeedsUpdate:!0===t.isObject3D&&(a=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===s){if(!t.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!t.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==t.morphTargetDictionary[r]&&(r=t.morphTargetDictionary[r])}o=this.BindingType.ArrayElement,this.resolvedProperty=n,this.propertyIndex=r}else void 0!==n.fromArray&&void 0!==n.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=n):Array.isArray(n)?(o=this.BindingType.EntireArray,this.resolvedProperty=n):this.propertyName=s;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][a]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}Eu.Composite=class{constructor(t,e,i){const s=i||Eu.parseTrackName(e);this._targetGroup=t,this._bindings=t.subscribe_(e,s)}getValue(t,e){this.bind();const i=this._targetGroup.nCachedObjects_,s=this._bindings[i];void 0!==s&&s.getValue(t,e)}setValue(t,e){const i=this._bindings;for(let s=this._targetGroup.nCachedObjects_,r=i.length;s!==r;++s)i[s].setValue(t,e)}bind(){const t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].bind()}unbind(){const t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].unbind()}},Eu.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},Eu.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},Eu.prototype.GetterByBindingType=[Eu.prototype._getValue_direct,Eu.prototype._getValue_array,Eu.prototype._getValue_arrayElement,Eu.prototype._getValue_toArray],Eu.prototype.SetterByBindingTypeAndVersioning=[[Eu.prototype._setValue_direct,Eu.prototype._setValue_direct_setNeedsUpdate,Eu.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[Eu.prototype._setValue_array,Eu.prototype._setValue_array_setNeedsUpdate,Eu.prototype._setValue_array_setMatrixWorldNeedsUpdate],[Eu.prototype._setValue_arrayElement,Eu.prototype._setValue_arrayElement_setNeedsUpdate,Eu.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[Eu.prototype._setValue_fromArray,Eu.prototype._setValue_fromArray_setNeedsUpdate,Eu.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]];class Ru{constructor(){this.isAnimationObjectGroup=!0,this.uuid=Di(),this._objects=Array.prototype.slice.call(arguments),this.nCachedObjects_=0;const t={};this._indicesByUUID=t;for(let e=0,i=arguments.length;e!==i;++e)t[arguments[e].uuid]=e;this._paths=[],this._parsedPaths=[],this._bindings=[],this._bindingsIndicesByPath={};const e=this;this.stats={objects:{get total(){return e._objects.length},get inUse(){return this.total-e.nCachedObjects_}},get bindingsPerObject(){return e._bindings.length}}}add(){const t=this._objects,e=this._indicesByUUID,i=this._paths,s=this._parsedPaths,r=this._bindings,n=r.length;let a,o=t.length,h=this.nCachedObjects_;for(let l=0,c=arguments.length;l!==c;++l){const c=arguments[l],u=c.uuid;let d=e[u];if(void 0===d){d=o++,e[u]=d,t.push(c);for(let t=0,e=n;t!==e;++t)r[t].push(new Eu(c,i[t],s[t]))}else if(d<h){a=t[d];const o=--h,l=t[o];e[l.uuid]=d,t[d]=l,e[u]=o,t[o]=c;for(let t=0,e=n;t!==e;++t){const e=r[t],n=e[o];let a=e[d];e[d]=n,void 0===a&&(a=new Eu(c,i[t],s[t])),e[o]=a}}else t[d]!==a&&console.error("THREE.AnimationObjectGroup: Different objects with the same UUID detected. Clean the caches or recreate your infrastructure when reloading scenes.")}this.nCachedObjects_=h}remove(){const t=this._objects,e=this._indicesByUUID,i=this._bindings,s=i.length;let r=this.nCachedObjects_;for(let n=0,a=arguments.length;n!==a;++n){const a=arguments[n],o=a.uuid,h=e[o];if(void 0!==h&&h>=r){const n=r++,l=t[n];e[l.uuid]=h,t[h]=l,e[o]=n,t[n]=a;for(let t=0,e=s;t!==e;++t){const e=i[t],s=e[n],r=e[h];e[h]=s,e[n]=r}}}this.nCachedObjects_=r}uncache(){const t=this._objects,e=this._indicesByUUID,i=this._bindings,s=i.length;let r=this.nCachedObjects_,n=t.length;for(let a=0,o=arguments.length;a!==o;++a){const o=arguments[a].uuid,h=e[o];if(void 0!==h)if(delete e[o],h<r){const a=--r,o=t[a],l=--n,c=t[l];e[o.uuid]=h,t[h]=o,e[c.uuid]=a,t[a]=c,t.pop();for(let t=0,e=s;t!==e;++t){const e=i[t],s=e[a],r=e[l];e[h]=s,e[a]=r,e.pop()}}else{const r=--n,a=t[r];r>0&&(e[a.uuid]=h),t[h]=a,t.pop();for(let t=0,e=s;t!==e;++t){const e=i[t];e[h]=e[r],e.pop()}}}this.nCachedObjects_=r}subscribe_(t,e){const i=this._bindingsIndicesByPath;let s=i[t];const r=this._bindings;if(void 0!==s)return r[s];const n=this._paths,a=this._parsedPaths,o=this._objects,h=o.length,l=this.nCachedObjects_,c=new Array(h);s=r.length,i[t]=s,n.push(t),a.push(e),r.push(c);for(let i=l,s=o.length;i!==s;++i){const s=o[i];c[i]=new Eu(s,t,e)}return c}unsubscribe_(t){const e=this._bindingsIndicesByPath,i=e[t];if(void 0!==i){const s=this._paths,r=this._parsedPaths,n=this._bindings,a=n.length-1,o=n[a];e[t[a]]=i,n[i]=o,n.pop(),r[i]=r[a],r.pop(),s[i]=s[a],s.pop()}}}class Pu{constructor(t,e,i=null,s=e.blendMode){this._mixer=t,this._clip=e,this._localRoot=i,this.blendMode=s;const r=e.tracks,n=r.length,a=new Array(n),o={endingStart:Re,endingEnd:Re};for(let t=0;t!==n;++t){const e=r[t].createInterpolant(null);a[t]=e,e.settings=o}this._interpolantSettings=o,this._interpolants=a,this._propertyBindings=new Array(n),this._cacheIndex=null,this._byClipCacheIndex=null,this._timeScaleInterpolant=null,this._weightInterpolant=null,this.loop=2201,this._loopCount=-1,this._startTime=null,this.time=0,this.timeScale=1,this._effectiveTimeScale=1,this.weight=1,this._effectiveWeight=1,this.repetitions=1/0,this.paused=!1,this.enabled=!0,this.clampWhenFinished=!1,this.zeroSlopeAtStart=!0,this.zeroSlopeAtEnd=!0}play(){return this._mixer._activateAction(this),this}stop(){return this._mixer._deactivateAction(this),this.reset()}reset(){return this.paused=!1,this.enabled=!0,this.time=0,this._loopCount=-1,this._startTime=null,this.stopFading().stopWarping()}isRunning(){return this.enabled&&!this.paused&&0!==this.timeScale&&null===this._startTime&&this._mixer._isActiveAction(this)}isScheduled(){return this._mixer._isActiveAction(this)}startAt(t){return this._startTime=t,this}setLoop(t,e){return this.loop=t,this.repetitions=e,this}setEffectiveWeight(t){return this.weight=t,this._effectiveWeight=this.enabled?t:0,this.stopFading()}getEffectiveWeight(){return this._effectiveWeight}fadeIn(t){return this._scheduleFading(t,0,1)}fadeOut(t){return this._scheduleFading(t,1,0)}crossFadeFrom(t,e,i=!1){if(t.fadeOut(e),this.fadeIn(e),!0===i){const i=this._clip.duration,s=t._clip.duration,r=s/i,n=i/s;t.warp(1,r,e),this.warp(n,1,e)}return this}crossFadeTo(t,e,i=!1){return t.crossFadeFrom(this,e,i)}stopFading(){const t=this._weightInterpolant;return null!==t&&(this._weightInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}setEffectiveTimeScale(t){return this.timeScale=t,this._effectiveTimeScale=this.paused?0:t,this.stopWarping()}getEffectiveTimeScale(){return this._effectiveTimeScale}setDuration(t){return this.timeScale=this._clip.duration/t,this.stopWarping()}syncWith(t){return this.time=t.time,this.timeScale=t.timeScale,this.stopWarping()}halt(t){return this.warp(this._effectiveTimeScale,0,t)}warp(t,e,i){const s=this._mixer,r=s.time,n=this.timeScale;let a=this._timeScaleInterpolant;null===a&&(a=s._lendControlInterpolant(),this._timeScaleInterpolant=a);const o=a.parameterPositions,h=a.sampleValues;return o[0]=r,o[1]=r+i,h[0]=t/n,h[1]=e/n,this}stopWarping(){const t=this._timeScaleInterpolant;return null!==t&&(this._timeScaleInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}getMixer(){return this._mixer}getClip(){return this._clip}getRoot(){return this._localRoot||this._mixer._root}_update(t,e,i,s){if(!this.enabled)return void this._updateWeight(t);const r=this._startTime;if(null!==r){const s=(t-r)*i;s<0||0===i?e=0:(this._startTime=null,e=i*s)}e*=this._updateTimeScale(t);const n=this._updateTime(e),a=this._updateWeight(t);if(a>0){const t=this._interpolants,e=this._propertyBindings;if(this.blendMode===Fe)for(let i=0,s=t.length;i!==s;++i)t[i].evaluate(n),e[i].accumulateAdditive(a);else for(let i=0,r=t.length;i!==r;++i)t[i].evaluate(n),e[i].accumulate(s,a)}}_updateWeight(t){let e=0;if(this.enabled){e=this.weight;const i=this._weightInterpolant;if(null!==i){const s=i.evaluate(t)[0];e*=s,t>i.parameterPositions[1]&&(this.stopFading(),0===s&&(this.enabled=!1))}}return this._effectiveWeight=e,e}_updateTimeScale(t){let e=0;if(!this.paused){e=this.timeScale;const i=this._timeScaleInterpolant;if(null!==i){e*=i.evaluate(t)[0],t>i.parameterPositions[1]&&(this.stopWarping(),0===e?this.paused=!0:this.timeScale=e)}}return this._effectiveTimeScale=e,e}_updateTime(t){const e=this._clip.duration,i=this.loop;let s=this.time+t,r=this._loopCount;const n=2202===i;if(0===t)return-1===r||!n||1&~r?s:e-s;if(2200===i){-1===r&&(this._loopCount=0,this._setEndings(!0,!0,!1));t:{if(s>=e)s=e;else{if(!(s<0)){this.time=s;break t}s=0}this.clampWhenFinished?this.paused=!0:this.enabled=!1,this.time=s,this._mixer.dispatchEvent({type:"finished",action:this,direction:t<0?-1:1})}}else{if(-1===r&&(t>=0?(r=0,this._setEndings(!0,0===this.repetitions,n)):this._setEndings(0===this.repetitions,!0,n)),s>=e||s<0){const i=Math.floor(s/e);s-=e*i,r+=Math.abs(i);const a=this.repetitions-r;if(a<=0)this.clampWhenFinished?this.paused=!0:this.enabled=!1,s=t>0?e:0,this.time=s,this._mixer.dispatchEvent({type:"finished",action:this,direction:t>0?1:-1});else{if(1===a){const e=t<0;this._setEndings(e,!e,n)}else this._setEndings(!1,!1,n);this._loopCount=r,this.time=s,this._mixer.dispatchEvent({type:"loop",action:this,loopDelta:i})}}else this.time=s;if(n&&!(1&~r))return e-s}return s}_setEndings(t,e,i){const s=this._interpolantSettings;i?(s.endingStart=Pe,s.endingEnd=Pe):(s.endingStart=t?this.zeroSlopeAtStart?Pe:Re:Oe,s.endingEnd=e?this.zeroSlopeAtEnd?Pe:Re:Oe)}_scheduleFading(t,e,i){const s=this._mixer,r=s.time;let n=this._weightInterpolant;null===n&&(n=s._lendControlInterpolant(),this._weightInterpolant=n);const a=n.parameterPositions,o=n.sampleValues;return a[0]=r,o[0]=e,a[1]=r+t,o[1]=i,this}}const Ou=new Float32Array(1);class Nu extends Vi{constructor(t){super(),this._root=t,this._initMemoryManager(),this._accuIndex=0,this.time=0,this.timeScale=1}_bindAction(t,e){const i=t._localRoot||this._root,s=t._clip.tracks,r=s.length,n=t._propertyBindings,a=t._interpolants,o=i.uuid,h=this._bindingsByRootAndName;let l=h[o];void 0===l&&(l={},h[o]=l);for(let t=0;t!==r;++t){const r=s[t],h=r.name;let c=l[h];if(void 0!==c)++c.referenceCount,n[t]=c;else{if(c=n[t],void 0!==c){null===c._cacheIndex&&(++c.referenceCount,this._addInactiveBinding(c,o,h));continue}const s=e&&e._propertyBindings[t].binding.parsedPath;c=new Au(Eu.create(i,h,s),r.ValueTypeName,r.getValueSize()),++c.referenceCount,this._addInactiveBinding(c,o,h),n[t]=c}a[t].resultBuffer=c.buffer}}_activateAction(t){if(!this._isActiveAction(t)){if(null===t._cacheIndex){const e=(t._localRoot||this._root).uuid,i=t._clip.uuid,s=this._actionsByClip[i];this._bindAction(t,s&&s.knownActions[0]),this._addInactiveAction(t,i,e)}const e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){const i=e[t];0===i.useCount++&&(this._lendBinding(i),i.saveOriginalState())}this._lendAction(t)}}_deactivateAction(t){if(this._isActiveAction(t)){const e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){const i=e[t];0===--i.useCount&&(i.restoreOriginalState(),this._takeBackBinding(i))}this._takeBackAction(t)}}_initMemoryManager(){this._actions=[],this._nActiveActions=0,this._actionsByClip={},this._bindings=[],this._nActiveBindings=0,this._bindingsByRootAndName={},this._controlInterpolants=[],this._nActiveControlInterpolants=0;const t=this;this.stats={actions:{get total(){return t._actions.length},get inUse(){return t._nActiveActions}},bindings:{get total(){return t._bindings.length},get inUse(){return t._nActiveBindings}},controlInterpolants:{get total(){return t._controlInterpolants.length},get inUse(){return t._nActiveControlInterpolants}}}}_isActiveAction(t){const e=t._cacheIndex;return null!==e&&e<this._nActiveActions}_addInactiveAction(t,e,i){const s=this._actions,r=this._actionsByClip;let n=r[e];if(void 0===n)n={knownActions:[t],actionByRoot:{}},t._byClipCacheIndex=0,r[e]=n;else{const e=n.knownActions;t._byClipCacheIndex=e.length,e.push(t)}t._cacheIndex=s.length,s.push(t),n.actionByRoot[i]=t}_removeInactiveAction(t){const e=this._actions,i=e[e.length-1],s=t._cacheIndex;i._cacheIndex=s,e[s]=i,e.pop(),t._cacheIndex=null;const r=t._clip.uuid,n=this._actionsByClip,a=n[r],o=a.knownActions,h=o[o.length-1],l=t._byClipCacheIndex;h._byClipCacheIndex=l,o[l]=h,o.pop(),t._byClipCacheIndex=null;delete a.actionByRoot[(t._localRoot||this._root).uuid],0===o.length&&delete n[r],this._removeInactiveBindingsForAction(t)}_removeInactiveBindingsForAction(t){const e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){const i=e[t];0===--i.referenceCount&&this._removeInactiveBinding(i)}}_lendAction(t){const e=this._actions,i=t._cacheIndex,s=this._nActiveActions++,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_takeBackAction(t){const e=this._actions,i=t._cacheIndex,s=--this._nActiveActions,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_addInactiveBinding(t,e,i){const s=this._bindingsByRootAndName,r=this._bindings;let n=s[e];void 0===n&&(n={},s[e]=n),n[i]=t,t._cacheIndex=r.length,r.push(t)}_removeInactiveBinding(t){const e=this._bindings,i=t.binding,s=i.rootNode.uuid,r=i.path,n=this._bindingsByRootAndName,a=n[s],o=e[e.length-1],h=t._cacheIndex;o._cacheIndex=h,e[h]=o,e.pop(),delete a[r],0===Object.keys(a).length&&delete n[s]}_lendBinding(t){const e=this._bindings,i=t._cacheIndex,s=this._nActiveBindings++,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_takeBackBinding(t){const e=this._bindings,i=t._cacheIndex,s=--this._nActiveBindings,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_lendControlInterpolant(){const t=this._controlInterpolants,e=this._nActiveControlInterpolants++;let i=t[e];return void 0===i&&(i=new ec(new Float32Array(2),new Float32Array(2),1,Ou),i.__cacheIndex=e,t[e]=i),i}_takeBackControlInterpolant(t){const e=this._controlInterpolants,i=t.__cacheIndex,s=--this._nActiveControlInterpolants,r=e[s];t.__cacheIndex=s,e[s]=t,r.__cacheIndex=i,e[i]=r}clipAction(t,e,i){const s=e||this._root,r=s.uuid;let n="string"==typeof t?uc.findByName(s,t):t;const a=null!==n?n.uuid:t,o=this._actionsByClip[a];let h=null;if(void 0===i&&(i=null!==n?n.blendMode:Ne),void 0!==o){const t=o.actionByRoot[r];if(void 0!==t&&t.blendMode===i)return t;h=o.knownActions[0],null===n&&(n=h._clip)}if(null===n)return null;const l=new Pu(this,n,e,i);return this._bindAction(l,h),this._addInactiveAction(l,a,r),l}existingAction(t,e){const i=e||this._root,s=i.uuid,r="string"==typeof t?uc.findByName(i,t):t,n=r?r.uuid:t,a=this._actionsByClip[n];return void 0!==a&&a.actionByRoot[s]||null}stopAllAction(){const t=this._actions;for(let e=this._nActiveActions-1;e>=0;--e)t[e].stop();return this}update(t){t*=this.timeScale;const e=this._actions,i=this._nActiveActions,s=this.time+=t,r=Math.sign(t),n=this._accuIndex^=1;for(let a=0;a!==i;++a){e[a]._update(s,t,r,n)}const a=this._bindings,o=this._nActiveBindings;for(let t=0;t!==o;++t)a[t].apply(n);return this}setTime(t){this.time=0;for(let t=0;t<this._actions.length;t++)this._actions[t].time=0;return this.update(t)}getRoot(){return this._root}uncacheClip(t){const e=this._actions,i=t.uuid,s=this._actionsByClip,r=s[i];if(void 0!==r){const t=r.knownActions;for(let i=0,s=t.length;i!==s;++i){const s=t[i];this._deactivateAction(s);const r=s._cacheIndex,n=e[e.length-1];s._cacheIndex=null,s._byClipCacheIndex=null,n._cacheIndex=r,e[r]=n,e.pop(),this._removeInactiveBindingsForAction(s)}delete s[i]}}uncacheRoot(t){const e=t.uuid,i=this._actionsByClip;for(const t in i){const s=i[t].actionByRoot[e];void 0!==s&&(this._deactivateAction(s),this._removeInactiveAction(s))}const s=this._bindingsByRootAndName[e];if(void 0!==s)for(const t in s){const e=s[t];e.restoreOriginalState(),this._removeInactiveBinding(e)}}uncacheAction(t,e){const i=this.existingAction(t,e);null!==i&&(this._deactivateAction(i),this._removeInactiveAction(i))}}class Fu extends Is{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isRenderTarget3D=!0,this.depth=i,this.texture=new Es(null,t,e,i),this._setTextureOptions(s),this.texture.isRenderTargetTexture=!0}}class Vu{constructor(t){this.value=t}clone(){return new Vu(void 0===this.value.clone?this.value:this.value.clone())}}let Lu=0;class ju extends Vi{constructor(){super(),this.isUniformsGroup=!0,Object.defineProperty(this,"id",{value:Lu++}),this.name="",this.usage=Mi,this.uniforms=[]}add(t){return this.uniforms.push(t),this}remove(t){const e=this.uniforms.indexOf(t);return-1!==e&&this.uniforms.splice(e,1),this}setName(t){return this.name=t,this}setUsage(t){return this.usage=t,this}dispose(){this.dispatchEvent({type:"dispose"})}copy(t){this.name=t.name,this.usage=t.usage;const e=t.uniforms;this.uniforms.length=0;for(let t=0,i=e.length;t<i;t++){const i=Array.isArray(e[t])?e[t]:[e[t]];for(let t=0;t<i.length;t++)this.uniforms.push(i[t].clone())}return this}clone(){return(new this.constructor).copy(this)}}class Wu extends ua{constructor(t,e,i=1){super(t,e),this.isInstancedInterleavedBuffer=!0,this.meshPerAttribute=i}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}clone(t){const e=super.clone(t);return e.meshPerAttribute=this.meshPerAttribute,e}toJSON(t){const e=super.toJSON(t);return e.isInstancedInterleavedBuffer=!0,e.meshPerAttribute=this.meshPerAttribute,e}}class Uu{constructor(t,e,i,s,r,n=!1){this.isGLBufferAttribute=!0,this.name="",this.buffer=t,this.type=e,this.itemSize=i,this.elementSize=s,this.count=r,this.normalized=n,this.version=0}set needsUpdate(t){!0===t&&this.version++}setBuffer(t){return this.buffer=t,this}setType(t,e){return this.type=t,this.elementSize=e,this}setItemSize(t){return this.itemSize=t,this}setCount(t){return this.count=t,this}}const Du=new or;class Hu{constructor(t,e,i=0,s=1/0){this.ray=new ar(t,e),this.near=i,this.far=s,this.camera=null,this.layers=new xr,this.params={Mesh:{},Line:{threshold:1},LOD:{},Points:{threshold:1},Sprite:{}}}set(t,e){this.ray.set(t,e)}setFromCamera(t,e){e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize(),this.camera=e):e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld),this.camera=e):console.error("THREE.Raycaster: Unsupported camera type: "+e.type)}setFromXRController(t){return Du.identity().extractRotation(t.matrixWorld),this.ray.origin.setFromMatrixPosition(t.matrixWorld),this.ray.direction.set(0,0,-1).applyMatrix4(Du),this}intersectObject(t,e=!0,i=[]){return Ju(t,this,i,e),i.sort(qu),i}intersectObjects(t,e=!0,i=[]){for(let s=0,r=t.length;s<r;s++)Ju(t[s],this,i,e);return i.sort(qu),i}}function qu(t,e){return t.distance-e.distance}function Ju(t,e,i,s){let r=!0;if(t.layers.test(e.layers)){!1===t.raycast(e,i)&&(r=!1)}if(!0===r&&!0===s){const s=t.children;for(let t=0,r=s.length;t<r;t++)Ju(s[t],e,i,!0)}}class Xu{constructor(t=1,e=0,i=0){this.radius=t,this.phi=e,this.theta=i}set(t,e,i){return this.radius=t,this.phi=e,this.theta=i,this}copy(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this}makeSafe(){const t=1e-6;return this.phi=Hi(this.phi,t,Math.PI-t),this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+e*e+i*i),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(t,i),this.phi=Math.acos(Hi(e/this.radius,-1,1))),this}clone(){return(new this.constructor).copy(this)}}class Yu{constructor(t=1,e=0,i=0){this.radius=t,this.theta=e,this.y=i}set(t,e,i){return this.radius=t,this.theta=e,this.y=i,this}copy(t){return this.radius=t.radius,this.theta=t.theta,this.y=t.y,this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+i*i),this.theta=Math.atan2(t,i),this.y=e,this}clone(){return(new this.constructor).copy(this)}}class Zu{constructor(t,e,i,s){Zu.prototype.isMatrix2=!0,this.elements=[1,0,0,1],void 0!==t&&this.set(t,e,i,s)}identity(){return this.set(1,0,0,1),this}fromArray(t,e=0){for(let i=0;i<4;i++)this.elements[i]=t[i+e];return this}set(t,e,i,s){const r=this.elements;return r[0]=t,r[2]=e,r[1]=i,r[3]=s,this}}const Gu=new Gi;class $u{constructor(t=new Gi(1/0,1/0),e=new Gi(-1/0,-1/0)){this.isBox2=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){const i=Gu.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}clone(){return(new this.constructor).copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=1/0,this.max.x=this.max.y=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y}getCenter(t){return this.isEmpty()?t.set(0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,Gu).distanceTo(t)}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}const Qu=new Qi,Ku=new Qi;class td{constructor(t=new Qi,e=new Qi){this.start=t,this.end=e}set(t,e){return this.start.copy(t),this.end.copy(e),this}copy(t){return this.start.copy(t.start),this.end.copy(t.end),this}getCenter(t){return t.addVectors(this.start,this.end).multiplyScalar(.5)}delta(t){return t.subVectors(this.end,this.start)}distanceSq(){return this.start.distanceToSquared(this.end)}distance(){return this.start.distanceTo(this.end)}at(t,e){return this.delta(e).multiplyScalar(t).add(this.start)}closestPointToPointParameter(t,e){Qu.subVectors(t,this.start),Ku.subVectors(this.end,this.start);const i=Ku.dot(Ku);let s=Ku.dot(Qu)/i;return e&&(s=Hi(s,0,1)),s}closestPointToPoint(t,e,i){const s=this.closestPointToPointParameter(t,e);return this.delta(i).multiplyScalar(s).add(this.start)}applyMatrix4(t){return this.start.applyMatrix4(t),this.end.applyMatrix4(t),this}equals(t){return t.start.equals(this.start)&&t.end.equals(this.end)}clone(){return(new this.constructor).copy(this)}}const ed=new Qi;class id extends Pr{constructor(t,e){super(),this.light=t,this.matrixAutoUpdate=!1,this.color=e,this.type="SpotLightHelper";const i=new Bn,s=[0,0,0,0,0,1,0,0,0,1,0,1,0,0,0,-1,0,1,0,0,0,0,1,1,0,0,0,0,-1,1];for(let t=0,e=1,i=32;t<i;t++,e++){const r=t/i*Math.PI*2,n=e/i*Math.PI*2;s.push(Math.cos(r),Math.sin(r),1,Math.cos(n),Math.sin(n),1)}i.setAttribute("position",new Mn(s,3));const r=new Po({fog:!1,toneMapped:!1});this.cone=new Jo(i,r),this.add(this.cone),this.update()}dispose(){this.cone.geometry.dispose(),this.cone.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),this.parent?(this.parent.updateWorldMatrix(!0),this.matrix.copy(this.parent.matrixWorld).invert().multiply(this.light.matrixWorld)):this.matrix.copy(this.light.matrixWorld),this.matrixWorld.copy(this.light.matrixWorld);const t=this.light.distance?this.light.distance:1e3,e=t*Math.tan(this.light.angle);this.cone.scale.set(e,e,t),ed.setFromMatrixPosition(this.light.target.matrixWorld),this.cone.lookAt(ed),void 0!==this.color?this.cone.material.color.set(this.color):this.cone.material.color.copy(this.light.color)}}const sd=new Qi,rd=new or,nd=new or;class ad extends Jo{constructor(t){const e=od(t),i=new Bn,s=[],r=[],n=new Kr(0,0,1),a=new Kr(0,1,0);for(let t=0;t<e.length;t++){const i=e[t];i.parent&&i.parent.isBone&&(s.push(0,0,0),s.push(0,0,0),r.push(n.r,n.g,n.b),r.push(a.r,a.g,a.b))}i.setAttribute("position",new Mn(s,3)),i.setAttribute("color",new Mn(r,3));super(i,new Po({vertexColors:!0,depthTest:!1,depthWrite:!1,toneMapped:!1,transparent:!0})),this.isSkeletonHelper=!0,this.type="SkeletonHelper",this.root=t,this.bones=e,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1}updateMatrixWorld(t){const e=this.bones,i=this.geometry,s=i.getAttribute("position");nd.copy(this.root.matrixWorld).invert();for(let t=0,i=0;t<e.length;t++){const r=e[t];r.parent&&r.parent.isBone&&(rd.multiplyMatrices(nd,r.matrixWorld),sd.setFromMatrixPosition(rd),s.setXYZ(i,sd.x,sd.y,sd.z),rd.multiplyMatrices(nd,r.parent.matrixWorld),sd.setFromMatrixPosition(rd),s.setXYZ(i+1,sd.x,sd.y,sd.z),i+=2)}i.getAttribute("position").needsUpdate=!0,super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose()}}function od(t){const e=[];!0===t.isBone&&e.push(t);for(let i=0;i<t.children.length;i++)e.push(...od(t.children[i]));return e}class hd extends Un{constructor(t,e,i){super(new zl(e,4,2),new rn({wireframe:!0,fog:!1,toneMapped:!1})),this.light=t,this.color=i,this.type="PointLightHelper",this.matrix=this.light.matrixWorld,this.matrixAutoUpdate=!1,this.update()}dispose(){this.geometry.dispose(),this.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),void 0!==this.color?this.material.color.set(this.color):this.material.color.copy(this.light.color)}}const ld=new Qi,cd=new Kr,ud=new Kr;class dd extends Pr{constructor(t,e,i){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,this.type="HemisphereLightHelper";const s=new Sl(e);s.rotateY(.5*Math.PI),this.material=new rn({wireframe:!0,fog:!1,toneMapped:!1}),void 0===this.color&&(this.material.vertexColors=!0);const r=s.getAttribute("position"),n=new Float32Array(3*r.count);s.setAttribute("color",new pn(n,3)),this.add(new Un(s,this.material)),this.update()}dispose(){this.children[0].geometry.dispose(),this.children[0].material.dispose()}update(){const t=this.children[0];if(void 0!==this.color)this.material.color.set(this.color);else{const e=t.geometry.getAttribute("color");cd.copy(this.light.color),ud.copy(this.light.groundColor);for(let t=0,i=e.count;t<i;t++){const s=t<i/2?cd:ud;e.setXYZ(t,s.r,s.g,s.b)}e.needsUpdate=!0}this.light.updateWorldMatrix(!0,!1),t.lookAt(ld.setFromMatrixPosition(this.light.matrixWorld).negate())}}class pd extends Jo{constructor(t=10,e=10,i=4473924,s=8947848){i=new Kr(i),s=new Kr(s);const r=e/2,n=t/e,a=t/2,o=[],h=[];for(let t=0,l=0,c=-a;t<=e;t++,c+=n){o.push(-a,0,c,a,0,c),o.push(c,0,-a,c,0,a);const e=t===r?i:s;e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3}const l=new Bn;l.setAttribute("position",new Mn(o,3)),l.setAttribute("color",new Mn(h,3));super(l,new Po({vertexColors:!0,toneMapped:!1})),this.type="GridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}class md extends Jo{constructor(t=10,e=16,i=8,s=64,r=4473924,n=8947848){r=new Kr(r),n=new Kr(n);const a=[],o=[];if(e>1)for(let i=0;i<e;i++){const s=i/e*(2*Math.PI),h=Math.sin(s)*t,l=Math.cos(s)*t;a.push(0,0,0),a.push(h,0,l);const c=1&i?r:n;o.push(c.r,c.g,c.b),o.push(c.r,c.g,c.b)}for(let e=0;e<i;e++){const h=1&e?r:n,l=t-t/i*e;for(let t=0;t<s;t++){let e=t/s*(2*Math.PI),i=Math.sin(e)*l,r=Math.cos(e)*l;a.push(i,0,r),o.push(h.r,h.g,h.b),e=(t+1)/s*(2*Math.PI),i=Math.sin(e)*l,r=Math.cos(e)*l,a.push(i,0,r),o.push(h.r,h.g,h.b)}}const h=new Bn;h.setAttribute("position",new Mn(a,3)),h.setAttribute("color",new Mn(o,3));super(h,new Po({vertexColors:!0,toneMapped:!1})),this.type="PolarGridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}const yd=new Qi,gd=new Qi,fd=new Qi;class xd extends Pr{constructor(t,e,i){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,this.type="DirectionalLightHelper",void 0===e&&(e=1);let s=new Bn;s.setAttribute("position",new Mn([-e,e,0,e,e,0,e,-e,0,-e,-e,0,-e,e,0],3));const r=new Po({fog:!1,toneMapped:!1});this.lightPlane=new Uo(s,r),this.add(this.lightPlane),s=new Bn,s.setAttribute("position",new Mn([0,0,0,0,0,1],3)),this.targetLine=new Uo(s,r),this.add(this.targetLine),this.update()}dispose(){this.lightPlane.geometry.dispose(),this.lightPlane.material.dispose(),this.targetLine.geometry.dispose(),this.targetLine.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),yd.setFromMatrixPosition(this.light.matrixWorld),gd.setFromMatrixPosition(this.light.target.matrixWorld),fd.subVectors(gd,yd),this.lightPlane.lookAt(gd),void 0!==this.color?(this.lightPlane.material.color.set(this.color),this.targetLine.material.color.set(this.color)):(this.lightPlane.material.color.copy(this.light.color),this.targetLine.material.color.copy(this.light.color)),this.targetLine.lookAt(gd),this.targetLine.scale.z=fd.length()}}const bd=new Qi,vd=new Gn;class wd extends Jo{constructor(t){const e=new Bn,i=new Po({color:16777215,vertexColors:!0,toneMapped:!1}),s=[],r=[],n={};function a(t,e){o(t),o(e)}function o(t){s.push(0,0,0),r.push(0,0,0),void 0===n[t]&&(n[t]=[]),n[t].push(s.length/3-1)}a("n1","n2"),a("n2","n4"),a("n4","n3"),a("n3","n1"),a("f1","f2"),a("f2","f4"),a("f4","f3"),a("f3","f1"),a("n1","f1"),a("n2","f2"),a("n3","f3"),a("n4","f4"),a("p","n1"),a("p","n2"),a("p","n3"),a("p","n4"),a("u1","u2"),a("u2","u3"),a("u3","u1"),a("c","t"),a("p","c"),a("cn1","cn2"),a("cn3","cn4"),a("cf1","cf2"),a("cf3","cf4"),e.setAttribute("position",new Mn(s,3)),e.setAttribute("color",new Mn(r,3)),super(e,i),this.type="CameraHelper",this.camera=t,this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.pointMap=n,this.update();const h=new Kr(16755200),l=new Kr(16711680),c=new Kr(43775),u=new Kr(16777215),d=new Kr(3355443);this.setColors(h,l,c,u,d)}setColors(t,e,i,s,r){const n=this.geometry.getAttribute("color");n.setXYZ(0,t.r,t.g,t.b),n.setXYZ(1,t.r,t.g,t.b),n.setXYZ(2,t.r,t.g,t.b),n.setXYZ(3,t.r,t.g,t.b),n.setXYZ(4,t.r,t.g,t.b),n.setXYZ(5,t.r,t.g,t.b),n.setXYZ(6,t.r,t.g,t.b),n.setXYZ(7,t.r,t.g,t.b),n.setXYZ(8,t.r,t.g,t.b),n.setXYZ(9,t.r,t.g,t.b),n.setXYZ(10,t.r,t.g,t.b),n.setXYZ(11,t.r,t.g,t.b),n.setXYZ(12,t.r,t.g,t.b),n.setXYZ(13,t.r,t.g,t.b),n.setXYZ(14,t.r,t.g,t.b),n.setXYZ(15,t.r,t.g,t.b),n.setXYZ(16,t.r,t.g,t.b),n.setXYZ(17,t.r,t.g,t.b),n.setXYZ(18,t.r,t.g,t.b),n.setXYZ(19,t.r,t.g,t.b),n.setXYZ(20,t.r,t.g,t.b),n.setXYZ(21,t.r,t.g,t.b),n.setXYZ(22,t.r,t.g,t.b),n.setXYZ(23,t.r,t.g,t.b),n.setXYZ(24,e.r,e.g,e.b),n.setXYZ(25,e.r,e.g,e.b),n.setXYZ(26,e.r,e.g,e.b),n.setXYZ(27,e.r,e.g,e.b),n.setXYZ(28,e.r,e.g,e.b),n.setXYZ(29,e.r,e.g,e.b),n.setXYZ(30,e.r,e.g,e.b),n.setXYZ(31,e.r,e.g,e.b),n.setXYZ(32,i.r,i.g,i.b),n.setXYZ(33,i.r,i.g,i.b),n.setXYZ(34,i.r,i.g,i.b),n.setXYZ(35,i.r,i.g,i.b),n.setXYZ(36,i.r,i.g,i.b),n.setXYZ(37,i.r,i.g,i.b),n.setXYZ(38,s.r,s.g,s.b),n.setXYZ(39,s.r,s.g,s.b),n.setXYZ(40,r.r,r.g,r.b),n.setXYZ(41,r.r,r.g,r.b),n.setXYZ(42,r.r,r.g,r.b),n.setXYZ(43,r.r,r.g,r.b),n.setXYZ(44,r.r,r.g,r.b),n.setXYZ(45,r.r,r.g,r.b),n.setXYZ(46,r.r,r.g,r.b),n.setXYZ(47,r.r,r.g,r.b),n.setXYZ(48,r.r,r.g,r.b),n.setXYZ(49,r.r,r.g,r.b),n.needsUpdate=!0}update(){const t=this.geometry,e=this.pointMap;vd.projectionMatrixInverse.copy(this.camera.projectionMatrixInverse);const i=this.camera.coordinateSystem===Ri?-1:0;Md("c",e,t,vd,0,0,i),Md("t",e,t,vd,0,0,1),Md("n1",e,t,vd,-1,-1,i),Md("n2",e,t,vd,1,-1,i),Md("n3",e,t,vd,-1,1,i),Md("n4",e,t,vd,1,1,i),Md("f1",e,t,vd,-1,-1,1),Md("f2",e,t,vd,1,-1,1),Md("f3",e,t,vd,-1,1,1),Md("f4",e,t,vd,1,1,1),Md("u1",e,t,vd,.7,1.1,i),Md("u2",e,t,vd,-.7,1.1,i),Md("u3",e,t,vd,0,2,i),Md("cf1",e,t,vd,-1,0,1),Md("cf2",e,t,vd,1,0,1),Md("cf3",e,t,vd,0,-1,1),Md("cf4",e,t,vd,0,1,1),Md("cn1",e,t,vd,-1,0,i),Md("cn2",e,t,vd,1,0,i),Md("cn3",e,t,vd,0,-1,i),Md("cn4",e,t,vd,0,1,i),t.getAttribute("position").needsUpdate=!0}dispose(){this.geometry.dispose(),this.material.dispose()}}function Md(t,e,i,s,r,n,a){bd.set(r,n,a).unproject(s);const o=e[t];if(void 0!==o){const t=i.getAttribute("position");for(let e=0,i=o.length;e<i;e++)t.setXYZ(o[e],bd.x,bd.y,bd.z)}}const Sd=new Ps;class _d extends Jo{constructor(t,e=16776960){const i=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),s=new Float32Array(24),r=new Bn;r.setIndex(new pn(i,1)),r.setAttribute("position",new pn(s,3)),super(r,new Po({color:e,toneMapped:!1})),this.object=t,this.type="BoxHelper",this.matrixAutoUpdate=!1,this.update()}update(){if(void 0!==this.object&&Sd.setFromObject(this.object),Sd.isEmpty())return;const t=Sd.min,e=Sd.max,i=this.geometry.attributes.position,s=i.array;s[0]=e.x,s[1]=e.y,s[2]=e.z,s[3]=t.x,s[4]=e.y,s[5]=e.z,s[6]=t.x,s[7]=t.y,s[8]=e.z,s[9]=e.x,s[10]=t.y,s[11]=e.z,s[12]=e.x,s[13]=e.y,s[14]=t.z,s[15]=t.x,s[16]=e.y,s[17]=t.z,s[18]=t.x,s[19]=t.y,s[20]=t.z,s[21]=e.x,s[22]=t.y,s[23]=t.z,i.needsUpdate=!0,this.geometry.computeBoundingSphere()}setFromObject(t){return this.object=t,this.update(),this}copy(t,e){return super.copy(t,e),this.object=t.object,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class Ad extends Jo{constructor(t,e=16776960){const i=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),s=new Bn;s.setIndex(new pn(i,1)),s.setAttribute("position",new Mn([1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,1,-1,-1,1,-1,-1,-1,-1,1,-1,-1],3)),super(s,new Po({color:e,toneMapped:!1})),this.box=t,this.type="Box3Helper",this.geometry.computeBoundingSphere()}updateMatrixWorld(t){const e=this.box;e.isEmpty()||(e.getCenter(this.position),e.getSize(this.scale),this.scale.multiplyScalar(.5),super.updateMatrixWorld(t))}dispose(){this.geometry.dispose(),this.material.dispose()}}class Td extends Uo{constructor(t,e=1,i=16776960){const s=i,r=new Bn;r.setAttribute("position",new Mn([1,-1,0,-1,1,0,-1,-1,0,1,1,0,-1,1,0,-1,-1,0,1,-1,0,1,1,0],3)),r.computeBoundingSphere(),super(r,new Po({color:s,toneMapped:!1})),this.type="PlaneHelper",this.plane=t,this.size=e;const n=new Bn;n.setAttribute("position",new Mn([1,1,0,-1,1,0,-1,-1,0,1,1,0,-1,-1,0,1,-1,0],3)),n.computeBoundingSphere(),this.add(new Un(n,new rn({color:s,opacity:.2,transparent:!0,depthWrite:!1,toneMapped:!1})))}updateMatrixWorld(t){this.position.set(0,0,0),this.scale.set(.5*this.size,.5*this.size,1),this.lookAt(this.plane.normal),this.translateZ(-this.plane.constant),super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose(),this.children[0].geometry.dispose(),this.children[0].material.dispose()}}const zd=new Qi;let Id,Cd;class Bd extends Pr{constructor(t=new Qi(0,0,1),e=new Qi(0,0,0),i=1,s=16776960,r=.2*i,n=.2*r){super(),this.type="ArrowHelper",void 0===Id&&(Id=new Bn,Id.setAttribute("position",new Mn([0,0,0,0,1,0],3)),Cd=new dh(.5,1,5,1),Cd.translate(0,-.5,0)),this.position.copy(e),this.line=new Uo(Id,new Po({color:s,toneMapped:!1})),this.line.matrixAutoUpdate=!1,this.add(this.line),this.cone=new Un(Cd,new rn({color:s,toneMapped:!1})),this.cone.matrixAutoUpdate=!1,this.add(this.cone),this.setDirection(t),this.setLength(i,r,n)}setDirection(t){if(t.y>.99999)this.quaternion.set(0,0,0,1);else if(t.y<-.99999)this.quaternion.set(1,0,0,0);else{zd.set(t.z,0,-t.x).normalize();const e=Math.acos(t.y);this.quaternion.setFromAxisAngle(zd,e)}}setLength(t,e=.2*t,i=.2*e){this.line.scale.set(1,Math.max(1e-4,t-e),1),this.line.updateMatrix(),this.cone.scale.set(i,e,i),this.cone.position.y=t,this.cone.updateMatrix()}setColor(t){this.line.material.color.set(t),this.cone.material.color.set(t)}copy(t){return super.copy(t,!1),this.line.copy(t.line),this.cone.copy(t.cone),this}dispose(){this.line.geometry.dispose(),this.line.material.dispose(),this.cone.geometry.dispose(),this.cone.material.dispose()}}class kd extends Jo{constructor(t=1){const e=[0,0,0,t,0,0,0,0,0,0,t,0,0,0,0,0,0,t],i=new Bn;i.setAttribute("position",new Mn(e,3)),i.setAttribute("color",new Mn([1,0,0,1,.6,0,0,1,0,.6,1,0,0,0,1,0,.6,1],3));super(i,new Po({vertexColors:!0,toneMapped:!1})),this.type="AxesHelper"}setColors(t,e,i){const s=new Kr,r=this.geometry.attributes.color.array;return s.set(t),s.toArray(r,0),s.toArray(r,3),s.set(e),s.toArray(r,6),s.toArray(r,9),s.set(i),s.toArray(r,12),s.toArray(r,15),this.geometry.attributes.color.needsUpdate=!0,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class Ed{constructor(){this.type="ShapePath",this.color=new Kr,this.subPaths=[],this.currentPath=null}moveTo(t,e){return this.currentPath=new Wh,this.subPaths.push(this.currentPath),this.currentPath.moveTo(t,e),this}lineTo(t,e){return this.currentPath.lineTo(t,e),this}quadraticCurveTo(t,e,i,s){return this.currentPath.quadraticCurveTo(t,e,i,s),this}bezierCurveTo(t,e,i,s,r,n){return this.currentPath.bezierCurveTo(t,e,i,s,r,n),this}splineThru(t){return this.currentPath.splineThru(t),this}toShapes(t){function e(t,e){const i=e.length;let s=!1;for(let r=i-1,n=0;n<i;r=n++){let i=e[r],a=e[n],o=a.x-i.x,h=a.y-i.y;if(Math.abs(h)>Number.EPSILON){if(h<0&&(i=e[n],o=-o,a=e[r],h=-h),t.y<i.y||t.y>a.y)continue;if(t.y===i.y){if(t.x===i.x)return!0}else{const e=h*(t.x-i.x)-o*(t.y-i.y);if(0===e)return!0;if(e<0)continue;s=!s}}else{if(t.y!==i.y)continue;if(a.x<=t.x&&t.x<=i.x||i.x<=t.x&&t.x<=a.x)return!0}}return s}const i=gl.isClockWise,s=this.subPaths;if(0===s.length)return[];let r,n,a;const o=[];if(1===s.length)return n=s[0],a=new Uh,a.curves=n.curves,o.push(a),o;let h=!i(s[0].getPoints());h=t?!h:h;const l=[],c=[];let u,d,p=[],m=0;c[m]=void 0,p[m]=[];for(let e=0,a=s.length;e<a;e++)n=s[e],u=n.getPoints(),r=i(u),r=t?!r:r,r?(!h&&c[m]&&m++,c[m]={s:new Uh,p:u},c[m].s.curves=n.curves,h&&m++,p[m]=[]):p[m].push({h:n,p:u[0]});if(!c[0])return function(t){const e=[];for(let i=0,s=t.length;i<s;i++){const s=t[i],r=new Uh;r.curves=s.curves,e.push(r)}return e}(s);if(c.length>1){let t=!1,i=0;for(let t=0,e=c.length;t<e;t++)l[t]=[];for(let s=0,r=c.length;s<r;s++){const r=p[s];for(let n=0;n<r.length;n++){const a=r[n];let o=!0;for(let r=0;r<c.length;r++)e(a.p,c[r].p)&&(s!==r&&i++,o?(o=!1,l[r].push(a)):t=!0);o&&l[s].push(a)}}i>0&&!1===t&&(p=l)}for(let t=0,e=c.length;t<e;t++){a=c[t].s,o.push(a),d=p[t];for(let t=0,e=d.length;t<e;t++)a.holes.push(d[t].h)}return o}}class Rd extends Vi{constructor(t,e=null){super(),this.object=t,this.domElement=e,this.enabled=!0,this.state=-1,this.keys={},this.mouseButtons={LEFT:null,MIDDLE:null,RIGHT:null},this.touches={ONE:null,TWO:null}}connect(t){void 0!==t?(null!==this.domElement&&this.disconnect(),this.domElement=t):console.warn("THREE.Controls: connect() now requires an element.")}disconnect(){}dispose(){}update(){}}function Pd(t,e,i,s){const r=function(t){switch(t){case Tt:case zt:return{byteLength:1,components:1};case Ct:case It:case Rt:return{byteLength:2,components:1};case Pt:case Ot:return{byteLength:2,components:4};case kt:case Bt:case Et:return{byteLength:4,components:1};case Ft:return{byteLength:4,components:3}}throw new Error(`Unknown texture type ${t}.`)}(s);switch(i){case 1021:return t*e;case Dt:case Ht:return t*e/r.components*r.byteLength;case 1030:case 1031:return t*e*2/r.components*r.byteLength;case 1022:return t*e*3/r.components*r.byteLength;case jt:case 1033:return t*e*4/r.components*r.byteLength;case 33776:case 33777:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case 33778:case 33779:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case 35841:case 35843:return Math.max(t,16)*Math.max(e,8)/4;case 35840:case 35842:return Math.max(t,8)*Math.max(e,8)/2;case 36196:case 37492:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case 37496:case 37808:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case 37809:return Math.floor((t+4)/5)*Math.floor((e+3)/4)*16;case 37810:return Math.floor((t+4)/5)*Math.floor((e+4)/5)*16;case 37811:return Math.floor((t+5)/6)*Math.floor((e+4)/5)*16;case 37812:return Math.floor((t+5)/6)*Math.floor((e+5)/6)*16;case 37813:return Math.floor((t+7)/8)*Math.floor((e+4)/5)*16;case 37814:return Math.floor((t+7)/8)*Math.floor((e+5)/6)*16;case 37815:return Math.floor((t+7)/8)*Math.floor((e+7)/8)*16;case 37816:return Math.floor((t+9)/10)*Math.floor((e+4)/5)*16;case 37817:return Math.floor((t+9)/10)*Math.floor((e+5)/6)*16;case 37818:return Math.floor((t+9)/10)*Math.floor((e+7)/8)*16;case 37819:return Math.floor((t+9)/10)*Math.floor((e+9)/10)*16;case 37820:return Math.floor((t+11)/12)*Math.floor((e+9)/10)*16;case 37821:return Math.floor((t+11)/12)*Math.floor((e+11)/12)*16;case 36492:case 36494:case 36495:return Math.ceil(t/4)*Math.ceil(e/4)*16;case 36283:case 36284:return Math.ceil(t/4)*Math.ceil(e/4)*8;case 36285:case 36286:return Math.ceil(t/4)*Math.ceil(e/4)*16}throw new Error(`Unable to determine texture byte length for ${i} format.`)}class Od{static contain(t,e){return function(t,e){const i=t.image&&t.image.width?t.image.width/t.image.height:1;return i>e?(t.repeat.x=1,t.repeat.y=i/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2):(t.repeat.x=e/i,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0),t}(t,e)}static cover(t,e){return function(t,e){const i=t.image&&t.image.width?t.image.width/t.image.height:1;return i>e?(t.repeat.x=e/i,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0):(t.repeat.x=1,t.repeat.y=i/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2),t}(t,e)}static fill(t){return function(t){return t.repeat.x=1,t.repeat.y=1,t.offset.x=0,t.offset.y=0,t}(t)}static getByteLength(t,e,i,s){return Pd(t,e,i,s)}}"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:t}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=t);export{et as ACESFilmicToneMapping,v as AddEquation,G as AddOperation,Fe as AdditiveAnimationBlendMode,g as AdditiveBlending,st as AgXToneMapping,Vt as AlphaFormat,wi as AlwaysCompare,W as AlwaysDepth,pi as AlwaysStencilFunc,Dc as AmbientLight,Pu as AnimationAction,uc as AnimationClip,vc as AnimationLoader,Nu as AnimationMixer,Ru as AnimationObjectGroup,Ql as AnimationUtils,Mh as ArcCurve,cu as ArrayCamera,Bd as ArrowHelper,nt as AttachedBindMode,xu as Audio,_u as AudioAnalyser,ru as AudioContext,fu as AudioListener,nu as AudioLoader,kd as AxesHelper,d as BackSide,We as BasicDepthPacking,o as BasicShadowMap,Ro as BatchedMesh,Da as Bone,rc as BooleanKeyframeTrack,$u as Box2,Ps as Box3,Ad as Box3Helper,Hn as BoxGeometry,_d as BoxHelper,pn as BufferAttribute,Bn as BufferGeometry,Gc as BufferGeometryLoader,zt as ByteType,pc as Cache,Gn as Camera,wd as CameraHelper,oh as CanvasTexture,lh as CapsuleGeometry,Ih as CatmullRomCurve3,tt as CineonToneMapping,ch as CircleGeometry,mt as ClampToEdgeWrapping,uu as Clock,Kr as Color,nc as ColorKeyframeTrack,gs as ColorManagement,nh as CompressedArrayTexture,ah as CompressedCubeTexture,rh as CompressedTexture,wc as CompressedTextureLoader,dh as ConeGeometry,V as ConstantAlphaFactor,N as ConstantColorFactor,Rd as Controls,ia as CubeCamera,ht as CubeReflectionMapping,lt as CubeRefractionMapping,sa as CubeTexture,_c as CubeTextureLoader,dt as CubeUVReflectionMapping,Eh as CubicBezierCurve,Rh as CubicBezierCurve3,tc as CubicInterpolant,r as CullFaceBack,n as CullFaceFront,a as CullFaceFrontBack,s as CullFaceNone,vh as Curve,jh as CurvePath,b as CustomBlending,it as CustomToneMapping,uh as CylinderGeometry,Yu as Cylindrical,Es as Data3DTexture,Bs as DataArrayTexture,Ha as DataTexture,Ac as DataTextureLoader,ln as DataUtils,ii as DecrementStencilOp,ri as DecrementWrapStencilOp,yc as DefaultLoadingManager,Wt as DepthFormat,Ut as DepthStencilFormat,hh as DepthTexture,at as DetachedBindMode,Uc as DirectionalLight,xd as DirectionalLightHelper,ic as DiscreteInterpolant,mh as DodecahedronGeometry,p as DoubleSide,k as DstAlphaFactor,R as DstColorFactor,Ci as DynamicCopyUsage,Si as DynamicDrawUsage,Ti as DynamicReadUsage,bh as EdgesGeometry,wh as EllipseCurve,gi as EqualCompare,H as EqualDepth,hi as EqualStencilFunc,ct as EquirectangularReflectionMapping,ut as EquirectangularRefractionMapping,fr as Euler,Vi as EventDispatcher,bl as ExtrudeGeometry,bc as FileLoader,wn as Float16BufferAttribute,Mn as Float32BufferAttribute,Et as FloatType,la as Fog,ha as FogExp2,sh as FramebufferTexture,u as FrontSide,co as Frustum,mo as FrustumArray,Uu as GLBufferAttribute,ki as GLSL1,Ei as GLSL3,xi as GreaterCompare,J as GreaterDepth,vi as GreaterEqualCompare,q as GreaterEqualDepth,di as GreaterEqualStencilFunc,ci as GreaterStencilFunc,pd as GridHelper,na as Group,Rt as HalfFloatType,Ic as HemisphereLight,dd as HemisphereLightHelper,wl as IcosahedronGeometry,iu as ImageBitmapLoader,Sc as ImageLoader,vs as ImageUtils,ei as IncrementStencilOp,si as IncrementWrapStencilOp,Ya as InstancedBufferAttribute,Zc as InstancedBufferGeometry,Wu as InstancedInterleavedBuffer,io as InstancedMesh,fn as Int16BufferAttribute,bn as Int32BufferAttribute,mn as Int8BufferAttribute,Bt as IntType,ua as InterleavedBuffer,pa as InterleavedBufferAttribute,Kl as Interpolant,Be as InterpolateDiscrete,ke as InterpolateLinear,Ee as InterpolateSmooth,Fi as InterpolationSamplingMode,Ni as InterpolationSamplingType,ni as InvertStencilOp,Ke as KeepStencilOp,sc as KeyframeTrack,Ea as LOD,Ml as LatheGeometry,xr as Layers,yi as LessCompare,U as LessDepth,fi as LessEqualCompare,D as LessEqualDepth,li as LessEqualStencilFunc,oi as LessStencilFunc,zc as Light,Jc as LightProbe,Uo as Line,td as Line3,Po as LineBasicMaterial,Ph as LineCurve,Oh as LineCurve3,Jl as LineDashedMaterial,Xo as LineLoop,Jo as LineSegments,wt as LinearFilter,ec as LinearInterpolant,At as LinearMipMapLinearFilter,St as LinearMipMapNearestFilter,_t as LinearMipmapLinearFilter,Mt as LinearMipmapNearestFilter,Ze as LinearSRGBColorSpace,Q as LinearToneMapping,Ge as LinearTransfer,gc as Loader,Yc as LoaderUtils,mc as LoadingManager,ze as LoopOnce,Ce as LoopPingPong,Ie as LoopRepeat,e as MOUSE,sn as Material,Xc as MaterialLoader,Zi as MathUtils,Zu as Matrix2,es as Matrix3,or as Matrix4,_ as MaxEquation,Un as Mesh,rn as MeshBasicMaterial,Dl as MeshDepthMaterial,Hl as MeshDistanceMaterial,Ul as MeshLambertMaterial,ql as MeshMatcapMaterial,Wl as MeshNormalMaterial,Ll as MeshPhongMaterial,Vl as MeshPhysicalMaterial,Fl as MeshStandardMaterial,jl as MeshToonMaterial,S as MinEquation,yt as MirroredRepeatWrapping,Z as MixOperation,x as MultiplyBlending,Y as MultiplyOperation,gt as NearestFilter,vt as NearestMipMapLinearFilter,xt as NearestMipMapNearestFilter,bt as NearestMipmapLinearFilter,ft as NearestMipmapNearestFilter,rt as NeutralToneMapping,mi as NeverCompare,j as NeverDepth,ai as NeverStencilFunc,m as NoBlending,Xe as NoColorSpace,$ as NoToneMapping,Ne as NormalAnimationBlendMode,y as NormalBlending,bi as NotEqualCompare,X as NotEqualDepth,ui as NotEqualStencilFunc,ac as NumberKeyframeTrack,Pr as Object3D,$c as ObjectLoader,Je as ObjectSpaceNormalMap,Sl as OctahedronGeometry,T as OneFactor,L as OneMinusConstantAlphaFactor,F as OneMinusConstantColorFactor,E as OneMinusDstAlphaFactor,P as OneMinusDstColorFactor,B as OneMinusSrcAlphaFactor,I as OneMinusSrcColorFactor,jc as OrthographicCamera,h as PCFShadowMap,l as PCFSoftShadowMap,Wh as Path,ta as PerspectiveCamera,ao as Plane,_l as PlaneGeometry,Td as PlaneHelper,Lc as PointLight,hd as PointLightHelper,Ko as Points,Yo as PointsMaterial,md as PolarGridHelper,ph as PolyhedronGeometry,Su as PositionalAudio,Eu as PropertyBinding,Au as PropertyMixer,Nh as QuadraticBezierCurve,Fh as QuadraticBezierCurve3,$i as Quaternion,hc as QuaternionKeyframeTrack,oc as QuaternionLinearInterpolant,Ui as RAD2DEG,Ae as RED_GREEN_RGTC2_Format,Se as RED_RGTC1_Format,t as REVISION,Ue as RGBADepthPacking,jt as RGBAFormat,Yt as RGBAIntegerFormat,fe as RGBA_ASTC_10x10_Format,me as RGBA_ASTC_10x5_Format,ye as RGBA_ASTC_10x6_Format,ge as RGBA_ASTC_10x8_Format,xe as RGBA_ASTC_12x10_Format,be as RGBA_ASTC_12x12_Format,ae as RGBA_ASTC_4x4_Format,oe as RGBA_ASTC_5x4_Format,he as RGBA_ASTC_5x5_Format,le as RGBA_ASTC_6x5_Format,ce as RGBA_ASTC_6x6_Format,ue as RGBA_ASTC_8x5_Format,de as RGBA_ASTC_8x6_Format,pe as RGBA_ASTC_8x8_Format,ve as RGBA_BPTC_Format,ne as RGBA_ETC2_EAC_Format,ie as RGBA_PVRTC_2BPPV1_Format,ee as RGBA_PVRTC_4BPPV1_Format,Gt as RGBA_S3TC_DXT1_Format,$t as RGBA_S3TC_DXT3_Format,Qt as RGBA_S3TC_DXT5_Format,De as RGBDepthPacking,Lt as RGBFormat,Xt as RGBIntegerFormat,we as RGB_BPTC_SIGNED_Format,Me as RGB_BPTC_UNSIGNED_Format,se as RGB_ETC1_Format,re as RGB_ETC2_Format,te as RGB_PVRTC_2BPPV1_Format,Kt as RGB_PVRTC_4BPPV1_Format,Zt as RGB_S3TC_DXT1_Format,He as RGDepthPacking,qt as RGFormat,Jt as RGIntegerFormat,Nl as RawShaderMaterial,ar as Ray,Hu as Raycaster,Hc as RectAreaLight,Dt as RedFormat,Ht as RedIntegerFormat,K as ReinhardToneMapping,Is as RenderTarget,Fu as RenderTarget3D,pt as RepeatWrapping,ti as ReplaceStencilOp,M as ReverseSubtractEquation,Al as RingGeometry,Te as SIGNED_RED_GREEN_RGTC2_Format,_e as SIGNED_RED_RGTC1_Format,Ye as SRGBColorSpace,$e as SRGBTransfer,ca as Scene,Zn as ShaderMaterial,Ol as ShadowMaterial,Uh as Shape,Tl as ShapeGeometry,Ed as ShapePath,gl as ShapeUtils,It as ShortType,Xa as Skeleton,ad as SkeletonHelper,Ua as SkinnedMesh,Ms as Source,Qs as Sphere,zl as SphereGeometry,Xu as Spherical,qc as SphericalHarmonics3,Vh as SplineCurve,Pc as SpotLight,id as SpotLightHelper,Ia as Sprite,ma as SpriteMaterial,C as SrcAlphaFactor,O as SrcAlphaSaturateFactor,z as SrcColorFactor,Ii as StaticCopyUsage,Mi as StaticDrawUsage,Ai as StaticReadUsage,lu as StereoCamera,Bi as StreamCopyUsage,_i as StreamDrawUsage,zi as StreamReadUsage,lc as StringKeyframeTrack,w as SubtractEquation,f as SubtractiveBlending,i as TOUCH,qe as TangentSpaceNormalMap,Il as TetrahedronGeometry,Ts as Texture,Tc as TextureLoader,Od as TextureUtils,Oi as TimestampQuery,Cl as TorusGeometry,Bl as TorusKnotGeometry,Yr as Triangle,je as TriangleFanDrawMode,Le as TriangleStripDrawMode,Ve as TrianglesDrawMode,kl as TubeGeometry,ot as UVMapping,xn as Uint16BufferAttribute,vn as Uint32BufferAttribute,yn as Uint8BufferAttribute,gn as Uint8ClampedBufferAttribute,Vu as Uniform,ju as UniformsGroup,Yn as UniformsUtils,Tt as UnsignedByteType,Nt as UnsignedInt248Type,Ft as UnsignedInt5999Type,kt as UnsignedIntType,Pt as UnsignedShort4444Type,Ot as UnsignedShort5551Type,Ct as UnsignedShortType,c as VSMShadowMap,Gi as Vector2,Qi as Vector3,zs as Vector4,cc as VectorKeyframeTrack,ih as VideoFrameTexture,eh as VideoTexture,Rs as WebGL3DRenderTarget,ks as WebGLArrayRenderTarget,Ri as WebGLCoordinateSystem,ra as WebGLCubeRenderTarget,Cs as WebGLRenderTarget,Pi as WebGPUCoordinateSystem,oa as WebXRController,El as WireframeGeometry,Oe as WrapAroundEnding,Re as ZeroCurvatureEnding,A as ZeroFactor,Pe as ZeroSlopeEnding,Qe as ZeroStencilOp,ss as arrayNeedsUint32,qn as cloneUniforms,os as createCanvasElement,as as createElementNS,Pd as getByteLength,Xn as getUnlitUniformColorSpace,Jn as mergeUniforms,cs as probeAsync,us as toNormalizedProjectionMatrix,ds as toReversedProjectionMatrix,ls as warnOnce};
