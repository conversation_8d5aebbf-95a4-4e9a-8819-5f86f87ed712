<template>
  <el-card class="quick-actions-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">快捷操作</span>
      </div>
    </template>

    <div class="actions-grid">
      <div v-for="action in actions" :key="action.name" class="action-item" @click="handleAction(action)">
        <div class="action-icon" :style="{ backgroundColor: action.color + '20' }">
          <el-icon :style="{ color: action.color, fontSize: '24px' }">
            <component :is="action.icon" />
          </el-icon>
        </div>
        <div class="action-content">
          <div class="action-name">{{ action.name }}</div>
          <div class="action-desc">{{ action.description }}</div>
        </div>
        <el-icon class="action-arrow">
          <ArrowRight />
        </el-icon>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ArrowRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface QuickAction {
  name: string
  description: string
  icon: string
  color: string
  route: string
}

const emit = defineEmits<{
  navigate: [route: string]
}>()

const actions: QuickAction[] = [
  {
    name: '数据采集',
    description: '开始新的数据采集任务',
    icon: 'Monitor',
    color: '#409EFF',
    route: '/screen/collection',
  },

  {
    name: '数据管理',
    description: '查看和管理已有数据',
    icon: 'DataBoard',
    color: '#E6A23C',
    route: '/data/management',
  },
  {
    name: '模型评分',
    description: '查看模型性能评估',
    icon: 'TrendCharts',
    color: '#F56C6C',
    route: '/model/score',
  },
]

const handleAction = (action: QuickAction) => {
  emit('navigate', action.route)
  ElMessage.success(`正在跳转到${action.name}`)
}
</script>

<style scoped lang="scss">
.quick-actions-card {
  height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
    transform: translateX(4px);
  }
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 16px;
}

.action-content {
  flex: 1;
}

.action-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 14px;
  color: #909399;
}

.action-arrow {
  color: #c0c4cc;
  font-size: 16px;
  transition: all 0.3s ease;
}

.action-item:hover .action-arrow {
  color: #409eff;
  transform: translateX(4px);
}
</style>
