<script setup lang="ts">
import { ref, computed } from 'vue'
import type { DataItem } from '@/api/data/type'
import DataImage from './DataImage.vue'
import useLayoutSettingStore from '@/store/modules/setting'

// 定义组件名称
defineOptions({
  name: 'DataTable',
})

// 获取布局设置store
const layoutSettingStore = useLayoutSettingStore()

// 定义props
interface Props {
  data: DataItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
})

// 定义emits
interface Emits {
  selectionChange: [selection: DataItem[]]
  view: [row: DataItem]
  delete: [row: DataItem]
}

const emit = defineEmits<Emits>()

// 表格引用
const tableRef = ref()

// 表格样式计算属性
const headerCellStyle = computed(() => ({
  backgroundColor: layoutSettingStore.darkMode ? '#2d2d2d' : '#fafafa',
  color: layoutSettingStore.darkMode ? '#ffffff' : '#303133',
  fontWeight: 'bold',
  textAlign: 'center',
  height: '50px',
  borderBottom: layoutSettingStore.darkMode ? '1px solid #404040' : '1px solid #e4e7ed',
}))

const cellStyle = computed(() => ({
  textAlign: 'center',
  height: '50px',
  borderBottom: layoutSettingStore.darkMode ? '1px solid #404040' : '1px solid #f0f0f0',
}))

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 获取诊断分类标签文本
const getDiagnosisLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    normal: '正常',
    benign: '良性',
    malignant: '恶性',
    uncertain: '未定',
    other: '其他',
  }
  return labelMap[category] || category
}

// 获取检查部位文本
const getExamPartText = (value?: string) => {
  if (!value) return ''
  const options = {
    thyroid: '甲状腺',
    breast: '乳房',
    liver: '肝脏',
    gallbladder: '胆囊',
    heart: '心脏',
    pancreas: '胰腺',
    spleen: '脾脏',
    prostate: '前列腺',
  }
  return options[value as keyof typeof options] || value
}

// 获取切面类型文本
const getSliceTypeText = (value?: string) => {
  if (!value) return ''
  const options = {
    sagittal: '矢状面',
    coronal: '冠状面',
    transverse: '横断面',
    oblique: '斜切面',
    longitudinal: '纵切面',
    cross: '横切面',
  }
  return options[value as keyof typeof options] || value
}

// 处理选中变化
const handleSelectionChange = (selection: DataItem[]) => {
  emit('selectionChange', selection)
}

// 查看详情
const handleView = (row: DataItem) => {
  emit('view', row)
}

// 删除记录
const handleDelete = (row: DataItem) => {
  emit('delete', row)
}

// 清空选中状态
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

// 暴露方法给父组件
defineExpose({
  clearSelection,
})
</script>

<template>
  <div class="table-container">
    <el-table
      ref="tableRef"
      :data="data"
      style="width: 100%"
      v-loading="loading"
      empty-text="暂无数据"
      border
      stripe
      @selection-change="handleSelectionChange"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="图像" width="100">
        <template #default="{ row }">
          <div v-if="row.images && Array.isArray(row.images) && row.images.length > 0" class="image-preview">
            <DataImage
              :image-id="row.images[0]"
              :preview-image-ids="row.images"
              style="width: 60px; height: 60px; border-radius: 4px"
            />
            <div v-if="row.images.length > 1" class="image-count">+{{ row.images.length - 1 }}</div>
          </div>
          <span v-else class="no-image">无图像</span>
        </template>
      </el-table-column>
      <el-table-column prop="part" label="检查部位" width="100">
        <template #default="{ row }">
          {{ getExamPartText(row.part) || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="section" label="切面类型" width="100">
        <template #default="{ row }">
          {{ getSliceTypeText(row.section) || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="classification" label="诊断分类" width="120">
        <template #default="{ row }">
          {{ getDiagnosisLabel(row.classification) }}
        </template>
      </el-table-column>
      <el-table-column prop="detail" label="详细诊断" min-width="200" show-overflow-tooltip />
      <el-table-column label="特征分析" width="150">
        <template #default="{ row }">
          <el-tag
            v-if="row.caption && Array.isArray(row.caption) && row.caption.length > 0"
            size="small"
            type="primary"
          >
            {{ row.caption[0] }}{{ row.caption.length > 1 ? '...' : '' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="诊断联想" width="150">
        <template #default="{ row }">
          <el-tag
            v-if="row.associate && Array.isArray(row.associate) && row.associate.length > 0"
            size="small"
            type="info"
          >
            {{ row.associate[0] }}{{ row.associate.length > 1 ? '...' : '' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="诊断依据" width="150">
        <template #default="{ row }">
          <el-tag v-if="row.basis && Array.isArray(row.basis) && row.basis.length > 0" size="small" type="success">
            {{ row.basis[0] }}{{ row.basis.length > 1 ? '...' : '' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="focus" label="病灶信息" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.focus || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="location" label="位置" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.location || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.remark || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="report" label="报告" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.report || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatTime(row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button link size="small" @click="handleView(row)">查看</el-button>
          <el-button link size="small" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.table-container {
  margin-bottom: 20px;
  background-color: var(--bg-primary);
  border-radius: 4px;
  box-shadow: var(--shadow-light);

  :deep(.el-table) {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;

    // 强制覆盖所有边框
    *,
    th,
    td,
    tr {
      border-color: var(--border-primary) !important;
    }

    .el-table__header-wrapper {
      background-color: var(--bg-secondary) !important;

      .el-table__header {
        background-color: var(--bg-secondary) !important;

        th {
          background-color: var(--bg-secondary) !important;
          color: var(--text-primary) !important;
          font-weight: 600;
          font-size: 14px;
          padding: 15px 0;
          border-color: var(--border-primary) !important;
          border-bottom-color: var(--border-primary) !important;
          border-right-color: var(--border-primary) !important;
          border-left-color: var(--border-primary) !important;
          border-top-color: var(--border-primary) !important;
        }
      }
    }

    .el-table__body-wrapper {
      background-color: var(--bg-primary) !important;

      .el-table__body {
        background-color: var(--bg-primary) !important;

        td {
          background-color: var(--bg-primary) !important;
          color: var(--text-primary) !important;
          padding: 15px 0;
          font-size: 14px;
          border-color: var(--border-primary) !important;
          border-bottom-color: var(--border-primary) !important;
          border-right-color: var(--border-primary) !important;
          border-left-color: var(--border-primary) !important;
          border-top-color: var(--border-primary) !important;
        }
      }
    }

    .el-table__row {
      background-color: var(--bg-primary) !important;

      &:hover {
        background-color: var(--bg-hover) !important;

        td {
          background-color: var(--bg-hover) !important;
        }
      }

      &.el-table__row--striped {
        background-color: var(--bg-tertiary) !important;

        td {
          background-color: var(--bg-tertiary) !important;
        }

        &:hover {
          background-color: var(--bg-hover) !important;

          td {
            background-color: var(--bg-hover) !important;
          }
        }
      }
    }

    .el-table__empty-block {
      background-color: var(--bg-primary) !important;
      color: var(--text-secondary) !important;
      padding: 60px 0;
      font-size: 14px;
    }

    // 表格外边框强制覆盖
    &.el-table--border {
      border-color: var(--border-primary) !important;

      &::after,
      &::before {
        background-color: var(--border-primary) !important;
      }
    }

    // 强制覆盖所有可能的白色边框
    &[style*='border-color: white'],
    &[style*='border-color: #fff'],
    &[style*='border-color: #ffffff'] {
      border-color: var(--border-primary) !important;
    }
  }

  // 图像预览样式
  .image-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    :deep(.el-image) {
      border: 1px solid #e4e7ed;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .image-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #409eff;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 8px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
  }

  .no-image {
    color: #909399;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
  }
}
</style>
