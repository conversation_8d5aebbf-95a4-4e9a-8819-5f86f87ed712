/**
 * 标注数据转换工具
 * 用于在不同格式的标注数据之间进行转换
 */

import type { AnnotationData } from './annotationStorage'

// 标注点接口定义（来自标注页面）
export interface AnnotationPoint {
  id: string
  index: number
  x: number
  y: number
  originalAnnotation?: any
  fabricX?: number
  fabricY?: number
}

/**
 * 标注数据转换器
 */
export class AnnotationConverter {
  
  /**
   * 将标注页面的标注点数组转换为存储格式
   * @param annotations 标注点数组
   * @param focusDescription 病灶描述（可选，如果不提供会自动生成）
   * @returns 存储格式的标注数据
   */
  static convertToStorageFormat(
    annotations: AnnotationPoint[],
    focusDescription?: string
  ): AnnotationData {
    // 转换坐标为字符串格式
    const location = annotations.map(annotation =>
      `(${annotation.x},${annotation.y})`
    )

    // 生成病灶描述（如果没有提供）
    const focus = focusDescription || AnnotationConverter.generateFocusDescription(annotations)

    return {
      focus,
      location,
      status: 'saved'
    }
  }

  /**
   * 将存储格式转换为标注页面可用的格式
   * @param storageData 存储格式的标注数据
   * @returns 标注点数组
   */
  static convertFromStorageFormat(storageData: AnnotationData): AnnotationPoint[] {
    return storageData.location.map((locationStr, index) => {
      // 解析坐标字符串 "(x,y)"
      const match = locationStr.match(/\((\d+),(\d+)\)/)
      if (!match) {
        console.warn('⚠️ 无法解析坐标字符串:', locationStr)
        return {
          id: `point_${index + 1}`,
          index: index + 1,
          x: 0,
          y: 0
        }
      }

      const x = parseInt(match[1], 10)
      const y = parseInt(match[2], 10)

      return {
        id: `point_${index + 1}`,
        index: index + 1,
        x,
        y
      }
    })
  }

  /**
   * 根据标注点生成病灶描述
   * @param annotations 标注点数组
   * @returns 病灶描述字符串
   */
  static generateFocusDescription(annotations: AnnotationPoint[]): string {
    if (annotations.length === 0) {
      return '未标注病灶区域'
    }

    if (annotations.length === 1) {
      return `单点标注病灶，位置：(${annotations[0].x},${annotations[0].y})`
    }

    // 计算标注区域的边界
    const xCoords = annotations.map(a => a.x)
    const yCoords = annotations.map(a => a.y)
    const minX = Math.min(...xCoords)
    const maxX = Math.max(...xCoords)
    const minY = Math.min(...yCoords)
    const maxY = Math.max(...yCoords)

    const width = maxX - minX
    const height = maxY - minY
    const centerX = Math.round((minX + maxX) / 2)
    const centerY = Math.round((minY + maxY) / 2)

    return `多点标注病灶区域，共${annotations.length}个标注点，中心位置：(${centerX},${centerY})，区域大小：${width}×${height}像素`
  }

  /**
   * 验证标注数据格式
   * @param data 要验证的数据
   * @returns 是否为有效的标注数据
   */
  static validateAnnotationData(data: any): data is AnnotationData {
    if (!data || typeof data !== 'object') {
      return false
    }

    // 检查必需字段
    if (typeof data.focus !== 'string') {
      return false
    }

    if (!Array.isArray(data.location)) {
      return false
    }

    // 检查location数组中的每个元素是否为有效的坐标字符串
    for (const locationStr of data.location) {
      if (typeof locationStr !== 'string') {
        return false
      }
      
      // 验证坐标格式 "(x,y)"
      if (!/^\(\d+,\d+\)$/.test(locationStr)) {
        return false
      }
    }

    // 检查状态字段
    if (data.status && !['draft', 'saved'].includes(data.status)) {
      return false
    }

    return true
  }

  /**
   * 清理和修复标注数据
   * @param data 原始数据
   * @returns 清理后的标注数据
   */
  static sanitizeAnnotationData(data: any): AnnotationData | null {
    try {
      if (!data || typeof data !== 'object') {
        return null
      }

      // 修复focus字段
      const focus = typeof data.focus === 'string' ? data.focus : '病灶信息缺失'

      // 修复location字段
      let location: string[] = []
      if (Array.isArray(data.location)) {
        location = data.location
          .filter((item: any) => typeof item === 'string')
          .filter((item: string) => /^\(\d+,\d+\)$/.test(item))
      }

      // 修复status字段
      const status: 'draft' | 'saved' = ['draft', 'saved'].includes(data.status) ? data.status : 'saved'

      return {
        focus,
        location,
        status
      }
    } catch (error) {
      console.error('❌ 清理标注数据失败:', error)
      return null
    }
  }

  /**
   * 计算标注区域的统计信息
   * @param annotations 标注点数组
   * @returns 统计信息
   */
  static calculateAnnotationStats(annotations: AnnotationPoint[]): {
    pointCount: number
    boundingBox: { minX: number, maxX: number, minY: number, maxY: number }
    center: { x: number, y: number }
    area: number
  } {
    if (annotations.length === 0) {
      return {
        pointCount: 0,
        boundingBox: { minX: 0, maxX: 0, minY: 0, maxY: 0 },
        center: { x: 0, y: 0 },
        area: 0
      }
    }

    const xCoords = annotations.map(a => a.x)
    const yCoords = annotations.map(a => a.y)
    const minX = Math.min(...xCoords)
    const maxX = Math.max(...xCoords)
    const minY = Math.min(...yCoords)
    const maxY = Math.max(...yCoords)

    const centerX = Math.round((minX + maxX) / 2)
    const centerY = Math.round((minY + maxY) / 2)
    const area = (maxX - minX) * (maxY - minY)

    return {
      pointCount: annotations.length,
      boundingBox: { minX, maxX, minY, maxY },
      center: { x: centerX, y: centerY },
      area
    }
  }
}

// 导出便捷方法
export const annotationConverter = {
  convertToStorageFormat: AnnotationConverter.convertToStorageFormat,
  convertFromStorageFormat: AnnotationConverter.convertFromStorageFormat,
  validateAnnotationData: AnnotationConverter.validateAnnotationData,
  sanitizeAnnotationData: AnnotationConverter.sanitizeAnnotationData,
  calculateAnnotationStats: AnnotationConverter.calculateAnnotationStats
}

export default AnnotationConverter
