<template>
  <div class="annotation-item" @click="handleClick">
    <div class="annotation-index">{{ annotation.index }}</div>
    <div class="annotation-coords">
      <span class="coord-label">坐标:</span>
      <span class="coord-value">({{ annotation.x }}, {{ annotation.y }})</span>
    </div>
  </div>
</template>

<script setup>
// 定义组件名称
defineOptions({
  name: 'CoordinateDisplay',
})

// 定义 Props
const props = defineProps({
  annotation: {
    type: Object,
    required: true,
    validator: (value) => {
      return (
        value &&
        typeof value.id !== 'undefined' &&
        typeof value.index === 'number' &&
        typeof value.x === 'number' &&
        typeof value.y === 'number'
      )
    },
  },
})

// 定义 Events
const emit = defineEmits(['annotation-click'])

// 处理点击事件
const handleClick = () => {
  emit('annotation-click', props.annotation)
}
</script>

<style scoped>
.annotation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.annotation-item:hover {
  border-color: var(--medical-blue);
  box-shadow: var(--shadow-light);
}

.annotation-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--medical-blue);
  color: var(--text-inverse);
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.annotation-coords {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.coord-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.coord-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}
</style>
