<template>
  <el-card class="case-info-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Document /></el-icon>
        <span>病例信息</span>
      </div>
    </template>

    <el-row :gutter="20">
      <el-col :span="8">
        <div class="info-item">
          <span class="info-label">病例ID：</span>
          <span class="info-value">{{ caseInfo.caseId || 'CASE_2024_001' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="info-label">诊断类型：</span>
          <el-tag
            :type="getDiagnosisTagType(caseInfo.diagnosisType)"
            size="small"
          >
            {{ getDiagnosisText(caseInfo.diagnosisType) }}
          </el-tag>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="info-label">详细诊断：</span>
          <span class="info-value">{{ caseInfo.diagnosisDetail || '良性结节，BRADS 3类' }}</span>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'
import type { CaseInfo } from '../types'

// Props
interface Props {
  caseInfo: CaseInfo
}

defineProps<Props>()

// 方法
const getDiagnosisTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    normal: 'success',
    benign: 'success',
    malignant: 'danger',
    uncertain: 'warning',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

const getDiagnosisText = (type: string) => {
  const textMap: Record<string, string> = {
    normal: '正常',
    benign: '良性',
    malignant: '恶性',
    uncertain: '未定',
    other: '其他'
  }
  return textMap[type] || '未知'
}
</script>

<style scoped>
/* 病例信息卡片 */
.case-info-card {
  margin-bottom: 20px;
  border-radius: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.info-label {
  font-weight: 400;
  color: #7f8c8d;
  min-width: 80px;
}

.info-value {
  color: #2c3e50;
  font-weight: 400;
}
</style>
