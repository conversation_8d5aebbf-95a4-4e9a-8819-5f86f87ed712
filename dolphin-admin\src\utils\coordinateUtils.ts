/**
 * 坐标相关的工具函数
 */

/**
 * 转换location格式：从字符串数组转换为数字数组
 * @param locationArray 坐标字符串数组，格式为 ["(x1,y1)", "(x2,y2)", ...]
 * @returns 数字数组，格式为 [x1, y1, x2, y2, ...]
 * 
 * @example
 * convertLocationFormat(["(100,200)", "(300,400)"]) 
 * // 返回: [100, 200, 300, 400]
 */
export const convertLocationFormat = (locationArray: string[]): number[] => {
  const result: number[] = []
  locationArray.forEach((locationStr) => {
    // 解析坐标字符串 "(x,y)"
    const match = locationStr.match(/\((\d+),(\d+)\)/)
    if (match) {
      const x = parseInt(match[1], 10)
      const y = parseInt(match[2], 10)
      result.push(x, y) // 将x和y分别添加到数组中
    }
  })
  return result
}

/**
 * 转换location格式：从数字数组转换为字符串数组
 * @param locationArray 数字数组，格式为 [x1, y1, x2, y2, ...]
 * @returns 坐标字符串数组，格式为 ["(x1,y1)", "(x2,y2)", ...]
 * 
 * @example
 * convertLocationToStringFormat([100, 200, 300, 400])
 * // 返回: ["(100,200)", "(300,400)"]
 */
export const convertLocationToStringFormat = (locationArray: number[]): string[] => {
  const result: string[] = []
  for (let i = 0; i < locationArray.length; i += 2) {
    if (i + 1 < locationArray.length) {
      const x = locationArray[i]
      const y = locationArray[i + 1]
      result.push(`(${x},${y})`)
    }
  }
  return result
}
