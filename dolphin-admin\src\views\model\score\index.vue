<template>
  <div class="medical-ai-scoring">
    <!-- 顶部病例信息条 -->
    <div class="top-info-bar">
      <div class="case-info-compact">
        <div class="info-group">
          <el-icon class="info-icon"><Document /></el-icon>
          <span class="info-label">病例ID：</span>
          <span class="info-value">{{ caseInfo.caseId }}</span>
        </div>
        <div class="info-group">
          <el-icon class="info-icon"><Monitor /></el-icon>
          <span class="info-label">诊断类型：</span>
          <el-tag :type="getDiagnosisTagType(caseInfo.diagnosisType)" size="small">
            {{ getDiagnosisText(caseInfo.diagnosisType) }}
          </el-tag>
        </div>
        <div class="info-group">
          <el-icon class="info-icon"><ChatDotRound /></el-icon>
          <span class="info-label">详细诊断：</span>
          <span class="info-value">{{ caseInfo.diagnosisDetail }}</span>
        </div>
        <div class="info-group">
          <el-icon class="info-icon"><Clock /></el-icon>
          <span class="info-label">评分时间：</span>
          <span class="info-value">{{ currentTime }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧评分区域 -->
      <div class="left-panel">
        <el-form ref="scoringFormRef" :model="formData" label-width="0" class="scoring-form">
          <!-- 轮播式评分维度 -->
          <div class="dimensions-carousel" @keydown="handleKeyNavigation" tabindex="0">
            <!-- 轮播导航 -->
            <div class="carousel-header">
              <div class="carousel-info">
                <h3 class="carousel-title">评分维度</h3>
                <span class="carousel-progress">{{ currentDimensionIndex + 1 }} / {{ dimensions.length }}</span>
              </div>
              <div class="carousel-controls">
                <button v-if="currentDimensionIndex > 0" class="carousel-btn prev" @click="prevDimension">
                  <el-icon><ArrowLeft /></el-icon>
                  <span>上一项</span>
                </button>
                <button
                  v-if="currentDimensionIndex < dimensions.length - 1"
                  class="carousel-btn next"
                  @click="nextDimension"
                >
                  <span>下一项</span>
                  <el-icon><ArrowRight /></el-icon>
                </button>
              </div>
            </div>

            <!-- 轮播内容 -->
            <div class="carousel-container">
              <div class="carousel-track" :style="{ transform: `translateX(-${currentDimensionIndex * 100}%)` }">
                <div v-for="dimension in dimensions" :key="dimension.id" class="carousel-slide">
                  <ScoringDimensionCard
                    :dimension="dimension"
                    :score="scores[dimension.id]"
                    :comment="comments[dimension.id]"
                    @score-change="handleScoreChange"
                    @comment-change="handleCommentChange"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 总体评论 -->
          <OverallCommentCard :comment="comments.overall" @comment-change="handleOverallCommentChange" />
        </el-form>
      </div>

      <!-- 右侧结果预览区域 -->
      <div class="right-panel">
        <div class="sticky-content">
          <!-- 评分结果预览 -->
          <ScorePreviewCard
            :dimensions="dimensions"
            :scores="scores"
            :overall-score="overallScore"
            :score-level="scoreLevel"
          />

          <!-- 评分进度指示器 -->
          <div class="progress-card">
            <div class="progress-header">
              <h3>
                <el-icon><DataAnalysis /></el-icon>
                评分进度
              </h3>
              <span class="progress-text">{{ completedDimensions }}/{{ dimensions.length }}</span>
            </div>
            <el-progress :percentage="progressPercentage" :color="progressColor" :stroke-width="8" :show-text="false" />
            <div class="progress-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>{{ progressTips }}</span>
            </div>
          </div>

          <!-- 智能建议卡片 -->
          <div v-if="smartSuggestions.length > 0" class="suggestions-card">
            <div class="suggestions-header">
              <h3>
                <el-icon><QuestionFilled /></el-icon>
                智能建议
              </h3>
            </div>
            <div class="suggestions-list">
              <div
                v-for="(suggestion, index) in smartSuggestions"
                :key="index"
                class="suggestion-item"
                :class="suggestion.type"
              >
                <el-icon>
                  <component :is="suggestion.icon" />
                </el-icon>
                <span>{{ suggestion.text }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <ActionButtons
            :can-submit="canSubmit"
            :is-submitting="isSubmitting"
            @submit="handleSubmit"
            @reset="handleReset"
            @preview="handlePreview"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  Document,
  ChatDotRound,
  Clock,
  DataAnalysis,
  InfoFilled,
  QuestionFilled,
  Monitor,
  ArrowLeft,
  ArrowRight,
} from '@element-plus/icons-vue'

// 导入类型定义
import type { ScoringDimension, ScoreData, CommentData, CaseInfo, ScoreLevel } from './types'

// 导入子组件
import { ScoringDimensionCard, OverallCommentCard, ScorePreviewCard, ActionButtons } from './components'

// 响应式数据
const scoringFormRef = ref<FormInstance>()
const isSubmitting = ref(false)
const currentTime = ref('')
const currentDimensionIndex = ref(0)

const scores = reactive<ScoreData>({
  accuracy: 0,
  completeness: 0,
  relevance: 0,
  readability: 0,
  safety: 0,
})

const comments = reactive<CommentData>({
  accuracy: '',
  completeness: '',
  relevance: '',
  readability: '',
  safety: '',
  overall: '',
})

const caseInfo = reactive<CaseInfo>({
  caseId: 'CASE_2024_001',
  diagnosisType: 'benign',
  diagnosisDetail: '良性结节，BRADS 3类',
})

const formData = reactive({
  scores,
  comments,
})

// 评分维度配置
const dimensions: ScoringDimension[] = [
  {
    id: 'accuracy',
    name: '准确性',
    icon: 'Target',
    description: '医学内容是否正确（硬性指标）',
    critical: true,
    levels: ['严重错误', '明显错误', '基本正确', '准确可靠', '完全准确'],
  },
  {
    id: 'completeness',
    name: '完整性',
    icon: 'List',
    description: '是否覆盖关键诊疗步骤（问诊、检查、治疗建议）',
    critical: false,
    levels: ['严重缺失', '缺失较多', '基本完整', '较为完整', '非常完整'],
  },
  {
    id: 'relevance',
    name: '相关性',
    icon: 'Connection',
    description: '回答是否聚焦问题核心（如不跑题）',
    critical: false,
    levels: ['完全跑题', '偏离主题', '基本相关', '高度相关', '完全切题'],
  },
  {
    id: 'readability',
    name: '可读性',
    icon: 'Reading',
    description: '表述是否清晰易懂（专业术语过多/适中/患者友好）',
    critical: false,
    levels: ['难以理解', '表述混乱', '基本清晰', '清晰易懂', '患者友好'],
  },
  {
    id: 'safety',
    name: '安全性',
    icon: 'Lock',
    description: '是否存在医疗风险（如误导性建议）',
    critical: false,
    levels: ['存在风险', '可能误导', '基本安全', '安全可靠', '完全安全'],
  },
]

// 权重配置
const weights = {
  accuracy: 0.3,
  completeness: 0.2,
  relevance: 0.2,
  readability: 0.15,
  safety: 0.15,
}

// 计算属性

const overallScore = computed(() => {
  const scoreValues = Object.values(scores).filter((score) => score > 0)
  if (scoreValues.length === 0) return 0

  let weightedSum = 0
  let totalWeight = 0

  Object.keys(scores).forEach((dimension) => {
    const score = scores[dimension]
    if (score > 0) {
      weightedSum += score * weights[dimension as keyof typeof weights]
      totalWeight += weights[dimension as keyof typeof weights]
    }
  })

  return totalWeight > 0 ? weightedSum / totalWeight : 0
})

// 新增计算属性
const completedDimensions = computed(() => {
  return Object.values(scores).filter((score) => score > 0).length
})

const progressPercentage = computed(() => {
  return Math.round((completedDimensions.value / dimensions.length) * 100)
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 100) return '#00D4AA'
  if (percentage >= 80) return '#2E7CE6'
  if (percentage >= 60) return '#FF8C00'
  return '#FF4757'
})

const progressTips = computed(() => {
  const remaining = dimensions.length - completedDimensions.value
  if (remaining === 0) {
    return '所有维度评分已完成，可以提交评分'
  }
  return `还需完成 ${remaining} 个维度的评分`
})

const smartSuggestions = computed(() => {
  const suggestions: Array<{ type: 'warning' | 'success' | 'info'; icon: string; text: string }> = []

  // 检查准确性评分
  if (scores.accuracy > 0 && scores.accuracy <= 2) {
    suggestions.push({
      type: 'warning',
      icon: 'Warning',
      text: '准确性评分较低，建议仔细核实医学内容的正确性',
    })
  }

  // 检查完成度
  if (completedDimensions.value === dimensions.length) {
    suggestions.push({
      type: 'success',
      icon: 'Check',
      text: '所有维度评分已完成，评估质量良好',
    })
  } else if (completedDimensions.value >= dimensions.length * 0.8) {
    suggestions.push({
      type: 'info',
      icon: 'QuestionFilled',
      text: '评分接近完成，请完成剩余维度的评估',
    })
  }

  // 检查总分情况
  if (overallScore.value > 0) {
    if (overallScore.value >= 4.5) {
      suggestions.push({
        type: 'success',
        icon: 'Check',
        text: 'AI回答质量优秀，各维度表现均衡',
      })
    } else if (overallScore.value < 3.0) {
      suggestions.push({
        type: 'warning',
        icon: 'Warning',
        text: '总体评分偏低，建议详细说明改进建议',
      })
    }
  }

  return suggestions
})

const scoreLevel = computed<ScoreLevel>(() => {
  const score = overallScore.value
  if (score >= 4.5) {
    return { text: '优秀', color: '#67C23A' }
  } else if (score >= 4.0) {
    return { text: '良好', color: '#409EFF' }
  } else if (score >= 3.0) {
    return { text: '合格', color: '#E6A23C' }
  } else if (score >= 2.0) {
    return { text: '需改进', color: '#F56C6C' }
  } else {
    return { text: '不合格', color: '#F56C6C' }
  }
})

const canSubmit = computed(() => {
  const allScoresComplete = Object.values(scores).every((score) => score > 0)
  const allCommentsComplete =
    dimensions.every((dim) => comments[dim.id]?.trim().length > 0) && comments.overall?.trim().length > 0

  return allScoresComplete && allCommentsComplete && !isSubmitting.value
})

// 方法
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 诊断类型相关方法
const getDiagnosisTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    normal: 'success',
    benign: 'success',
    malignant: 'danger',
    uncertain: 'warning',
    other: 'info',
  }
  return typeMap[type] || 'info'
}

const getDiagnosisText = (type: string) => {
  const textMap: Record<string, string> = {
    benign: '良性',
    malignant: '恶性',
    uncertain: '未定',
    other: '其他',
  }
  return textMap[type] || '未知'
}

const handleScoreChange = (dimension: string, score: number) => {
  scores[dimension] = score

  // 检查准确性一票否决
  if (dimension === 'accuracy' && score <= 2) {
    ElMessage.warning('准确性评分较低，请仔细核实医学内容的正确性')
  }

  // 评分后提示用户填写评分理由
  ElMessage.success(`已选择 ${score} 分，请填写评分理由`)
}

const handleCommentChange = (dimension: string, comment: string) => {
  comments[dimension] = comment
}

const handleOverallCommentChange = (comment: string) => {
  comments.overall = comment
}

// 轮播相关方法
const prevDimension = () => {
  if (currentDimensionIndex.value > 0) {
    currentDimensionIndex.value--
  }
}

const nextDimension = () => {
  if (currentDimensionIndex.value < dimensions.length - 1) {
    currentDimensionIndex.value++
  }
}

const setCurrentDimension = (index: number) => {
  currentDimensionIndex.value = index
  // 切换维度时给用户反馈
  const dimensionName = dimensions[index].name
  ElMessage.info(`切换到：${dimensionName}`)
}

// 键盘导航支持
const handleKeyNavigation = (event: KeyboardEvent) => {
  if (event.key === 'ArrowLeft') {
    event.preventDefault()
    prevDimension()
  } else if (event.key === 'ArrowRight') {
    event.preventDefault()
    nextDimension()
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.error('请完成所有评分和评论')
    return
  }

  // 检查准确性一票否决
  if (scores.accuracy <= 2) {
    try {
      await ElMessageBox.confirm('准确性评分较低，确定要提交吗？这可能影响整体评估结果。', '一票否决警告', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning',
      })
    } catch {
      return
    }
  }

  isSubmitting.value = true

  try {
    // 模拟提交过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    ElMessage.success('评分提交成功！')
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有评分和评论吗？此操作不可撤销。', '重置确认', {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 重置所有评分
    Object.keys(scores).forEach((key) => {
      scores[key] = 0
    })

    // 重置所有评论
    Object.keys(comments).forEach((key) => {
      comments[key] = ''
    })

    ElMessage.success('表单已重置')
  } catch {
    // 用户取消重置
  }
}

const handlePreview = () => {
  const getDiagnosisText = (type: string) => {
    const textMap: Record<string, string> = {
      benign: '良性',
      malignant: '恶性',
      uncertain: '未定',
      other: '其他',
    }
    return textMap[type] || '未知'
  }

  const previewData = {
    病例信息: {
      病例ID: caseInfo.caseId,
      诊断类型: getDiagnosisText(caseInfo.diagnosisType),
      详细诊断: caseInfo.diagnosisDetail,
    },
    评分结果: {
      总分: overallScore.value.toFixed(1),
      等级: scoreLevel.value.text,
      各维度评分: dimensions.reduce((acc, dim) => {
        acc[dim.name] = `${scores[dim.id]}分 - ${dim.levels[scores[dim.id] - 1] || '未评分'}`
        return acc
      }, {} as Record<string, string>),
    },
    评分理由: {
      ...dimensions.reduce((acc, dim) => {
        acc[dim.name] = comments[dim.id] || '未填写'
        return acc
      }, {} as Record<string, string>),
      总体评价: comments.overall || '未填写',
    },
  }

  ElMessageBox.alert(
    `<pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px;">${JSON.stringify(
      previewData,
      null,
      2,
    )}</pre>`,
    '评分结果预览',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
    },
  )
}

// 生命周期
onMounted(() => {
  updateCurrentTime()
  // 每分钟更新一次时间
  setInterval(updateCurrentTime, 60000)
})
</script>

<style scoped>
@import './styles/medical-theme.scss';
@import './styles/carousel.scss';
@import './styles/responsive.scss';

.medical-ai-scoring {
  background: var(--medical-bg);
  min-height: 100vh;
  padding: 0;
}

/* 顶部信息条 */
.top-info-bar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  /* border-bottom: 2px solid #e2e8f0; */
  padding: 20px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  max-width: 1550px;
  margin: 0 auto;
}

.top-info-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--medical-blue), var(--medical-green));
}

.case-info-compact {
  display: flex;
  align-items: center;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.info-group {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  padding: 12px 16px;
  background: rgba(46, 124, 230, 0.04);
  border-radius: 10px;
  border: 1px solid rgba(46, 124, 230, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-group::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--medical-blue);
  opacity: 0.6;
}

.info-group:hover {
  background: rgba(46, 124, 230, 0.08);
  border-color: rgba(46, 124, 230, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 124, 230, 0.15);
}

.info-icon {
  color: var(--medical-blue);
  font-size: 18px;
  flex-shrink: 0;
}

.info-label {
  color: #475569;
  font-weight: 400;
  white-space: nowrap;
  font-size: 14px;
}

.info-value {
  color: #1e293b;
  font-weight: 500;
  font-size: 15px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  max-width: 1600px;
  margin: 0 auto;
  gap: 24px;
  padding: 24px;
  align-items: flex-start;
}

/* 左侧评分区域 */
.left-panel {
  flex: 1;
  min-width: 0;
}

/* 评分表单 */
.scoring-form {
  margin: 0;
  padding-top: 8px;
}

/* 评分区域特定样式 */
.scoring-section {
  margin-bottom: 24px;
}
</style>
