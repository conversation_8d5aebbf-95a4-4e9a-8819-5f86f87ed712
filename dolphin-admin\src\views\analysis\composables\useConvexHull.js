export function useConvexHull() {
  // 凸包算法 - Jarvis步进算法（Gift Wrapping）
  const computeConvexHull = (points) => {
    if (points.length < 3) return points

    // 找到最左下角的点作为起始点
    let leftmost = 0
    for (let i = 1; i < points.length; i++) {
      if (
        points[i].x < points[leftmost].x ||
        (points[i].x === points[leftmost].x && points[i].y < points[leftmost].y)
      ) {
        leftmost = i
      }
    }

    const hull = []
    let current = leftmost

    do {
      hull.push(points[current])

      // 找到相对于当前点的最逆时针的下一个点
      let next = (current + 1) % points.length

      for (let i = 0; i < points.length; i++) {
        // 计算叉积判断方向
        const cross = crossProduct(points[current], points[i], points[next])
        if (
          cross > 0 ||
          (cross === 0 && distance(points[current], points[i]) > distance(points[current], points[next]))
        ) {
          next = i
        }
      }

      current = next
    } while (current !== leftmost)

    return hull
  }

  // 计算两个向量的叉积
  const crossProduct = (o, a, b) => {
    return (a.x - o.x) * (b.y - o.y) - (a.y - o.y) * (b.x - o.x)
  }

  // 计算两点间距离
  const distance = (a, b) => {
    return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2)
  }

  // Graham扫描算法（备选实现）
  const grahamScan = (points) => {
    if (points.length < 3) return points

    // 找到最下方的点（y最小），如果有多个则选择最左的
    let bottom = 0
    for (let i = 1; i < points.length; i++) {
      if (points[i].y < points[bottom].y || (points[i].y === points[bottom].y && points[i].x < points[bottom].x)) {
        bottom = i
      }
    }

    // 将最下方的点移到数组开头
    ;[points[0], points[bottom]] = [points[bottom], points[0]]
    const pivot = points[0]

    // 按极角排序
    const sortedPoints = points.slice(1).sort((a, b) => {
      const cross = crossProduct(pivot, a, b)
      if (cross === 0) {
        // 如果共线，按距离排序
        return distance(pivot, a) - distance(pivot, b)
      }
      return cross > 0 ? -1 : 1
    })

    const hull = [pivot]

    for (const point of sortedPoints) {
      // 移除不构成左转的点
      while (hull.length > 1 && crossProduct(hull[hull.length - 2], hull[hull.length - 1], point) <= 0) {
        hull.pop()
      }
      hull.push(point)
    }

    return hull
  }

  // 检查点是否在凸包内
  const isPointInConvexHull = (point, hull) => {
    if (hull.length < 3) return false

    for (let i = 0; i < hull.length; i++) {
      const current = hull[i]
      const next = hull[(i + 1) % hull.length]

      // 如果点在边的右侧，则不在凸包内
      if (crossProduct(current, next, point) < 0) {
        return false
      }
    }

    return true
  }

  // 计算凸包面积
  const calculateConvexHullArea = (hull) => {
    if (hull.length < 3) return 0

    let area = 0
    for (let i = 0; i < hull.length; i++) {
      const current = hull[i]
      const next = hull[(i + 1) % hull.length]
      area += current.x * next.y - next.x * current.y
    }

    return Math.abs(area) / 2
  }

  // 计算凸包周长
  const calculateConvexHullPerimeter = (hull) => {
    if (hull.length < 2) return 0

    let perimeter = 0
    for (let i = 0; i < hull.length; i++) {
      const current = hull[i]
      const next = hull[(i + 1) % hull.length]
      perimeter += distance(current, next)
    }

    return perimeter
  }

  // 简化凸包（移除共线点）
  const simplifyConvexHull = (hull, tolerance = 1e-10) => {
    if (hull.length < 3) return hull

    const simplified = []

    for (let i = 0; i < hull.length; i++) {
      const prev = hull[(i - 1 + hull.length) % hull.length]
      const current = hull[i]
      const next = hull[(i + 1) % hull.length]

      // 如果当前点不与前后点共线，则保留
      if (Math.abs(crossProduct(prev, current, next)) > tolerance) {
        simplified.push(current)
      }
    }

    return simplified
  }

  return {
    computeConvexHull,
    grahamScan,
    crossProduct,
    distance,
    isPointInConvexHull,
    calculateConvexHullArea,
    calculateConvexHullPerimeter,
    simplifyConvexHull,
  }
}
