<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search, ArrowRight } from '@element-plus/icons-vue'
import { TagManager } from './index'
import type { MedicalFormData } from '../types'

// Props 定义
interface Props {
  formData: MedicalFormData
}

const props = defineProps<Props>()

// Events 定义
interface Emits {
  'update:caption': [caption: string[]]
}

const emit = defineEmits<Emits>()

// 抽屉状态管理
const analysisExpanded = ref(true) // 图像特征分析

// 计算属性
const caption = computed({
  get: () => props.formData.caption,
  set: (value) => emit('update:caption', value),
})

// 切换抽屉状态的方法
const toggleAnalysisExpanded = () => {
  analysisExpanded.value = !analysisExpanded.value
}
</script>

<template>
  <div class="analysis-section card-container drawer-container">
    <div class="section-header drawer-header" @click="toggleAnalysisExpanded">
      <div class="header-left">
        <el-icon class="section-icon"><Search /></el-icon>
        <h3>图像特征分析</h3>
      </div>
      <div class="header-right">
        <el-icon class="expand-icon" :class="{ expanded: analysisExpanded }">
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    <div class="drawer-content" :class="{ expanded: analysisExpanded }">
      <el-form-item prop="caption">
        <TagManager
          v-model="caption"
          type="features"
          title="图像特征"
          placeholder="输入图像特征描述"
          tag-type="success"
          button-type="success"
        />
      </el-form-item>
    </div>
  </div>
</template>

<style scoped>
/* 统一卡片容器样式 */
.card-container {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: var(--shadow-medium);
}

/* 抽屉容器样式 */
.drawer-container {
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 60px; /* 统一折叠状态的最小高度 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 8px;
  margin: -8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.drawer-header:hover {
  background-color: var(--bg-hover);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: var(--text-secondary);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.drawer-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0;
}

.drawer-content.expanded {
  max-height: 500px;
  padding-top: 16px;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-primary);
}

.section-icon {
  font-size: 18px;
  color: var(--medical-blue);
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}
</style>
