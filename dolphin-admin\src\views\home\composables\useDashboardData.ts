// 首页数据管理 Composable

import { ref, reactive, onMounted } from 'vue'
import type {
  StatCardData,
  ClassificationData,
  TrendData,
  RecentDataItem,
  DashboardStats
} from '../types'

// 导入背景图片
import bgImage1 from '@/assets/images/dashborad/index.png'
import bgImage2 from '@/assets/images/dashborad/index2.png'
import bgImage3 from '@/assets/images/dashborad/index3.png'
import bgImage4 from '@/assets/images/dashborad/index4.png'

export function useDashboardData() {
  // 加载状态
  const loading = ref(false)

  // 统计数据
  const stats = reactive<DashboardStats>({
    totalData: 0,
    todayNew: 0,
    pendingProcess: 0,
    modelAccuracy: 0
  })

  // 分类分布数据
  const classificationData = ref<ClassificationData>({
    benign: 0,
    malignant: 0,
    normal: 0,
    undetermined: 0,
    other: 0
  })

  // 趋势数据
  const trendData = ref<TrendData[]>([])

  // 最新数据
  const recentData = ref<RecentDataItem[]>([])

  // 模拟获取统计数据
  const fetchStats = async () => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    stats.totalData = 1247
    stats.todayNew = 23
    stats.pendingProcess = 156
    stats.modelAccuracy = 94.2
  }

  // 模拟获取分类分布数据
  const fetchClassificationData = async () => {
    await new Promise(resolve => setTimeout(resolve, 600))

    classificationData.value = {
      benign: 456,
      malignant: 234,
      normal: 389,
      undetermined: 98,
      other: 70
    }
  }

  // 模拟获取趋势数据
  const fetchTrendData = async () => {
    await new Promise(resolve => setTimeout(resolve, 700))

    const dates = []
    const today = new Date()

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      dates.push({
        date: date.toISOString().split('T')[0],
        count: Math.floor(Math.random() * 50) + 10
      })
    }

    trendData.value = dates
  }

  // 模拟获取最新数据
  const fetchRecentData = async () => {
    await new Promise(resolve => setTimeout(resolve, 500))

    const classifications = ['benign', 'malignant', 'normal', 'undetermined', 'other']
    const details = [
      '良性结节，BRADS 3类，建议定期随访',
      '恶性肿瘤可能，BRADS 5类，建议进一步检查',
      '正常超声表现，未见异常',

    ]

    const recent = []
    for (let i = 0; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * classifications.length)
      recent.push({
        id: `194${Math.random().toString().slice(2, 15)}`,
        classification: classifications[randomIndex],
        detail: details[randomIndex],
        createTime: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
        hasImage: Math.random() > 0.3
      })
    }

    recentData.value = recent
  }

  // 获取统计卡片数据
  const getStatCards = (): StatCardData[] => {
    return [
      {
        title: '总数据量',
        value: stats.totalData,
        icon: 'all',  // 使用自定义SVG图标
        iconType: 'svg',     // 指定为SVG类型
        color: '#409EFF',
        background: `url(${bgImage1}) center/cover, linear-gradient(135deg, rgba(46, 124, 230, 0.8), rgba(30, 91, 184, 0.8))`,

      },
      {
        title: '今日新增',
        value: stats.todayNew,
        icon: 'add',
        iconType: 'svg',     // 指定为SVG类型
        color: '#67C23A',
        background: `url(${bgImage2}) center/cover, linear-gradient(135deg, rgba(0, 212, 170, 0.8), rgba(0, 160, 133, 0.8))`,

      },
      {
        title: '待处理',
        value: stats.pendingProcess,
        icon: 'tet',
        iconType: 'svg',     // 指定为SVG类型
        color: '#E6A23C',
        background: `url(${bgImage3}) center/cover, linear-gradient(135deg, rgba(255, 140, 0, 0.8), rgba(230, 115, 0, 0.8))`,

      },
      {
        title: '模型准确率',
        value: `${stats.modelAccuracy}%`,
        icon: 'databoard',
        iconType: 'svg',     // 指定为SVG类型
        color: '#F56C6C',
        background: `url(${bgImage4}) center/cover, linear-gradient(135deg, rgba(255, 71, 87, 0.8), rgba(230, 57, 70, 0.8))`,

      }
    ]
  }

  // 获取分类分布图表数据
  const getClassificationChartData = () => {
    const data = classificationData.value
    return [
      { name: '良性', value: data.benign, color: '#67C23A' },
      { name: '恶性', value: data.malignant, color: '#F56C6C' },
      { name: '正常', value: data.normal, color: '#409EFF' },
      { name: '未定', value: data.undetermined, color: '#E6A23C' },
      { name: '其他', value: data.other, color: '#909399' }
    ]
  }

  // 刷新所有数据
  const refreshData = async () => {
    loading.value = true
    try {
      await Promise.all([
        fetchStats(),
        fetchClassificationData(),
        fetchTrendData(),
        fetchRecentData()
      ])
    } finally {
      loading.value = false
    }
  }

  // 初始化数据
  onMounted(() => {
    refreshData()
  })

  return {
    loading,
    stats,
    classificationData,
    trendData,
    recentData,
    getStatCards,
    getClassificationChartData,
    refreshData
  }
}
