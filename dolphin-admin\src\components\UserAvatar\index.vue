<template>
  <div class="user-avatar">
    <!-- 头像显示 -->
    <div class="avatar-wrapper" :class="{ clickable: clickable }" @click="handleAvatarClick">
      <el-avatar v-if="avatarUrl" :size="size" :src="avatarUrl" :class="avatarClass" @error="handleAvatarError">
        <template #error>
          <el-icon><User /></el-icon>
        </template>
      </el-avatar>

      <!-- 加载状态 -->
      <el-avatar v-else-if="loading || uploading" :size="size" :class="avatarClass">
        <el-icon class="is-loading"><Loading /></el-icon>
      </el-avatar>

      <!-- 默认头像 -->
      <el-avatar v-else :size="size" :class="avatarClass">
        <el-icon><User /></el-icon>
      </el-avatar>

      <!-- 上传遮罩层 -->
      <div v-if="clickable && !loading && !uploading" class="upload-overlay">
        <el-icon><Camera /></el-icon>
        <span class="upload-text">点击上传</span>
      </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input ref="fileInputRef" type="file" accept="image/*" style="display: none" @change="handleFileChange" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, computed } from 'vue'
import { User, Loading, Camera } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { reqGetAvatar, reqUploadAvatar } from '@/api/user'
import useUserStore from '@/store/modules/user'

// 定义组件名称
defineOptions({
  name: 'UserAvatar',
})

// 获取用户store
const userStore = useUserStore()

// 全局头像缓存，避免重复加载
let globalAvatarCache: Promise<string> | null = null

// 定义组件属性
interface Props {
  /** 头像参数 */
  avatar?: string
  /** 头像大小 */
  size?: number | string
  /** 自定义样式类 */
  class?: string
  /** 是否可点击上传 */
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  avatar: '',
  size: 40,
  class: '',
  clickable: false,
})

// 定义事件
const emit = defineEmits<{
  'upload-success': [avatarId: string]
  'upload-error': [error: Error]
}>()

// 清除头像缓存并重新加载
const clearCacheAndReload = async () => {
  // 清除全局缓存
  globalAvatarCache = null
  // 重新加载头像
  await loadUserAvatar()
}

// 暴露方法给父组件
defineExpose({
  clearCacheAndReload,
})

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const avatarUrl = ref('')
const avatarObjectUrls = ref<string[]>([]) // 用于清理URL对象
const fileInputRef = ref<HTMLInputElement>()

// 计算头像样式类
const avatarClass = computed(() => {
  return ['user-avatar-el', props.class].filter(Boolean).join(' ')
})

// 加载头像（使用全局缓存）
const loadAvatar = async (): Promise<string> => {
  // 检查全局缓存
  if (globalAvatarCache) {
    return await globalAvatarCache
  }

  // 创建加载 Promise 并添加到缓存
  globalAvatarCache = (async () => {
    try {
      const response = await reqGetAvatar()
      // 响应拦截器应该返回 Blob，但类型推断可能有问题，所以做类型断言
      const avatarBlob = response as unknown as Blob
      const url = URL.createObjectURL(avatarBlob)
      avatarObjectUrls.value.push(url) // 记录URL用于清理
      return url
    } catch (error) {
      // 如果加载失败，清除缓存
      globalAvatarCache = null
      console.error('加载头像失败:', error)
      throw error
    }
  })()

  return await globalAvatarCache
}

// 加载头像
const loadUserAvatar = async () => {
  if (!props.avatar) {
    avatarUrl.value = ''
    return
  }

  try {
    loading.value = true
    const url = await loadAvatar()
    avatarUrl.value = url
  } catch (error) {
    console.error('加载用户头像失败:', error)
    avatarUrl.value = ''
  } finally {
    loading.value = false
  }
}

// 处理头像加载错误
const handleAvatarError = () => {
  console.error('头像显示错误')
}

// 处理头像点击
const handleAvatarClick = () => {
  if (props.clickable && !loading.value && !uploading.value) {
    fileInputRef.value?.click()
  }
}

// 处理文件选择
const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小（限制为2MB）
  const maxSize = 2 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('图片大小不能超过2MB')
    return
  }

  try {
    uploading.value = true

    // 检查用户ID是否存在
    if (!userStore.id) {
      ElMessage.error('用户信息不完整，请重新登录')
      return
    }

    // 调用上传接口，传递文件和用户ID
    const avatarId = (await reqUploadAvatar(file, userStore.id)) as unknown as string

    // 清除头像缓存，确保下次加载新头像
    globalAvatarCache = null

    // 上传成功，发送事件
    emit('upload-success', avatarId)

    ElMessage.success('头像上传成功')

    // 清空文件输入框
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
    emit('upload-error', error as Error)
  } finally {
    uploading.value = false
  }
}

// 监听头像参数变化
watch(
  () => props.avatar,
  (newAvatar, oldAvatar) => {
    // 如果头像ID发生变化，清除缓存
    if (newAvatar !== oldAvatar) {
      globalAvatarCache = null
    }
    loadUserAvatar()
  },
  { immediate: true },
)

// 组件卸载时清理URL对象
onUnmounted(() => {
  avatarObjectUrls.value.forEach((url) => {
    URL.revokeObjectURL(url)
  })
  avatarObjectUrls.value = []
})
</script>

<style scoped lang="scss">
.user-avatar {
  display: inline-block;

  .avatar-wrapper {
    position: relative;
    display: inline-block;

    &.clickable {
      cursor: pointer;

      &:hover {
        .upload-overlay {
          opacity: 1;
        }
      }
    }

    .user-avatar-el {
      transition: all 0.3s ease;
    }

    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      color: white;
      font-size: 12px;

      .el-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }

      .upload-text {
        font-size: 10px;
        white-space: nowrap;
      }
    }
  }
}
</style>
