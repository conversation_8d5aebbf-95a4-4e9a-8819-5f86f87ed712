/* 轮播式评分维度样式 */
.dimensions-carousel {
  margin-bottom: 32px;
}

.carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: var(--medical-card);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-blue);
}

.carousel-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.carousel-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--medical-text-dark);
}

.carousel-progress {
  font-size: 16px;
  color: var(--medical-blue);
  font-weight: 500;
  padding: 6px 12px;
  background: rgba(46, 124, 230, 0.1);
  border-radius: 6px;
}

.carousel-controls {
  display: flex;
  gap: 12px;
}

.carousel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px solid var(--medical-blue);
  border-radius: 8px;
  background: var(--medical-card);
  color: var(--medical-blue);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-btn:hover {
  background: var(--medical-blue);
  // color: white;
  box-shadow: 0 4px 12px rgba(46, 124, 230, 0.3);
}

// .carousel-btn.prev {
//   border-color: #6B7280;
//   color: #6B7280;
// }

.carousel-btn.prev:hover {
  background: #6B7280;
  color: white;
}

/* 进度指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--medical-bg);
  border-radius: 12px;
  overflow-x: auto;
}

.indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  text-align: center;
}

.indicator:hover {
  background: rgba(46, 124, 230, 0.05);
}

.indicator.active {
  background: rgba(46, 124, 230, 0.1);
  border: 2px solid var(--medical-blue);
}

.indicator.completed {
  background: rgba(0, 212, 170, 0.1);
  border: 2px solid var(--medical-green);
}

.indicator.completed.active {
  background: rgba(0, 212, 170, 0.15);
  border: 2px solid var(--medical-green);
}

.indicator-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #E5E7EB;
  color: #6B7280;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.indicator.active .indicator-icon {
  background: var(--medical-blue);
  color: white;
}

.indicator.completed .indicator-icon {
  background: var(--medical-green);
  color: white;
}

.indicator-label {
  font-size: 12px;
  color: var(--medical-text-light);
  font-weight: 500;
  line-height: 1.2;
}

.indicator.active .indicator-label {
  color: var(--medical-blue);
  font-weight: 600;
}

.indicator.completed .indicator-label {
  color: var(--medical-green);
  font-weight: 600;
}

/* 轮播容器 */
.carousel-container {
  overflow: hidden;
  border-radius: 16px;
  background: var(--medical-card);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.carousel-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-slide {
  width: 100%;
  flex-shrink: 0;
}

/* 右侧结果预览区域 */
.right-panel {
  width: 400px;
  flex-shrink: 0;
}

.sticky-content {
  position: sticky;
  top: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 进度卡片 */
.progress-card {
  background: var(--medical-card);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-green);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.progress-header .el-icon {
  color: var(--medical-green);
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--medical-blue);
}

.progress-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  font-size: 13px;
  color: var(--medical-text-light);
}

.progress-tips .el-icon {
  color: var(--medical-blue);
}

/* 智能建议卡片 */
.suggestions-card {
  background: var(--medical-card);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-orange);
}

.suggestions-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.suggestions-header .el-icon {
  color: var(--medical-orange);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.suggestion-item.warning {
  background: #FFF7ED;
  border: 1px solid #FBBF24;
  color: #92400E;
}

.suggestion-item.success {
  background: #F0FDF4;
  border: 1px solid #34D399;
  color: #065F46;
}

.suggestion-item.info {
  background: #EFF6FF;
  border: 1px solid #60A5FA;
  color: #1E40AF;
}

.suggestion-item .el-icon {
  margin-top: 1px;
  flex-shrink: 0;
}