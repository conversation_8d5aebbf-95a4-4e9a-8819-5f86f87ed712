<template>
  <div class="empty-state">
    <!-- 背景装饰 -->
    <div class="empty-bg-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="empty-content">
      <!-- 图标区域 -->
      <div class="empty-icon-container">
        <div class="icon-wrapper">
          <el-icon class="main-icon">
            <Picture />
          </el-icon>
          <div class="icon-pulse"></div>
        </div>
      </div>

      <!-- 文字内容 -->
      <div class="empty-text-content">
        <h2 class="empty-title">{{ title }}</h2>
        <p class="empty-description">{{ description }}</p>

        <!-- 功能特点 -->
        <div class="feature-list" v-if="features.length > 0">
          <div v-for="feature in features" :key="feature" class="feature-item">
            <el-icon class="feature-icon"><Check /></el-icon>
            <span>{{ feature }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Picture, Check } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'EmptyStateDisplay',
})

// 定义 Props
const props = defineProps({
  title: {
    type: String,
    default: '开始您的医学影像查看',
  },
  description: {
    type: String,
    default: '上传医学图像进行查看和分析',
  },
  features: {
    type: Array,
    default: () => ['支持多种医学影像格式', '高清图像显示', '便捷的图像管理'],
  },
})
</script>

<style scoped>
/* 空状态样式 */
.empty-state {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--bg-primary);
  overflow: hidden;
}

/* 背景装饰 */
.empty-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(74, 144, 226, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 主要内容 */
.empty-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  max-width: 500px;
  text-align: center;
}

/* 图标容器 */
.empty-icon-container {
  position: relative;
}

.icon-wrapper {
  position: relative;
  display: inline-block;
}

.main-icon {
  font-size: 5rem;
  color: var(--medical-blue);
  filter: drop-shadow(0 4px 12px rgba(74, 144, 226, 0.3));
  animation: pulse-icon 2s ease-in-out infinite;
}

.icon-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border: 2px solid var(--medical-blue);
  border-radius: 50%;
  opacity: 0;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-icon {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}

/* 文字内容 */
.empty-text-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.empty-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.empty-description {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 功能特点列表 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: var(--bg-hover);
  border-color: var(--medical-blue);
  transform: translateX(4px);
}

.feature-icon {
  color: var(--medical-green);
  font-size: 1.1rem;
  flex-shrink: 0;
}

.feature-item span {
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
}
</style>
