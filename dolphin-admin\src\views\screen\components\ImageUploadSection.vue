<script setup lang="ts">
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'
import type { MedicalFormData } from '../types'

// Props 定义
interface Props {
  formData: MedicalFormData
  examPartOptions: Array<{ label: string; value: string }>
  sliceTypeOptions: Array<{ label: string; value: string }>
}

const props = defineProps<Props>()

// Events 定义
interface Emits {
  'update:imageFile': [file: File | null]
  'update:imagePreview': [preview: string]
  'update:fileName': [name: string]
  'update:examPart': [part: string]
  'update:sliceType': [type: string]
  'upload-success': [file: File, result: { imageId: string }]
  'upload-error': [error: Error]
}

const emit = defineEmits<Emits>()

// 计算属性
const imageFile = computed({
  get: () => props.formData.imageFile,
  set: (value) => emit('update:imageFile', value),
})

const imagePreview = computed({
  get: () => props.formData.imagePreview,
  set: (value) => emit('update:imagePreview', value),
})

const fileName = computed({
  get: () => props.formData.fileName,
  set: (value) => emit('update:fileName', value),
})

const examPart = computed({
  get: () => props.formData.examPart,
  set: (value) => emit('update:examPart', value),
})

const sliceType = computed({
  get: () => props.formData.sliceType,
  set: (value) => emit('update:sliceType', value),
})

// 事件处理
const handleUploadSuccess = (file: File, result: { imageId: string }) => {
  emit('upload-success', file, result)
}

const handleUploadError = (error: Error) => {
  emit('upload-error', error)
}
</script>

<template>
  <div class="upload-section card-container">
    <div class="section-header">
      <el-icon class="section-icon"><Picture /></el-icon>
      <h3>图像上传</h3>
    </div>
    <el-form-item prop="imageFile">
      <ImageUploader
        v-model="imageFile"
        v-model:image-preview="imagePreview"
        v-model:file-name="fileName"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
      />
    </el-form-item>

    <!-- 检查部位和切面类型下拉框 -->
    <div class="dropdown-section">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="检查部位" prop="examPart" label-width="80px">
            <el-select v-model="examPart" placeholder="请选择检查部位" style="width: 100%">
              <el-option
                v-for="option in examPartOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="切面类型" prop="sliceType" label-width="80px">
            <el-select v-model="sliceType" placeholder="请选择切面类型" style="width: 100%">
              <el-option
                v-for="option in sliceTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
/* 统一卡片容器样式 */
.card-container {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: var(--shadow-medium);
}

/* 图像上传区域 */
.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-section .el-form-item {
  margin-bottom: 0;
  flex: 1;
}

/* 下拉框区域样式 */
.dropdown-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-secondary);
}

.dropdown-section .el-form-item {
  margin-bottom: 0;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-primary);
}

.section-icon {
  font-size: 18px;
  color: #409eff;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}
</style>
