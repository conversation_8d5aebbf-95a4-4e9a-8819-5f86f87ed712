<template>
  <el-card class="chart-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">分类分布</span>
        <el-button link size="small" @click="refreshChart">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    <div ref="chartRef" class="chart-container"></div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { ChartData } from '../types'

interface Props {
  data: ChartData[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  refresh: []
}>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  // 获取当前主题的边框颜色
  const isDarkTheme = document.documentElement.classList.contains('dark-theme')
  const borderColor = isDarkTheme ? '#404040' : '#f0f0f0'
  const textColor = isDarkTheme ? '#ffffff' : '#303133'

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: isDarkTheme ? '#2d2d2d' : '#ffffff',
      borderColor: isDarkTheme ? '#404040' : '#e4e7ed',
      textStyle: {
        color: textColor,
      },
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        fontSize: 12,
        color: textColor,
      },
    },
    series: [
      {
        name: '数据分类',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: borderColor,
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            color: textColor,
          },
        },
        labelLine: {
          show: false,
        },
        data: props.data.map((item) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
          },
        })),
      },
    ],
  }

  chartInstance.setOption(option)
}

const refreshChart = () => {
  emit('refresh')
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true },
)

watch(
  () => props.loading,
  (loading) => {
    if (chartInstance) {
      if (loading) {
        chartInstance.showLoading()
      } else {
        chartInstance.hideLoading()
      }
    }
  },
)

// 监听主题变化
const themeObserver = new MutationObserver(() => {
  updateChart()
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)

  // 监听主题变化
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
  themeObserver.disconnect()
})
</script>

<style scoped lang="scss">
.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-container {
  width: 100%;
  height: 320px;
}
</style>
