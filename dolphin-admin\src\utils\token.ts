/**
 * 令牌管理工具
 *
 * 功能：
 * - 访问令牌(Access Token)管理
 * - 刷新令牌(Refresh Token)管理
 * - 令牌过期时间管理
 * - 本地存储封装
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// ==================== 访问令牌管理 ====================

/**
 * 设置访问令牌
 * @param token 访问令牌
 */
export const SET_TOKEN = (token: string) => {
  localStorage.setItem('Authorization', token)
}

/**
 * 获取访问令牌
 * @returns 访问令牌或null
 */
export const GET_TOKEN = () => {
  return localStorage.getItem('Authorization')
}

/**
 * 移除访问令牌
 */
export const REMOVE_TOKEN = () => {
  localStorage.removeItem('Authorization')
}

// ==================== 刷新令牌管理 ====================

/**
 * 设置刷新令牌
 * @param refreshToken 刷新令牌
 */
export const SET_REFRESH_TOKEN = (refreshToken: string) => {
  localStorage.setItem('RefreshToken', refreshToken)
}

/**
 * 获取刷新令牌
 * @returns 刷新令牌或null
 */
export const GET_REFRESH_TOKEN = () => {
  return localStorage.getItem('RefreshToken')
}

/**
 * 移除刷新令牌
 */
export const REMOVE_REFRESH_TOKEN = () => {
  localStorage.removeItem('RefreshToken')
}

// ==================== 令牌过期时间管理 ====================

/**
 * 设置令牌过期时间
 * @param expiresAt 过期时间戳
 */
export const SET_TOKEN_EXPIRES = (expiresAt: number) => {
  localStorage.setItem('TokenExpires', expiresAt.toString())
}

/**
 * 获取令牌过期时间
 * @returns 过期时间戳或null
 */
export const GET_TOKEN_EXPIRES = () => {
  const expires = localStorage.getItem('TokenExpires')
  return expires ? parseInt(expires) : null
}

/**
 * 移除令牌过期时间
 */
export const REMOVE_TOKEN_EXPIRES = () => {
  localStorage.removeItem('TokenExpires')
}

/**
 * 检查令牌是否过期
 * @param buffer 缓冲时间（秒），默认60秒
 * @returns true表示已过期或即将过期
 */
export const IS_TOKEN_EXPIRED = (buffer: number = 60) => {
  const expires = GET_TOKEN_EXPIRES()
  // 如果没有过期时间信息，认为token仍然有效
  // 让后端来决定token是否过期（通过401响应）
  if (!expires) return false

  const now = Math.floor(Date.now() / 1000)
  return now >= (expires - buffer)
}

// ==================== 用户信息管理 ====================

/**
 * 设置用户信息
 * @param userInfo 用户信息对象
 */
export const SET_USER_INFO = (userInfo: any) => {
  localStorage.setItem('UserInfo', JSON.stringify(userInfo))
}

/**
 * 获取用户信息
 * @returns 用户信息对象或null
 */
export const GET_USER_INFO = () => {
  const userInfo = localStorage.getItem('UserInfo')
  return userInfo ? JSON.parse(userInfo) : null
}

/**
 * 移除用户信息
 */
export const REMOVE_USER_INFO = () => {
  localStorage.removeItem('UserInfo')
}

// ==================== 批量操作 ====================

/**
 * 清除所有令牌相关数据
 */
export const CLEAR_ALL_TOKENS = () => {
  REMOVE_TOKEN()
  REMOVE_REFRESH_TOKEN()
  REMOVE_TOKEN_EXPIRES()
  REMOVE_USER_INFO()
}

/**
 * 设置完整的令牌信息
 * @param token 访问令牌
 * @param refreshToken 刷新令牌
 * @param expiresAt 过期时间戳（可选）
 */
export const SET_TOKEN_INFO = (token: string, refreshToken: string, expiresAt?: number | null) => {
  SET_TOKEN(token)
  SET_REFRESH_TOKEN(refreshToken)
  if (expiresAt) {
    SET_TOKEN_EXPIRES(expiresAt)
  }
}
