<template>
  <el-card class="stat-card" :body-style="{ padding: '20px' }" :style="{ background: data.background || '#ffffff' }">
    <div class="stat-content">
      <div class="stat-info">
        <div class="stat-title" :class="{ 'white-text': data.background }">{{ data.title }}</div>
        <div class="stat-value" :class="{ 'white-text': data.background }">{{ data.value }}</div>
        <div v-if="data.trend" class="stat-trend">
          <el-icon :class="['trend-icon', data.trend.isUp ? 'trend-up' : 'trend-down']">
            <ArrowUp v-if="data.trend.isUp" />
            <ArrowDown v-else />
          </el-icon>
          <span :class="['trend-text', data.trend.isUp ? 'trend-up' : 'trend-down', { 'white-text': data.background }]">
            {{ data.trend.value }}%
          </span>
        </div>
      </div>
      <div
        class="stat-icon"
        :style="{
          backgroundColor: data.background ? 'rgba(255, 255, 255, 0.2)' : `rgba(${hexToRgb(data.color)}, 0.1)`,
        }"
      >
        <!-- 如果是自定义SVG图标，使用SvgIcon组件 -->
        <SvgIcon
          v-if="data.iconType === 'svg'"
          :name="data.icon"
          :color="data.background ? '#ffffff' : data.color"
          width="32px"
          height="32px"
        />
        <!-- 否则使用Element Plus图标 -->
        <el-icon v-else :style="{ color: data.background ? '#ffffff' : data.color, fontSize: '32px' }">
          <component :is="data.icon" />
        </el-icon>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import type { StatCardData } from '../types'

interface Props {
  data: StatCardData
}

defineProps<Props>()

// 将十六进制颜色转换为RGB
const hexToRgb = (hex: string): string => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (result) {
    const r = parseInt(result[1], 16)
    const g = parseInt(result[2], 16)
    const b = parseInt(result[3], 16)
    return `${r}, ${g}, ${b}`
  }
  return '64, 158, 255' // 默认蓝色
}
</script>

<style scoped lang="scss">
.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  position: relative;

  // 确保背景图片正确显示
  :deep(.el-card__body) {
    height: 100%;
    background: transparent;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  // 为背景图片添加遮罩效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
    pointer-events: none;
  }
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 2;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

  &.white-text {
    color: rgba(255, 255, 255, 0.95);
    font-weight: 500;
  }
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

  &.white-text {
    color: #ffffff;
    font-weight: 700;
  }
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.trend-icon {
  margin-right: 4px;
}

.trend-up {
  color: #67c23a;

  &.white-text {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.trend-down {
  color: #f56c6c;

  &.white-text {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
</style>
