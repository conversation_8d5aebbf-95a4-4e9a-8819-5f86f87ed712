<template>
  <el-card
    class="modern-panel"
    :class="[`modern-panel--${size}`, `modern-panel--${position}`, { 'modern-panel--bordered': bordered }]"
    :shadow="shadow"
    :body-style="{ padding: '0' }"
  >
    <!-- 面板头部 -->
    <template #header v-if="title || $slots.header">
      <div class="modern-panel__header">
        <div class="modern-panel__title" v-if="title">
          <el-icon v-if="icon" class="modern-panel__icon">
            <component :is="icon" />
          </el-icon>
          {{ title }}
        </div>
        <slot name="header" />
        <div class="modern-panel__actions" v-if="$slots.actions">
          <slot name="actions" />
        </div>
      </div>
    </template>

    <!-- 面板内容 -->
    <div class="modern-panel__content" :class="{ 'modern-panel__content--scrollable': scrollable }">
      <slot />
    </div>
  </el-card>
</template>

<script setup>
import { defineProps, defineSlots } from 'vue'

defineProps({
  // 面板标题
  title: {
    type: String,
    default: '',
  },
  // 标题图标
  icon: {
    type: [String, Object],
    default: null,
  },
  // 面板尺寸
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value),
  },
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: true,
  },
  // 阴影效果
  shadow: {
    type: String,
    default: 'never',
    validator: (value) => ['always', 'hover', 'never'].includes(value),
  },
  // 内容是否可滚动
  scrollable: {
    type: Boolean,
    default: true,
  },
  // 面板位置（控制圆角方向）
  position: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value),
  },
})

defineSlots()
</script>

<style scoped>
.modern-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
}

.modern-panel--left {
  border-radius: 16px 0 0 16px;
}

.modern-panel--right {
  border-radius: 0 16px 16px 0;
}

.modern-panel:hover {
  box-shadow: var(--shadow-heavy);
  /* transform: translateY(-1px); */
}

.modern-panel--small {
  min-width: 200px;
}

.modern-panel--default {
  min-width: 280px;
}

.modern-panel--large {
  min-width: 400px;
}

.modern-panel--bordered {
  border: 2px solid var(--border-primary);
  box-shadow: var(--shadow-heavy);
}

/* 头部样式 */
.modern-panel__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.75rem;
  margin-left: -35px;
  margin-bottom: -20px;
  /* background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); */
  /* border-bottom: 2px solid #e2e8f0; */
}

.modern-panel__header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 100%);
}

.modern-panel__title {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  margin-top: -20px;
}

.modern-panel__icon {
  color: var(--medical-blue);
  font-size: 1.25rem;
}

.modern-panel__actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 内容样式 */
.modern-panel__content {
  flex: 1;
  padding: 0;
  background: var(--bg-primary);
  position: relative;
  padding: 0.75rem;
}

.modern-panel__content--scrollable {
  overflow-y: auto;
}

.modern-panel__content--scrollable::-webkit-scrollbar {
  width: 6px;
}

.modern-panel__content--scrollable::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 3px;
}

.modern-panel__content--scrollable::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 3px;
}

.modern-panel__content--scrollable::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-panel__header {
    padding: 1rem 1.25rem;
  }

  .modern-panel__content {
    padding: 1.25rem;
  }

  .modern-panel__title {
    font-size: 1rem;
  }

  .modern-panel__icon {
    font-size: 1.1rem;
  }
}
</style>
