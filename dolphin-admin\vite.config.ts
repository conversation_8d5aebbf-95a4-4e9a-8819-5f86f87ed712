import { defineConfig, loadEnv } from 'vite'
import type { ConfigEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import removeConsole from 'vite-plugin-remove-console'
// 图片优化插件（需要安装）
import ViteImageOptimize  from 'vite-plugin-imagemin'

// https://vite.dev/config/
export default defineConfig((configEnv: ConfigEnv) => {
  const { mode } = configEnv
  // 获取各种环境下对应的变量
  const env = loadEnv(mode, process.cwd())
  return {
    plugins: [
      vue(),
      createSvgIconsPlugin({
        // Specify the icon folder to be cached
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
        // Specify symbolId format
        symbolId: 'icon-[dir]-[name]',
      }),
      // 只在生产环境中移除 console
      mode === 'production' && removeConsole(),
      // 图片优化插件（生产环境）
      mode === 'production' && ViteImageOptimize({
        gifsicle: { optimizationLevel: 7, interlaced: false },
        mozjpeg: { quality: 80 },
        optipng: { optimizationLevel: 7 },
        pngquant: { quality: [0.65, 0.8], speed: 4 },
        svgo: {
          plugins: [
            { name: 'removeViewBox', active: false },
            { name: 'removeEmptyAttrs', active: false }
          ]
        }
      }),
    ].filter(Boolean),
    resolve: {
      alias: {
        '@': path.resolve('./src'), // 相对路径别名配置，使用 @ 代替 src
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/styles/variable.scss" as *;',
        },
      },
    },
    // 构建优化配置
    build: {
      // 静态资源处理
      assetsDir: 'assets',
      // 小于此阈值的导入或引用资源将内联为 base64 编码
      assetsInlineLimit: 4096, // 4kb
      // 启用/禁用 CSS 代码拆分
      cssCodeSplit: true,
      // 构建后是否生成 source map 文件
      sourcemap: mode === 'development',
      // 设置最终构建的浏览器兼容目标
      target: 'es2015',
      // Rollup 打包配置
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const fileName = assetInfo.names?.[0] || assetInfo.name || ''
            const info = fileName.split('.') || []
            let extType = info[info.length - 1]
            // 图片资源
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(fileName)) {
              extType = 'media'
            } else if (/\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(fileName)) {
              extType = 'images'
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(fileName)) {
              extType = 'fonts'
            }
            return `assets/${extType}/[name]-[hash].[ext]`
          },
          // 代码分割
          manualChunks: {
            // 将 Vue 相关库打包到一个 chunk
            vue: ['vue', 'vue-router', 'pinia'],
            // 将 Element Plus 打包到一个 chunk
            elementPlus: ['element-plus'],
            // 将工具库打包到一个 chunk
            utils: ['axios', 'dayjs', 'nprogress']
          }
        }
      },
      // 压缩配置
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境移除 console
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      }
    },
    server: {
      host: "0.0.0.0", // 允许外部访问
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_SERVE,
          changeOrigin: true,
        },
      },
    },
  }
})
