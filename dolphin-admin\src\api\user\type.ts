// ==================== 认证相关类型定义 ====================

/**
 * 登录请求参数
 */
export interface loginForm {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码（可选） */
  captcha?: string
  /** 记住我（可选） */
  rememberMe?: boolean
}

/**
 * 登录响应数据
 */
export interface loginResponse {
  /** 令牌类型 */
  tokenType: string
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken: string
}

/**
 * 登出请求参数
 */
export interface logoutForm {
  /** 刷新令牌（可选，用于服务端清理） */
  refreshToken?: string
}

/**
 * 登出响应数据
 */
export interface logoutResponse {
  /** 操作结果消息 */
  message: string
}

/**
 * 刷新令牌请求参数
 */
export interface refreshTokenForm {
  /** 刷新令牌 */
  refreshToken: string
}

/**
 * 刷新令牌响应数据
 */
export interface refreshTokenResponse {
  /** 令牌类型 */
  tokenType: string
  /** 新的访问令牌 */
  accessToken: string
  /** 新的刷新令牌 */
  refreshToken: string
}

// ==================== 用户信息相关类型定义 ====================

/**
 * 用户详细信息
 */
export interface userInfo {
  /** 用户ID */
  id: number
  avatar: string
  username: string
  nickname?: string
  /** 性别：1-男，0-女，-1-未知 */
  sex?: number
  email?: string
  phone?: string
  desc?: string
  roles: string[]
  buttons: string[]
  routes: string[]
  /** 账户状态：1-正常，0-禁用 */
  status: number
  /** 创建时间 */
  createTime?: string
  /** 最后登录时间 */
  lastLoginTime?: string
}

/**
 * 获取用户信息响应数据
 */
export interface userResponseData {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname?: string
  /** 性别：1-男，0-女，-1-未知 */
  sex?: number
  /** 头像 */
  avatar?: string
  /** 手机号 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 用户角色（可选，某些接口可能不返回） */
  roles?: string[]
  /** 按钮权限（可选，某些接口可能不返回） */
  buttons?: string[]
  /** 路由权限（可选，某些接口可能不返回） */
  routes?: string[]
  /** 账户状态（可选，某些接口可能不返回） */
  status?: number
}

/**
 * 更新用户信息请求参数
 */
export interface updateProfileForm {
  nickname?: string
  /** 性别：1-男，0-女，-1-未知 */
  sex?: number
  email?: string
  phone?: string
  avatar?: string
  desc?: string
}

/**
 * 修改用户信息请求参数（包含用户ID）
 */
export interface editUserInfoForm {
  /** 用户ID */
  id: number
  nickname?: string
  /** 性别：1-男，0-女，-1-未知 */
  sex?: number
  email?: string
  phone?: string
}

/**
 * 修改密码请求参数
 */
export interface changePasswordForm {
  /** 当前密码 */
  oldPassword: string
  /** 新密码 */
  newPassword: string
  /** 确认新密码 */
  confirmPassword: string
}
