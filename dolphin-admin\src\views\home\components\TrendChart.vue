<template>
  <el-card class="chart-card">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <span class="card-title">
            <el-icon><TrendCharts /></el-icon>
            数据趋势分析
          </span>
          <div class="time-range-selector">
            <el-radio-group v-model="timeRange" size="small" @change="handleTimeRangeChange">
              <el-radio-button label="7d">7天</el-radio-button>
              <el-radio-button label="30d">30天</el-radio-button>
              <el-radio-button label="90d">90天</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="header-right">
          <div class="chart-controls">
            <el-tooltip content="切换图表类型" placement="top">
              <el-button link size="small" @click="toggleChartType">
                <el-icon><Switch /></el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip content="刷新数据" placement="top">
              <el-button link size="small" @click="refreshChart">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </template>

    <div ref="chartRef" class="chart-container"></div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Refresh, TrendCharts, Switch } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { TrendData } from '../types'

interface Props {
  data: TrendData[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  refresh: []
}>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 新增的响应式数据
const timeRange = ref('7d')
const chartType = ref<'line' | 'bar'>('line')

// 生成模拟的多维数据
const generateMultiSeriesData = () => {
  const days = timeRange.value === '7d' ? 7 : timeRange.value === '30d' ? 30 : 90
  const dates = []
  const newData = []
  const processedData = []
  const accuracyData = []

  const today = new Date()
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    dates.push(date.toISOString().split('T')[0])

    // 生成模拟数据
    const baseValue = Math.floor(Math.random() * 30) + 20
    newData.push(baseValue + Math.floor(Math.random() * 20))
    processedData.push(baseValue + Math.floor(Math.random() * 15))
    accuracyData.push(85 + Math.random() * 10) // 85-95%的准确率
  }

  return { dates, newData, processedData, accuracyData }
}

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  // 获取当前主题的颜色
  const isDarkTheme = document.documentElement.classList.contains('dark-theme')
  const textColor = isDarkTheme ? '#ffffff' : '#303133'
  const secondaryTextColor = isDarkTheme ? '#b3b3b3' : '#666'
  const borderColor = isDarkTheme ? '#404040' : '#e4e7ed'
  const splitLineColor = isDarkTheme ? '#404040' : '#f5f7fa'
  const tooltipBgColor = isDarkTheme ? '#2d2d2d' : '#ffffff'

  const multiData = generateMultiSeriesData()
  const { dates, newData, processedData, accuracyData } = multiData

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }

  const formattedDates = dates.map(formatDate)

  const option = {
    title: {
      text: `${timeRange.value === '7d' ? '近7天' : timeRange.value === '30d' ? '近30天' : '近90天'}数据趋势`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: textColor,
        fontWeight: 'normal',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: tooltipBgColor,
      borderColor: borderColor,
      textStyle: {
        color: textColor,
      },
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: isDarkTheme ? '#555555' : '#6a7985',
        },
      },
      formatter: function (params: any) {
        let result = `<div style="margin-bottom: 5px; color: ${textColor};">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const value = param.seriesName === '模型准确率' ? `${param.value.toFixed(1)}%` : param.value
          result += `<div style="display: flex; align-items: center; margin-bottom: 3px; color: ${textColor};">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="flex: 1;">${param.seriesName}:</span>
            <span style="font-weight: bold; margin-left: 10px;">${value}</span>
          </div>`
        })
        return result
      },
    },
    legend: {
      data: ['新增数据', '已处理数据', '模型准确率'],
      top: 30,
      textStyle: {
        fontSize: 12,
        color: secondaryTextColor,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '20%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: chartType.value === 'bar',
        data: formattedDates,
        axisLabel: {
          fontSize: 11,
          color: secondaryTextColor,
          rotate: timeRange.value === '90d' ? 45 : 0,
        },
        axisLine: {
          lineStyle: {
            color: borderColor,
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数据量',
        position: 'left',
        axisLabel: {
          fontSize: 11,
          color: secondaryTextColor,
        },
        axisLine: {
          lineStyle: {
            color: borderColor,
          },
        },
        splitLine: {
          lineStyle: {
            color: splitLineColor,
          },
        },
      },
      {
        type: 'value',
        name: '准确率(%)',
        position: 'right',
        min: 80,
        max: 100,
        axisLabel: {
          fontSize: 11,
          color: secondaryTextColor,
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: borderColor,
          },
        },
      },
    ],
    series: [
      {
        name: '新增数据',
        type: chartType.value,
        smooth: chartType.value === 'line',
        data: newData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#79bbff' },
          ]),
        },
        areaStyle:
          chartType.value === 'line'
            ? {
                opacity: 0.3,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
                ]),
              }
            : undefined,
        lineStyle: chartType.value === 'line' ? { width: 3 } : undefined,
        showSymbol: chartType.value === 'line' ? false : true,
        emphasis: {
          focus: 'series',
        },
      },
      {
        name: '已处理数据',
        type: chartType.value,
        smooth: chartType.value === 'line',
        data: processedData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#67C23A' },
            { offset: 1, color: '#95d475' },
          ]),
        },
        areaStyle:
          chartType.value === 'line'
            ? {
                opacity: 0.3,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                  { offset: 1, color: 'rgba(103, 194, 58, 0.1)' },
                ]),
              }
            : undefined,
        lineStyle: chartType.value === 'line' ? { width: 3 } : undefined,
        showSymbol: chartType.value === 'line' ? false : true,
        emphasis: {
          focus: 'series',
        },
      },
      {
        name: '模型准确率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        data: accuracyData,
        itemStyle: {
          color: '#E6A23C',
        },
        lineStyle: {
          width: 2,
          type: 'dashed',
        },
        showSymbol: true,
        symbolSize: 6,
        emphasis: {
          focus: 'series',
        },
      },
    ],
    dataZoom:
      timeRange.value === '90d'
        ? [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 70,
              end: 100,
              height: 20,
              bottom: 10,
            },
          ]
        : undefined,
  }

  chartInstance.setOption(option, true)
}

const refreshChart = () => {
  emit('refresh')
}

// 新增的方法
const handleTimeRangeChange = () => {
  updateChart()
}

const toggleChartType = () => {
  chartType.value = chartType.value === 'line' ? 'bar' : 'line'
  updateChart()
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true },
)

watch(
  () => props.loading,
  (loading) => {
    if (chartInstance) {
      if (loading) {
        chartInstance.showLoading()
      } else {
        chartInstance.hideLoading()
      }
    }
  },
)

// 监听主题变化
const themeObserver = new MutationObserver(() => {
  updateChart()
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)

  // 监听主题变化
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
  themeObserver.disconnect()
})
</script>

<style scoped lang="scss">
.chart-card {
  height: 500px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-card__body) {
    padding: 0;
    height: calc(100% - 57px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);

  .el-icon {
    color: #409eff;
  }
}

.time-range-selector {
  :deep(.el-radio-group) {
    .el-radio-button__inner {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 4px;

  .el-button {
    padding: 6px;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.chart-container {
  width: 100%;
  height: calc(100% - 57px);
  padding: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .chart-card {
    height: 450px;
  }

  .card-header {
    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }

  .chart-container {
    height: calc(100% - 57px);
  }
}
</style>
