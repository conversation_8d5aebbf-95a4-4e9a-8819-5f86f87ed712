<template>
  <div class="overall-comment-card">
    <div class="card-header">
      <h3>
        <el-icon><ChatDotRound /></el-icon>
        总体评价
      </h3>
      <p class="card-subtitle">请提供对AI诊断结果的综合评价和专业建议</p>
    </div>

    <el-form-item label="" required>
      <el-input
        :model-value="comment"
        type="textarea"
        :rows="5"
        placeholder="请从医学专业角度提供总体评价：&#10;1. AI回答的整体质量如何？&#10;2. 是否符合临床实践标准？&#10;3. 有哪些需要改进的地方？&#10;4. 对患者的指导价值如何？"
        maxlength="500"
        show-word-limit
        @input="handleCommentChange"
        class="comment-textarea"
      />
    </el-form-item>

    <!-- 评价提示 -->
    <div class="evaluation-tips">
      <div class="tip-header">
        <el-icon><InfoFilled /></el-icon>
        <span>评价要点提示</span>
      </div>
      <ul class="tip-list">
        <li>从医学准确性、临床实用性角度评价</li>
        <li>指出具体的优点和不足之处</li>
        <li>提供具体的改进建议</li>
        <li>考虑对患者的指导价值</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChatDotRound, InfoFilled } from '@element-plus/icons-vue'

// Props
interface Props {
  comment: string
}

defineProps<Props>()

// Events
interface Emits {
  (e: 'comment-change', comment: string): void
}

const emit = defineEmits<Emits>()

// 方法
const handleCommentChange = (comment: string) => {
  emit('comment-change', comment)
}
</script>

<style scoped>
@import '../styles/medical-theme.scss';

/* 总体评论卡片 */
.overall-comment-card {
  background: var(--medical-card);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid var(--medical-orange);
  margin-bottom: 24px;
}

.card-header {
  margin-bottom: 20px;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--medical-text-dark);
}

.card-header .el-icon {
  color: var(--medical-orange);
  font-size: 20px;
}

.card-subtitle {
  margin: 0;
  color: var(--medical-text-light);
  font-size: 14px;
  line-height: 1.5;
}

/* 评论输入框 */
:deep(.comment-textarea .el-textarea__inner) {
  border-radius: 10px;
  border: 2px solid #E4E7ED;
  transition: all 0.3s ease;
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
  background: #FAFBFC;
}

:deep(.comment-textarea .el-textarea__inner:focus) {
  border-color: var(--medical-orange);
  box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.1);
  background: var(--medical-card);
}

:deep(.comment-textarea .el-input__count) {
  background: transparent;
  color: var(--medical-text-light);
  font-size: 12px;
  bottom: 8px;
  right: 12px;
}

/* 评价提示 */
.evaluation-tips {
  margin-top: 20px;
  padding: 16px;
  background: #FFF9E6;
  border-radius: 8px;
  border-left: 3px solid var(--medical-orange);
}

.tip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--medical-text-dark);
  font-size: 14px;
}

.tip-header .el-icon {
  color: var(--medical-orange);
}

.tip-list {
  margin: 0;
  padding-left: 20px;
  color: var(--medical-text-light);
  font-size: 13px;
  line-height: 1.6;
}

.tip-list li {
  margin-bottom: 4px;
}

.tip-list li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overall-comment-card {
    padding: 16px;
  }

  .card-header h3 {
    font-size: 16px;
  }

  .card-subtitle {
    font-size: 13px;
  }

  :deep(.comment-textarea .el-textarea__inner) {
    padding: 12px;
    font-size: 13px;
  }

  .evaluation-tips {
    padding: 12px;
  }

  .tip-list {
    font-size: 12px;
  }
}
</style>
