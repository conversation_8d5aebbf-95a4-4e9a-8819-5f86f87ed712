<script setup lang="ts">
import { ref, computed } from 'vue'
import { Medal, Search, Document, InfoFilled, ArrowRight } from '@element-plus/icons-vue'
import { TagManager } from './index'
import type { MedicalFormData } from '../types'

// Props 定义
interface Props {
  formData: MedicalFormData
}

const props = defineProps<Props>()

// Events 定义
interface Emits {
  'update:classification': [classification: string]
  'update:detail': [detail: string]
  'update:remark': [remark: string]
  'update:associate': [associate: string[]]
  'update:basis': [basis: string[]]
}

const emit = defineEmits<Emits>()

// 抽屉状态管理
const diagnosisExpanded = ref(true) // 诊断结果
const associateExpanded = ref(true) // 诊断联想
const basisExpanded = ref(true) // 诊断依据

// 计算属性
const classification = computed({
  get: () => props.formData.classification,
  set: (value) => emit('update:classification', value),
})

const detail = computed({
  get: () => props.formData.detail,
  set: (value) => emit('update:detail', value),
})

const remark = computed({
  get: () => props.formData.remark,
  set: (value) => emit('update:remark', value),
})

const associate = computed({
  get: () => props.formData.associate,
  set: (value) => emit('update:associate', value),
})

const basis = computed({
  get: () => props.formData.basis,
  set: (value) => emit('update:basis', value),
})

// 控制诊断依据显示的计算属性
const showDiagnosisBasis = computed(() => {
  return (
    classification.value === 'benign' ||
    classification.value === 'malignant' ||
    classification.value === 'uncertain' ||
    classification.value === 'other'
  )
})

// 控制诊断联想显示的计算属性
const showDiagnosisAssociate = computed(() => {
  return (
    classification.value === 'benign' ||
    classification.value === 'malignant' ||
    classification.value === 'uncertain' ||
    classification.value === 'other'
  )
})

// 切换抽屉状态的方法
const toggleDiagnosisExpanded = () => {
  diagnosisExpanded.value = !diagnosisExpanded.value
}

const toggleAssociateExpanded = () => {
  associateExpanded.value = !associateExpanded.value
}

const toggleBasisExpanded = () => {
  basisExpanded.value = !basisExpanded.value
}
</script>

<template>
  <div class="diagnosis-container">
    <!-- 诊断结果 -->
    <div class="diagnosis-classification-section card-container drawer-container">
      <div class="section-header drawer-header" @click="toggleDiagnosisExpanded">
        <div class="header-left">
          <el-icon class="section-icon"><Medal /></el-icon>
          <h3>诊断结果</h3>
        </div>
        <div class="header-right">
          <el-icon class="expand-icon" :class="{ expanded: diagnosisExpanded }">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="drawer-content" :class="{ expanded: diagnosisExpanded }">
        <div class="diagnosis-content">
          <el-form-item label="诊断分类" prop="classification" label-width="90px">
            <el-radio-group v-model="classification" class="horizontal-radio-group">
              <el-radio value="normal">正常</el-radio>
              <el-radio value="benign">良性</el-radio>
              <el-radio value="malignant">恶性</el-radio>
              <el-radio value="uncertain">未定</el-radio>
              <el-radio value="other">其他</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="详细诊断" prop="detail" label-width="90px">
            <el-input
              v-model="detail"
              type="textarea"
              :rows="4"
              placeholder="例如：良性结节，BRADS 3类"
              maxlength="100"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="备注" prop="remark" label-width="90px">
            <el-input
              v-model="remark"
              type="textarea"
              :rows="4"
              placeholder="请填写备注信息"
              maxlength="200"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>
      </div>
    </div>

    <!-- 诊断联想 - 只有选择非正常选项时才显示 -->
    <div v-if="showDiagnosisAssociate" class="diagnosis-associate-section card-container drawer-container">
      <div class="section-header drawer-header" @click="toggleAssociateExpanded">
        <div class="header-left">
          <el-icon class="section-icon"><Search /></el-icon>
          <h3>诊断联想</h3>
        </div>
        <div class="header-right">
          <el-icon class="expand-icon" :class="{ expanded: associateExpanded }">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="drawer-content" :class="{ expanded: associateExpanded }">
        <el-form-item prop="associate">
          <TagManager
            v-model="associate"
            type="thinking"
            title="诊断联想"
            placeholder="输入诊断联想内容"
            tag-type="primary"
            button-type="primary"
          />
        </el-form-item>
      </div>
    </div>

    <!-- 诊断依据 - 只有选择非正常选项时才显示 -->
    <div v-if="showDiagnosisBasis" class="diagnosis-basis-section card-container drawer-container">
      <div class="section-header drawer-header" @click="toggleBasisExpanded">
        <div class="header-left">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3>诊断依据</h3>
        </div>
        <div class="header-right">
          <el-icon class="expand-icon" :class="{ expanded: basisExpanded }">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="drawer-content" :class="{ expanded: basisExpanded }">
        <el-form-item prop="basis">
          <TagManager
            v-model="basis"
            type="basis"
            title="诊断依据"
            placeholder="输入诊断依据"
            tag-type="warning"
            button-type="warning"
          />
        </el-form-item>
      </div>
    </div>

    <!-- 空状态提示 - 当选择正常时显示 -->
    <div v-if="!showDiagnosisAssociate && !showDiagnosisBasis" class="empty-diagnosis-hint card-container">
      <div class="empty-content">
        <el-icon class="empty-icon"><InfoFilled /></el-icon>
        <p class="empty-text">选择非正常诊断分类后，此处将显示相关诊断信息</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.diagnosis-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 统一卡片容器样式 */
.card-container {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: var(--shadow-medium);
}

/* 抽屉容器样式 */
.drawer-container {
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 60px; /* 统一折叠状态的最小高度 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 8px;
  margin: -8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.drawer-header:hover {
  background-color: var(--bg-hover);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: var(--text-secondary);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.drawer-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0;
}

.drawer-content.expanded {
  max-height: 500px;
  padding-top: 16px;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-primary);
}

.section-icon {
  font-size: 18px;
  color: var(--medical-blue);
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 诊断内容区域 */
.diagnosis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.diagnosis-content .el-form-item {
  margin-bottom: 20px;
}

.diagnosis-content .el-form-item:last-child {
  margin-bottom: 0;
}

/* 水平排列的单选按钮组 */
.horizontal-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.horizontal-radio-group .el-radio {
  margin-right: 0;
  margin-bottom: 0;
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.horizontal-radio-group .el-radio.is-checked {
  background-color: #ecf5ff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.horizontal-radio-group .el-radio:hover {
  background-color: #f0f2f5;
}

.horizontal-radio-group .el-radio.is-checked:hover {
  background-color: #d9ecff;
}

/* 空状态提示 */
.empty-diagnosis-hint {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 48px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  color: var(--text-secondary);
}
</style>
