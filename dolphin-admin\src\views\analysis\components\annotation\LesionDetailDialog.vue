<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleClose"
    title="病灶详细信息"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <div v-if="lesion" class="lesion-detail">
      <!-- 病灶基本信息 -->
      <div class="detail-section">
        <div class="section-title">
          <div class="lesion-indicator" :style="{ backgroundColor: lesion.color }"></div>
          <span>{{ lesionTitle }}</span>
        </div>

        <div class="detail-item">
          <span class="detail-label">病灶信息：</span>
          <span class="detail-content">{{ lesion.description }}</span>
        </div>

        <div class="detail-item">
          <span class="detail-label">创建时间：</span>
          <span class="detail-content">{{ formatTime(lesion.createdAt) }}</span>
        </div>

        <div class="detail-item">
          <span class="detail-label">标注点数量：</span>
          <span class="detail-content">{{ lesion.points.length }}个</span>
        </div>
      </div>

      <!-- 标注点详细信息 -->
      <div class="detail-section">
        <h4 class="section-subtitle">标注点坐标</h4>
        <div class="points-grid">
          <div v-for="(point, index) in lesion.points" :key="point.id" class="point-item">
            <span class="point-index">{{ index + 1 }}</span>
            <span class="point-coord">({{ Math.round(point.x) }}, {{ Math.round(point.y) }})</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="danger" @click="handleDelete">删除病灶</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'LesionDetailDialog',
})

// 定义 Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  lesion: {
    type: Object,
    default: null,
  },
  lesionIndex: {
    type: Number,
    default: 0,
  },
})

// 定义 Events
const emit = defineEmits(['update:visible', 'delete-lesion'])

// 计算属性
const lesionTitle = computed(() => {
  return `病灶 ${props.lesionIndex + 1}`
})

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除"${lesionTitle.value}"吗？此操作将同时清除画布上的标注区域。`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    emit('delete-lesion', props.lesion.id)
    handleClose()
  } catch {
    // 用户取消删除
  }
}
</script>

<style scoped>
.lesion-detail {
  padding: 0.5rem 0;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.lesion-indicator {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  flex-shrink: 0;
}

.section-subtitle {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  margin-top: 0;
}

.detail-item {
  display: flex;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--text-primary);
  min-width: 100px;
  flex-shrink: 0;
}

.detail-content {
  color: var(--text-secondary);
  word-break: break-word;
  flex: 1;
}

.points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 0.5rem;
}

.point-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
}

.point-index {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--medical-blue);
  color: var(--text-inverse);
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 500;
  flex-shrink: 0;
}

.point-coord {
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
</style>
