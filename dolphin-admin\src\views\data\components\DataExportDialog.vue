<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { reqExportData } from '@/api/data'
import type { ExportDataParams } from '@/api/data/type'

// 定义组件名称
defineOptions({
  name: 'DataExportDialog',
})

// 定义props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// 定义emits
interface Emits {
  'update:visible': [visible: boolean]
  close: []
}

const emit = defineEmits<Emits>()

// 导出表单数据
const exportForm = reactive({
  datasetName: '',
  datasetDescription: '',
  startTime: null as Date | null,
  endTime: null as Date | null,
  remark: '',
})

// 导出表单引用
const exportFormRef = ref()

// 导出加载状态
const exportLoading = ref(false)

// 导出表单验证规则
const exportRules = {
  datasetName: [
    { required: true, message: '请输入数据集名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  datasetDescription: [
    { required: true, message: '请输入数据集描述', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  startTime: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择终止时间', trigger: 'change' }],
  remark: [
    { required: true, message: '请输入备注信息', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' },
  ],
}

// 处理导出取消
const handleCancel = () => {
  emit('update:visible', false)
  emit('close')
  // 重置表单
  exportFormRef.value?.resetFields()
  exportForm.datasetName = ''
  exportForm.datasetDescription = ''
  exportForm.startTime = null
  exportForm.endTime = null
  exportForm.remark = ''
}

// 处理导出确认
const handleConfirm = async () => {
  try {
    // 表单验证
    await exportFormRef.value?.validate()

    // 验证时间范围
    if (exportForm.startTime && exportForm.endTime) {
      const startTime = new Date(exportForm.startTime).getTime()
      const endTime = new Date(exportForm.endTime).getTime()

      if (startTime >= endTime) {
        ElMessage.error('起始时间必须早于终止时间')
        return
      }
    }

    exportLoading.value = true

    // 构建请求参数
    const params: ExportDataParams = {
      datasetName: exportForm.datasetName,
      datasetDescription: exportForm.datasetDescription,
      startTime: new Date(exportForm.startTime!).getTime(),
      endTime: new Date(exportForm.endTime!).getTime(),
      remark: exportForm.remark,
    }

    // 调用导出接口获取二进制文件
    const response = await reqExportData(params)
    // 从axios响应中提取blob数据
    const blob: Blob = (response as any).data || (response as unknown as Blob)

    // 触发文件下载
    downloadFile(blob, params.datasetName)

    ElMessage.success('导出成功，文件已开始下载')

    // 关闭对话框
    handleCancel()
  } catch (error: any) {
    if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('导出失败，请稍后重试')
    }
  } finally {
    exportLoading.value = false
  }
}

// 下载文件工具函数
const downloadFile = (blob: Blob, filename: string) => {
  try {
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名（添加时间戳避免重名）
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const extension = getFileExtension(blob.type) || 'zip'
    link.download = `${filename}_${timestamp}.${extension}`

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败，请稍后重试')
  }
}

// 根据MIME类型获取文件扩展名
const getFileExtension = (mimeType: string): string => {
  const mimeMap: Record<string, string> = {
    'application/zip': 'zip',
    'application/x-zip-compressed': 'zip',
    'application/gzip': 'gz',
    'application/x-tar': 'tar',
    'application/json': 'json',
    'text/plain': 'txt',
    'application/octet-stream': 'zip', // 默认为zip
  }
  return mimeMap[mimeType] || 'zip'
}
</script>

<template>
  <el-dialog 
    :model-value="visible" 
    @update:model-value="$emit('update:visible', $event)"
    title="导出数据" 
    width="500px" 
    :close-on-click-modal="false"
  >
    <el-form ref="exportFormRef" :model="exportForm" :rules="exportRules" label-width="120px" label-position="left">
      <el-form-item label="数据集名称" prop="datasetName">
        <el-input v-model="exportForm.datasetName" placeholder="请输入数据集名称" clearable />
      </el-form-item>

      <el-form-item label="数据集描述" prop="datasetDescription">
        <el-input v-model="exportForm.datasetDescription" placeholder="请输入数据集描述" clearable />
      </el-form-item>

      <el-form-item label="起始时间" prop="startTime">
        <el-date-picker
          v-model="exportForm.startTime"
          type="datetime"
          placeholder="请选择起始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="终止时间" prop="endTime">
        <el-date-picker
          v-model="exportForm.endTime"
          type="datetime"
          placeholder="请选择终止时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="exportForm.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="exportLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
// 对话框样式可以根据需要添加
</style>
