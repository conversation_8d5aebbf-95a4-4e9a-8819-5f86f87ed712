#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/bin/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/bin/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules:/mnt/d/Dolphin_AI/dolphin-ai/dolphin-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/bin/autoprefixer" "$@"
else
  exec node  "$basedir/../.pnpm/autoprefixer@10.4.21_postcss@8.5.6/node_modules/autoprefixer/bin/autoprefixer" "$@"
fi
