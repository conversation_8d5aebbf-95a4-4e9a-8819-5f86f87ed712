import { ref } from 'vue'

export function useCanvasInteraction() {
  // 响应式数据
  const currentMode = ref('select')
  const isDrawing = ref(false)
  const drawingObject = ref(null)
  const startPoint = ref(null)

  // 交互模式常量
  const INTERACTION_MODES = {
    SELECT: 'select',
    DRAW_RECT: 'draw-rect',
    DRAW_CIRCLE: 'draw-circle',
    DRAW_POINT: 'draw-point',
  }

  // 设置交互模式
  const setInteractionMode = (mode) => {
    if (!Object.values(INTERACTION_MODES).includes(mode)) {
      console.warn('无效的交互模式:', mode)
      return false
    }

    currentMode.value = mode

    // 重置绘制状态
    resetDrawingState()

    return true
  }

  // 重置绘制状态
  const resetDrawingState = () => {
    isDrawing.value = false
    drawingObject.value = null
    startPoint.value = null
  }

  // 开始绘制
  const startDrawing = (point) => {
    if (currentMode.value === INTERACTION_MODES.SELECT) {
      return false
    }

    isDrawing.value = true
    startPoint.value = { ...point }

    return true
  }

  // 更新绘制
  const updateDrawing = (point) => {
    if (!isDrawing.value || !startPoint.value) {
      return null
    }

    // 根据模式返回不同的绘制数据
    switch (currentMode.value) {
      case INTERACTION_MODES.DRAW_RECT:
        return {
          type: 'rect',
          left: Math.min(startPoint.value.x, point.x),
          top: Math.min(startPoint.value.y, point.y),
          width: Math.abs(point.x - startPoint.value.x),
          height: Math.abs(point.y - startPoint.value.y),
        }

      case INTERACTION_MODES.DRAW_CIRCLE:
        const radius = Math.sqrt(Math.pow(point.x - startPoint.value.x, 2) + Math.pow(point.y - startPoint.value.y, 2))
        return {
          type: 'circle',
          left: startPoint.value.x - radius,
          top: startPoint.value.y - radius,
          radius: radius,
        }

      case INTERACTION_MODES.DRAW_POINT:
        return {
          type: 'point',
          left: point.x,
          top: point.y,
        }

      default:
        return null
    }
  }

  // 完成绘制
  const finishDrawing = (point) => {
    if (!isDrawing.value) {
      return null
    }

    const drawingData = updateDrawing(point)
    resetDrawingState()

    return drawingData
  }

  // 取消绘制
  const cancelDrawing = () => {
    resetDrawingState()
  }

  // 处理鼠标按下事件
  const handleMouseDown = (event, canvas) => {
    const pointer = canvas.getPointer(event.e)

    switch (currentMode.value) {
      case INTERACTION_MODES.DRAW_POINT:
        // 点标注模式：直接创建点
        return {
          type: 'point',
          x: pointer.x,
          y: pointer.y,
          created: true,
        }

      case INTERACTION_MODES.DRAW_RECT:
      case INTERACTION_MODES.DRAW_CIRCLE:
        // 矩形和圆形模式：开始绘制
        startDrawing(pointer)
        return {
          type: 'start-drawing',
          point: pointer,
        }

      default:
        return null
    }
  }

  // 处理鼠标移动事件
  const handleMouseMove = (event, canvas) => {
    if (!isDrawing.value) return null

    const pointer = canvas.getPointer(event.e)
    return updateDrawing(pointer)
  }

  // 处理鼠标释放事件
  const handleMouseUp = (event, canvas) => {
    if (!isDrawing.value) return null

    const pointer = canvas.getPointer(event.e)
    return finishDrawing(pointer)
  }

  // 检查是否可以进行交互
  const canInteract = () => {
    return currentMode.value !== INTERACTION_MODES.SELECT
  }

  // 获取当前模式的描述
  const getModeDescription = () => {
    const descriptions = {
      [INTERACTION_MODES.SELECT]: '选择模式',
      [INTERACTION_MODES.DRAW_POINT]: '点标注模式',
      [INTERACTION_MODES.DRAW_RECT]: '矩形标注模式',
      [INTERACTION_MODES.DRAW_CIRCLE]: '圆形标注模式',
    }

    return descriptions[currentMode.value] || '未知模式'
  }

  // 获取模式相关的样式
  const getModeStyles = () => {
    const styles = {
      [INTERACTION_MODES.SELECT]: { cursor: 'default' },
      [INTERACTION_MODES.DRAW_POINT]: { cursor: 'crosshair' },
      [INTERACTION_MODES.DRAW_RECT]: { cursor: 'crosshair' },
      [INTERACTION_MODES.DRAW_CIRCLE]: { cursor: 'crosshair' },
    }

    return styles[currentMode.value] || { cursor: 'default' }
  }

  return {
    // 响应式数据
    currentMode,
    isDrawing,
    drawingObject,
    startPoint,

    // 常量
    INTERACTION_MODES,

    // 方法
    setInteractionMode,
    resetDrawingState,
    startDrawing,
    updateDrawing,
    finishDrawing,
    cancelDrawing,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    canInteract,
    getModeDescription,
    getModeStyles,
  }
}
