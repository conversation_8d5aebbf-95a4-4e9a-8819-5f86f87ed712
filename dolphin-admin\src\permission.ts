// 路由鉴权
import router from '@/router'
import Nprogress from 'nprogress'
import 'nprogress/nprogress.css'
import useUserStore from '@/store/modules/user'
import pinia from './store'
import setting from './setting'
import { authDebugger } from '@/utils/authDebug'

Nprogress.configure({ showSpinner: false }) // 进度环关闭

let userStore = useUserStore(pinia)

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  document.title = (setting.title + '-' + to.meta.title) as string
  // to: 即将要进入的目标路由对象
  // from: 当前导航正要离开的路由
  // next: 调用该方法后，才能进入下一个钩子
  Nprogress.start()

  let username = userStore.username
  let token = userStore.token

  // 记录路由守卫检查
  authDebugger.log(`路由守卫检查 - 目标: ${to.path}`, 'router.beforeEach')

  if (token) {
    if (to.path === '/login') {
      console.log('✅ 已登录用户访问登录页，重定向到首页')
      next({ path: '/' })
    } else {
      if (username) {
        console.log('✅ 用户信息完整，允许访问')
        next()
      } else {
        console.log('⚠️ 有token但无用户信息，尝试获取用户信息')
        authDebugger.log('尝试获取用户信息', 'router.beforeEach')
        try {
          await userStore.userInfo()
          console.log('✅ 用户信息获取成功')
          authDebugger.log('用户信息获取成功', 'router.beforeEach')
          next()
        } catch (error: any) {
          console.error('❌ 用户信息获取失败:', error)
          authDebugger.log(`用户信息获取失败: ${error?.message}`, 'router.beforeEach')

          // 检查错误类型，只有在token真正无效时才登出
          if (error?.code === 401 || error?.code === 4001 || error?.code === 4002) {
            console.log('🔑 Token已失效，执行登出')
            authDebugger.log('Token失效，执行登出', 'router.beforeEach')
            userStore.userLogout()
            next({ path: '/login', query: { redirect: to.path } })
          } else {
            // 网络错误或其他临时错误，不清除token，直接跳转到登录页
            console.log('🌐 网络或服务器错误，保留token，跳转登录页')
            authDebugger.log('网络错误，保留token', 'router.beforeEach')
            next({ path: '/login', query: { redirect: to.path } })
          }
        }
      }
    }
  } else {
    console.log('❌ 无token，重定向到登录页')
    if (to.path === '/login') {
      next()
    } else {
      next({ path: '/login', query: { redirect: to.path } })
    }
  }
})

// 全局后置守卫
router.afterEach((_to, _from) => {
  Nprogress.done()
})
