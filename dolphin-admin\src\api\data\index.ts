/**
 * 数据采集相关接口
 *
 * API设计规范：
 * - 采用RESTful风格
 * - 版本化管理：/v1/模块/功能
 * - 统一响应格式
 * - 完整的类型定义
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import request from '@/utils/request'
import type {
  DiagnoseDto,
  DataItem,
  PageQuery,
  SearchQuery,
  PageResponse,
  ExportDataParams
} from './type'

/**
 * API接口地址枚举
 *
 * 接口命名规范：
 * - v1: API版本号
 * - data: 数据管理模块
 * - diagnose: 诊断数据子模块
 * - 使用RESTful动词：GET/POST/PUT/DELETE
 */
enum API {
  // ==================== 诊断数据模块 (v1/data/diagnose) ====================
  /** 保存诊断数据 */
  SAVE_DIAGNOSE_URL = '/v1/data/diagnose/save',
  /** 上传医学图像 */
  UPLOAD_IMAGE_URL = '/v1/data/diagnose/image/upload',

  // ==================== 数据采集模块 (v1/data/collect) ====================
  /** 搜索诊断数据 */
  SEARCH_DATA_URL = '/v1/data/collect/search',
  /** 分页获取数据列表 */
  GET_DATA_LIST_URL = '/v1/data/collect/list',
  /** 获取数据详情 */
  GET_DATA_INFO_URL = '/v1/data/collect/info',
  /** 获取图像数据 */
  GET_IMAGE_URL = '/v1/resource',
  /** 删除数据记录 */
  DELETE_DATA_URL = '/v1/data/collect/delete',
  /** 导出数据 */
  EXPORT_DATA_URL = '/v1/data/collect/export',
}

// ==================== 诊断数据相关接口 ====================

/**
 * 保存诊断数据
 * POST /v1/data/diagnose/save (实际请求: /api/v1/data/diagnose/save)
 *
 * @param data 诊断数据
 * @returns Promise<string> 创建的记录ID
 *
 * @example
 * ```typescript
 * const diagnoseData = {
 *   associate: ['考虑良性结节', '结合BRADS分级'],
 *   basis: ['无钙化表现', '血流信号正常'],
 *   caption: ['边界清晰', '回声均匀', '形态规则'],
 *   classification: 'benign',
 *   detail: '良性结节，BRADS 3类',
 *   focus: '甲状腺右叶低回声结节，边界清晰，形态规则，大小约1.2x0.8cm',
 *   location: [320, 340, 360, 380, 400, 340],
 *   part: 'thyroid',
 *   section: 'transverse',
 *   image: '1942470299538821120'
 * }
 * const result = await reqSaveDiagnose(diagnoseData)
 * console.log(result) // 新创建的记录ID: "1942470299538821120"
 * ```
 */
export const reqSaveDiagnose = (data: DiagnoseDto) =>
  request.post<string>(API.SAVE_DIAGNOSE_URL, data)

/**
 * 上传医学图像
 * POST /v1/data/collect/image/upload (实际请求: /api/v1/data/collect/image/upload)
 *
 * @param file 图像文件
 * @param onProgress 上传进度回调
 * @returns Promise<string> 图像ID
 *
 * @example
 * ```typescript
 * const file = new File([...], 'medical-image.jpg', { type: 'image/jpeg' })
 * const result = await reqUploadImage(file, (progress) => {
 *   console.log(`上传进度: ${progress}%`)
 * })
 * console.log(result) // 图像ID: "1942470299538821120"
 * ```
 */
export const reqUploadImage = (
  file: File,
  onProgress?: (progress: number) => void
) => {
  const formData = new FormData()
  formData.append('image', file)

  return request.post<string>(
    API.UPLOAD_IMAGE_URL,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    }
  )
}

// ==================== 数据采集相关接口 ====================

/**
 * 搜索诊断数据
 * GET /v1/data/collect/search (实际请求: /api/v1/data/collect/search)
 *
 * @param params 搜索查询参数
 * @returns Promise<PageResponse<DataItem>> 分页数据响应
 *
 * @example
 * ```typescript
 * // 按分类搜索
 * const params1 = {
 *   pageNumber: 0,
 *   pageSize: 10,
 *   classification: 'benign'
 * }
 *
 * // 按诊断ID搜索
 * const params2 = {
 *   pageNumber: 0,
 *   pageSize: 10,
 *   diagnoseId: '1'
 * }
 *
 * // 组合搜索
 * const params3 = {
 *   pageNumber: 0,
 *   pageSize: 10,
 *   classification: 'malignant',
 *   diagnoseId: '1'
 * }
 *
 * const result = await reqSearchData(params1)
 * console.log(result.records) // 数据列表，每个项目包含image字段(单个图像ID)
 * console.log(result.total) // 总记录数
 * ```
 */
export const reqSearchData = (params: SearchQuery) =>
  request.get<PageResponse<DataItem>>(API.SEARCH_DATA_URL, {
    params,
    // 静默请求，不显示成功提示
    headers: {
      'X-Silent-Request': 'true'
    }
  })

/**
 * 分页获取数据列表
 * GET /v1/data/collect/list (实际请求: /api/v1/data/collect/list)
 *
 * @param params 分页查询参数
 * @returns Promise<PageResponse<DataItem>> 分页数据响应
 *
 * @example
 * ```typescript
 * const params = {
 *   current: 1,
 *   size: 10,
 *   classification: 'benign',
 *   keyword: '结节'
 * }
 * const result = await reqGetDataList(params)
 * console.log(result.records) // 数据列表
 * console.log(result.total) // 总记录数
 * ```
 */
export const reqGetDataList = (params: PageQuery) =>
  request.get<PageResponse<DataItem>>(API.GET_DATA_LIST_URL, { params })

/**
 * 获取数据详情
 * GET /v1/data/collect/info (实际请求: /api/v1/data/collect/info)
 *
 * @param diagnoseId 诊断记录ID
 * @returns Promise<DataItem> 数据详情
 *
 * @example
 * ```typescript
 * const result = await reqGetDataInfo('1942846051186774016')
 * console.log(result.detail) // 详细诊断
 * console.log(result.caption) // 特征分析
 * ```
 */
export const reqGetDataInfo = (diagnoseId: string) =>
  request.get<DataItem>(API.GET_DATA_INFO_URL, {
    params: { diagnoseId }
  })

/**
 * 获取图像数据
 * GET /v1/resource/{imageId} (实际请求: /api/v1/resource/data/image/xxx.jpg)
 *
 * @param imageId 图像ID (如: "data/image/1945328703244472320.jpg")
 * @returns Promise<Blob> 图像数据
 *
 * @example
 * ```typescript
 * const imageBlob = await reqGetImage('data/image/1942470299538821120.jpg')
 * const imageUrl = URL.createObjectURL(imageBlob)
 * ```
 */
export const reqGetImage = (imageId: string) =>
  request.get<Blob>(`${API.GET_IMAGE_URL}/${imageId}`, {
    responseType: 'blob',
    // 静默请求，不显示成功提示
    headers: {
      'X-Silent-Request': 'true'
    }
  })

/**
 * 删除数据记录
 * DELETE /v1/data/collect/delete (实际请求: /api/v1/data/collect/delete)
 *
 * @param diagnoseId 诊断记录ID
 * @returns Promise<string> 删除结果消息
 *
 * @example
 * ```typescript
 * const result = await reqDeleteData('1942846051186774016')
 * console.log(result) // "删除成功"
 * ```
 */
export const reqDeleteData = (diagnoseId: string) =>
  request.delete<string>(API.DELETE_DATA_URL, {
    params: { diagnoseId }
  })

/**
 * 导出数据
 * POST /v1/data/collect/export (实际请求: /api/v1/data/collect/export)
 *
 * @param params 导出参数
 * @returns Promise<Blob> 导出的二进制文件数据
 *
 * @example
 * ```typescript
 * const params = {
 *   datasetName: '甲状腺结节数据集',
 *   datasetDescription: '用于训练的甲状腺结节超声图像数据',
 *   startTime: 1640995200000, // 2022-01-01 00:00:00
 *   endTime: 1672531199000,   // 2022-12-31 23:59:59
 *   remark: '包含良性和恶性结节样本'
 * }
 * const blob = await reqExportData(params)
 * // 触发下载
 * const url = URL.createObjectURL(blob)
 * const link = document.createElement('a')
 * link.href = url
 * link.download = `${params.datasetName}.zip`
 * link.click()
 * URL.revokeObjectURL(url)
 * ```
 */
export const reqExportData = (params: ExportDataParams) =>
  request.post<Blob>(API.EXPORT_DATA_URL, params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    }
  })

// ==================== 导出所有接口 ====================

export default {
  // 诊断数据接口
  reqSaveDiagnose,
  reqUploadImage,
  // 数据采集接口
  reqSearchData,
  reqGetDataList,
  reqGetDataInfo,
  reqGetImage,
  reqDeleteData,
  reqExportData
}
