<template>
  <div class="logo" v-if="setting.logoHidden">
    <img :src="setting.logo" alt="" />
    <p v-show="!settingStore.fold">{{ setting.title }}</p>
  </div>
</template>

<script setup lang="ts">
  import setting from '@/setting'
  import useLayoutSettingStore from '@/store/modules/setting'
  let settingStore = useLayoutSettingStore()
  defineOptions({
    name: 'Logo',
  })
</script>

<style scoped lang="scss">
  .logo {
    height: $base-logo-height;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1); // 医疗风格的分隔线
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    p {
      font-size: $base-logo-font-size;
      margin-left: 10px;
      color: #FFFFFF; // 医疗蓝色背景上的白色文字，医生喜欢的清洁专业风格
      font-weight: 600; // 增加字体粗细，更专业
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); // 轻微阴影增加可读性
    }
    img {
      width: 40px;
      height: 40px;
      border-radius: 8px; // 医疗风格的圆角
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // 轻微阴影
    }
  }
</style>
