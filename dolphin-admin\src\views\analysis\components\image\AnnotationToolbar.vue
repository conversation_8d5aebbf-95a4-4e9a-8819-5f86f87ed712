<script setup>
import SvgIcon from '@/components/SvgIcon/index.vue'

// 定义组件名称
defineOptions({
  name: 'AnnotationToolbar',
})

// 定义 Props
const props = defineProps({
  currentMode: {
    type: String,
    default: 'select',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  hasAnnotations: {
    type: Boolean,
    default: false,
  },
  canExport: {
    type: Boolean,
    default: false,
  },
})

// 定义 Events
const emit = defineEmits(['mode-change', 'undo-last-annotation', 'export-lesion'])

// 处理模式切换
const handleModeChange = (mode) => {
  if (props.disabled) return

  emit('mode-change', mode)
}

// 处理撤销上次标注
const handleUndoLastAnnotation = () => {
  if (props.disabled || !props.hasAnnotations) return

  emit('undo-last-annotation')
}

// 处理导出病灶
const handleExportLesion = () => {
  if (props.disabled || !props.canExport) return

  emit('export-lesion')
}
</script>
<template>
  <div class="annotation-toolbar">
    <div class="toolbar-content">
      <!-- 紧凑的图标按钮组 -->
      <div class="compact-button-group">
        <el-tooltip content="点标注" placement="bottom" :show-after="500">
          <el-button
            :class="['icon-btn', { 'icon-btn--active': currentMode === 'draw-point' }]"
            size="small"
            @click="handleModeChange('draw-point')"
            :disabled="disabled"
          >
            <SvgIcon name="analysis-annotation" width="16px" height="16px" />
          </el-button>
        </el-tooltip>

        <el-tooltip content="选择" placement="bottom" :show-after="500">
          <el-button
            :class="['icon-btn', { 'icon-btn--active': currentMode === 'select' }]"
            size="small"
            @click="handleModeChange('select')"
            :disabled="disabled"
          >
            <SvgIcon name="analysis-choice" width="16px" height="16px" />
          </el-button>
        </el-tooltip>

        <el-tooltip v-if="currentMode === 'draw-point'" content="撤销" placement="bottom" :show-after="500">
          <el-button
            class="icon-btn"
            size="small"
            @click="handleUndoLastAnnotation"
            :disabled="disabled || !hasAnnotations"
          >
            <SvgIcon name="analysis-undo" width="16px" height="16px" />
          </el-button>
        </el-tooltip>

        <el-tooltip v-if="currentMode === 'draw-point'" content="标识" placement="bottom" :show-after="500">
          <el-button class="icon-btn" size="small" @click="handleExportLesion" :disabled="disabled || !canExport">
            <SvgIcon name="analysis-export" width="16px" height="16px" />
          </el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style scoped>
.annotation-toolbar {
  display: inline-block; /* 改为inline-block，让宽度自适应内容 */
  padding: 6px 12px; /* 减少内边距 */
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
}

.toolbar-content {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  gap: 6px; /* 减少间距 */
}

.compact-button-group {
  display: flex;
  align-items: center;
  gap: 4px; /* 按钮间距更小 */
}

/* 确保所有按钮都可以点击 */
.annotation-toolbar .el-button {
  position: relative;
  z-index: 101;
  pointer-events: auto;
}

.annotation-toolbar button {
  position: relative;
  z-index: 101;
  pointer-events: auto;
}

.icon-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #d1d5db;
  padding: 6px !important; /* 正方形按钮 */
  font-size: 14px !important; /* 图标大小 */
  height: 32px !important; /* 固定高度 */
  width: 32px !important; /* 固定宽度，正方形 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.icon-btn:hover {
  background: transparent !important; /* 保持透明 */
  border-color: #999;
  color: #333;
}

.icon-btn--active {
  background: transparent !important; /* 去掉背景颜色 */
  border-color: #2563eb;
  color: #2563eb; /* 改为蓝色文字 */
}

.icon-btn--active:hover {
  background: transparent !important; /* 保持透明 */
  border-color: #1d4ed8;
  color: #1d4ed8; /* 深蓝色文字 */
}

/* 图标样式 */
.icon-btn .el-icon {
  font-size: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-text {
  font-size: 0.8rem;
  color: #6b7280;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annotation-toolbar {
    padding: 6px 8px;
  }

  .toolbar-content {
    gap: 8px;
  }

  .tool-group {
    gap: 6px;
  }

  .status-text {
    font-size: 0.75rem;
  }
}
</style>
