// 对外暴露配置路由（常量路由）
export const constantRoute = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true, // 是否显示在左侧菜单中 true隐藏 false显示
      icon: 'User', // 菜单图标,支持element-plus的所有图标组件
    },
  },
  {
    path: '/layout',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    meta: { title: '', hidden: true, icon: '' },
    children: [
      {
        path: '/layout/profile',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'),
        meta: { title: '个人中心', hidden: true, icon: 'User' },
      },
    ],
  },
  {
    path: '/',
    name: 'Home',
    component: () => import('@/layout/index.vue'),
    meta: { title: '首页', hidden: false, icon: 'HomeFilled' },
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'HomePage',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '首页', hidden: false, icon: 'HomeFilled' },
      },
    ],
  },
  {
    path: '/data',
    name: 'Data',
    component: () => import('@/layout/index.vue'),
    meta: { title: '数据管理', hidden: false, icon: 'DataBoard' },
    redirect: '/data/management',
    children: [
      {
        path: '/data/management',
        name: 'DataManagement',
        component: () => import('@/views/data/index.vue'),
        meta: { title: '数据管理', hidden: false, icon: 'DataBoard' },
      },
    ],
  },
  {
    path: '/screen',
    name: 'Screen',
    component: () => import('@/layout/index.vue'),
    meta: { title: '数据采集', hidden: false, icon: 'Monitor' },
    redirect: '/screen/collection',
    children: [
      {
        path: '/screen/collection',
        name: 'DataCollection',
        component: () => import('@/views/screen/index.vue'),
        meta: { title: '数据采集', hidden: false, icon: 'Monitor' },
      },
    ],
  },
  {
    path: '/analysis',
    name: 'ImageAnalysis',
    component: () => import('@/layout/index.vue'),
    meta: { title: '图像标注', hidden: false, icon: 'Picture' },
    redirect: '/analysis/main',
    children: [
      {
        path: '/analysis/main',
        name: 'ImageAnalysisMain',
        // @ts-ignore
        component: () => import('@/views/analysis/index.vue'),
        meta: { title: '图像标注', hidden: false, icon: 'Picture' },
      },
    ],
  },
  {
    path: '/model',
    name: 'Model',
    component: () => import('@/layout/index.vue'),
    meta: { title: '模型评分', hidden: false, icon: 'TrendCharts' },
    redirect: '/model/score',
    children: [
      {
        path: '/model/score',
        name: 'ModelScore',
        component: () => import('@/views/model/score/index.vue'),
        meta: { title: '模型评分', hidden: false, icon: 'TrendCharts' },
      },
    ],
  },
  {
    path: '/acl',
    name: 'Acl',
    component: () => import('@/layout/index.vue'),
    meta: { title: '权限管理', hidden: false, icon: 'Lock' },
    redirect: '/acl/user',
    children: [
      {
        path: '/acl/user',
        name: 'User',
        component: () => import('@/views/acl/user/index.vue'),
        meta: { title: '用户管理', hidden: false, icon: 'User' },
      },
      {
        path: '/acl/role',
        name: 'Role',
        component: () => import('@/views/acl/role/index.vue'),
        meta: { title: '角色管理', hidden: false, icon: 'UserFilled' },
      },
      {
        path: '/acl/permission',
        name: 'Permission',
        component: () => import('@/views/acl/permission/index.vue'),
        meta: { title: '菜单管理', hidden: false, icon: 'DocumentCopy' },
      },
    ],
  },
  {
    path: '/product',
    name: 'Product',
    component: () => import('@/layout/index.vue'),
    meta: { title: '商品管理', hidden: false, icon: 'Goods' },
    children: [],
  },
  {
    path: '/ultrasound3d',
    name: 'Ultrasound3D',
    component: () => import('@/layout/index.vue'),
    meta: { title: '超声3D测试', hidden: false, icon: 'View' },
    redirect: '/ultrasound3d/viewer',
    children: [
      {
        path: '/ultrasound3d/viewer',
        name: 'Ultrasound3DViewer',
        component: () => import('@/views/ultrasound3d/index.vue'),
        meta: { title: '超声3D测试', hidden: false, icon: 'View' },
      },
    ],
  },

  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404/index.vue'),
    meta: { title: '404', hidden: true, icon: 'Warning' },
  },
  {
    // 任意路由
    path: '/:pathMatch(.*)*',
    name: 'Any',
    redirect: '/404',
    meta: { title: '任意', hidden: true, icon: 'Warning' },
  },
]
