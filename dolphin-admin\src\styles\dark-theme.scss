/**
 * 暗色主题样式定义
 * 提供完整的暗色模式CSS变量和样式
 */

// 亮色模式（默认）CSS变量
:root {
  // 基础背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f7fa;
  --bg-hover: #f0f2f5;
  
  // 文字颜色
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-tertiary: #95a5a6;
  --text-inverse: #ffffff;
  
  // 边框颜色
  --border-primary: #e4e7ed;
  --border-secondary: #ebeef5;
  --border-tertiary: #f2f6fc;
  
  // 阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 6px 20px rgba(0, 0, 0, 0.15);
  
  // 医疗主题色（保持不变）
  --medical-blue: #2e7ce6;
  --medical-green: #00d4aa;
  --medical-orange: #ff8c00;
  --medical-red: #ff4757;
  
  // 菜单相关
  --menu-bg: #2e7ce6;
  --menu-text: #ffffff;
  --menu-active: #00d4aa;
  --menu-hover-bg: rgba(255, 255, 255, 0.1);
  --menu-active-bg: rgba(0, 212, 170, 0.2);
  
  // 顶部导航
  --tabbar-bg: #ffffff;
  --tabbar-text: #2c3e50;
  
  // 滚动条
  --scrollbar-thumb: #c0c4cc;
  --scrollbar-thumb-hover: #909399;
  --scrollbar-track: #f5f7fa;
}

// 暗色模式CSS变量
.dark-theme {
  // 基础背景色 - 使用深灰色调而非纯黑
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --bg-hover: #404040;
  
  // 文字颜色 - 高对比度确保可读性
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #8a8a8a;
  --text-inverse: #2c3e50;
  
  // 边框颜色
  --border-primary: #404040;
  --border-secondary: #4a4a4a;
  --border-tertiary: #555555;
  
  // 阴影 - 暗色模式下使用更深的阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 6px 20px rgba(0, 0, 0, 0.5);
  
  // 医疗主题色 - 稍微调亮以适应暗色背景
  --medical-blue: #4a90e2;
  --medical-green: #1ae6c4;
  --medical-orange: #ffa726;
  --medical-red: #ff6b7a;
  
  // 菜单相关 - 保持医疗蓝调但更深
  --menu-bg: #1e3a5f;
  --menu-text: #ffffff;
  --menu-active: #1ae6c4;
  --menu-hover-bg: rgba(255, 255, 255, 0.1);
  --menu-active-bg: rgba(26, 230, 196, 0.2);
  
  // 顶部导航
  --tabbar-bg: #2d2d2d;
  --tabbar-text: #ffffff;
  
  // 滚动条
  --scrollbar-thumb: #555555;
  --scrollbar-thumb-hover: #666666;
  --scrollbar-track: #2d2d2d;
}

// 主题切换过渡动画
* {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease,
              box-shadow 0.3s ease !important;
}

// 基础布局样式应用主题变量
body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

// 卡片样式
.el-card {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

// 输入框样式
.dark-theme {
  .el-input {
    .el-input__wrapper {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
      color: var(--text-primary) !important;

      &:hover {
        border-color: var(--medical-blue) !important;
      }

      &.is-focus {
        border-color: var(--medical-blue) !important;
      }
    }

    .el-input__inner {
      background-color: transparent !important;
      color: var(--text-primary) !important;

      &::placeholder {
        color: var(--text-tertiary) !important;
      }
    }

    .el-input__prefix,
    .el-input__suffix {
      color: var(--text-secondary) !important;
    }
  }
}

// 下拉菜单样式
.el-dropdown-menu {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-primary) !important;
}

.el-dropdown-menu__item {
  color: var(--text-primary) !important;
  
  &:hover {
    background-color: var(--bg-hover) !important;
  }
}

// 按钮样式
.el-button {
  &--default {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
    
    &:hover {
      background-color: var(--bg-hover) !important;
    }
  }
}

// 面包屑样式
.el-breadcrumb {
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      color: var(--text-primary) !important;
      
      &:hover {
        color: var(--medical-blue) !important;
      }
    }
  }
}

// 滚动条样式更新
::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb) !important;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover) !important;
}

::-webkit-scrollbar-track {
  background-color: var(--scrollbar-track) !important;
}

// Element Plus 表格样式 - 更强的选择器优先级
.dark-theme {
  .el-table {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-primary) !important;

    // 表格头部样式
    .el-table__header-wrapper {
      background-color: var(--bg-secondary) !important;

      .el-table__header {
        background-color: var(--bg-secondary) !important;

        th {
          background-color: var(--bg-secondary) !important;
          color: var(--text-primary) !important;
          border-color: var(--border-primary) !important;
          border-bottom-color: var(--border-primary) !important;
          border-right-color: var(--border-primary) !important;
          border-left-color: var(--border-primary) !important;
          border-top-color: var(--border-primary) !important;
        }
      }
    }

    // 表格体样式
    .el-table__body-wrapper {
      background-color: var(--bg-primary) !important;

      .el-table__body {
        background-color: var(--bg-primary) !important;

        td {
          background-color: var(--bg-primary) !important;
          color: var(--text-primary) !important;
          border-color: var(--border-primary) !important;
          border-bottom-color: var(--border-primary) !important;
          border-right-color: var(--border-primary) !important;
          border-left-color: var(--border-primary) !important;
          border-top-color: var(--border-primary) !important;
        }
      }
    }

    // 表格行样式
    tr {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        background-color: var(--bg-hover) !important;

        td {
          background-color: var(--bg-hover) !important;
        }
      }

      // 斑马纹样式
      &.el-table__row--striped {
        background-color: var(--bg-tertiary) !important;

        td {
          background-color: var(--bg-tertiary) !important;
        }

        &:hover {
          background-color: var(--bg-hover) !important;

          td {
            background-color: var(--bg-hover) !important;
          }
        }
      }
    }

    // 单元格样式 - 更强的选择器
    th.el-table__cell,
    td.el-table__cell {
      border-color: var(--border-primary) !important;
      border-bottom-color: var(--border-primary) !important;
      border-right-color: var(--border-primary) !important;
      border-left-color: var(--border-primary) !important;
      border-top-color: var(--border-primary) !important;
    }

    th.el-table__cell {
      background-color: var(--bg-secondary) !important;
      color: var(--text-primary) !important;
    }

    td.el-table__cell {
      background-color: var(--bg-primary) !important;
      color: var(--text-primary) !important;
    }

    // 表格空状态
    .el-table__empty-block {
      background-color: var(--bg-primary) !important;
      color: var(--text-secondary) !important;
    }

    // 表格边框 - 更全面的边框覆盖
    &::before,
    &::after {
      background-color: var(--border-primary) !important;
    }

    // 强制覆盖所有可能的边框
    * {
      border-color: var(--border-primary) !important;
    }

    // 表格外边框
    &.el-table--border {
      border-color: var(--border-primary) !important;

      &::after {
        background-color: var(--border-primary) !important;
      }

      &::before {
        background-color: var(--border-primary) !important;
      }
    }
  }

  // 表格容器样式
  .table-container {
    background-color: var(--bg-primary) !important;
    box-shadow: var(--shadow-light) !important;
  }

  // 全局表格边框强制覆盖
  .el-table,
  .el-table *,
  .el-table th,
  .el-table td,
  .el-table tr,
  .el-table__cell,
  .el-table__header th,
  .el-table__body td {
    border-color: var(--border-primary) !important;
  }

  // 针对带边框的表格
  .el-table[border],
  .el-table--border {
    border-color: var(--border-primary) !important;

    th,
    td {
      border-color: var(--border-primary) !important;
    }
  }
}

// Element Plus 分页组件 - 更强的选择器
.dark-theme {
  .el-pagination {
    background-color: var(--bg-primary) !important;

    .el-pagination__total,
    .el-pagination__jump {
      color: var(--text-primary) !important;
    }

    .el-pagination__sizes {
      .el-select {
        .el-select__wrapper {
          background-color: var(--bg-primary) !important;
          border-color: var(--border-primary) !important;
          color: var(--text-primary) !important;
        }
      }
    }

    .el-pager {
      li {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;

        &:hover {
          background-color: var(--bg-hover) !important;
        }

        &.is-active {
          background-color: var(--medical-blue) !important;
          color: #ffffff !important;
        }

        &.btn-quicknext,
        &.btn-quickprev {
          background-color: var(--bg-primary) !important;
          color: var(--text-primary) !important;

          &:hover {
            background-color: var(--bg-hover) !important;
          }
        }
      }
    }

    .btn-prev,
    .btn-next {
      background-color: var(--bg-primary) !important;
      color: var(--text-primary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        background-color: var(--bg-hover) !important;
      }

      &:disabled {
        background-color: var(--bg-secondary) !important;
        color: var(--text-tertiary) !important;
      }
    }

    .el-pagination__jump {
      .el-input {
        .el-input__wrapper {
          background-color: var(--bg-primary) !important;
          border-color: var(--border-primary) !important;

          .el-input__inner {
            color: var(--text-primary) !important;
          }
        }
      }
    }
  }
}

// 全局分页样式覆盖 - 最强优先级
.dark-theme {
  .el-pagination {
    background-color: var(--bg-primary) !important;

    * {
      background-color: var(--bg-primary) !important;
    }

    .el-pagination__total,
    .el-pagination__jump,
    .el-pager li,
    .btn-prev,
    .btn-next {
      color: var(--text-primary) !important;
      background-color: var(--bg-primary) !important;
    }

    // 强制覆盖所有可能的白色背景
    .el-pagination__sizes,
    .el-pagination__jump,
    .el-pager,
    .el-pagination__rightwrapper,
    .el-pagination__leftwrapper {
      background-color: var(--bg-primary) !important;
    }
  }

  // 分页容器强制覆盖
  .pagination-container,
  .table-container + div,
  div[class*="pagination"] {
    background-color: var(--bg-primary) !important;
  }

  // 强制覆盖所有可能的白色背景
  [style*="background-color: rgb(255, 255, 255)"],
  [style*="background-color: #fff"],
  [style*="background-color: #ffffff"],
  [style*="background-color: white"] {
    background-color: var(--bg-primary) !important;
  }

  // Element Plus 组件通用覆盖
  .el-popper.is-light {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
  }

  .el-tooltip__popper.is-light {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
  }

  // 数据采集页面卡片容器样式
  .card-container {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    box-shadow: var(--shadow-light) !important;

    &:hover {
      box-shadow: var(--shadow-medium) !important;
    }
  }

  // 区域标题样式
  .section-header {
    border-color: var(--border-primary) !important;

    h3 {
      color: var(--text-primary) !important;
    }
  }

  .section-icon {
    color: var(--medical-blue) !important;
  }

  // 抽屉头部悬浮效果
  .drawer-header:hover {
    background-color: var(--bg-hover) !important;
  }

  // 病灶信息显示区域
  .lesion-text {
    background: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
  }

  // 空状态样式
  .lesion-empty,
  .empty-diagnosis-hint {
    color: var(--text-secondary) !important;

    .empty-icon {
      color: var(--text-tertiary) !important;
    }

    .empty-text {
      color: var(--text-secondary) !important;
    }

    .empty-hint {
      color: var(--text-tertiary) !important;
    }
  }

  // 单选按钮组样式
  .horizontal-radio-group {
    .el-radio {
      background-color: var(--bg-secondary) !important;
      color: var(--text-primary) !important;

      &:hover {
        background-color: var(--bg-hover) !important;
      }

      &.is-checked {
        background-color: rgba(74, 144, 226, 0.2) !important;

        &:hover {
          background-color: rgba(74, 144, 226, 0.3) !important;
        }
      }
    }
  }

  // 表单标签样式
  .el-form-item__label {
    color: var(--text-primary) !important;
  }

  // 展开图标样式
  .expand-icon {
    color: var(--text-secondary) !important;
  }

  // 文本区域样式 - 强制覆盖
  .el-textarea,
  textarea {
    .el-textarea__inner,
    &.el-textarea__inner {
      background-color: var(--bg-primary) !important;
      border: 1px solid var(--border-primary) !important;
      border-color: var(--border-primary) !important;
      color: var(--text-primary) !important;
      box-shadow: none !important;

      &::placeholder {
        color: var(--text-tertiary) !important;
      }

      &:hover {
        border-color: var(--medical-blue) !important;
        box-shadow: none !important;
      }

      &:focus,
      &:focus-visible {
        border-color: var(--medical-blue) !important;
        box-shadow: 0 0 0 1px var(--medical-blue) !important;
        outline: none !important;
      }
    }

    // 修复字符计数器背景
    .el-input__count {
      background-color: var(--bg-secondary) !important;
      color: var(--text-secondary) !important;
      border: 1px solid var(--border-primary) !important;
      border-radius: 4px !important;
    }
  }

  // 强制覆盖所有textarea元素
  textarea {
    background-color: var(--bg-primary) !important;
    border: 1px solid var(--border-primary) !important;
    color: var(--text-primary) !important;

    &:hover {
      border-color: var(--medical-blue) !important;
    }

    &:focus {
      border-color: var(--medical-blue) !important;
      box-shadow: 0 0 0 1px var(--medical-blue) !important;
    }
  }

  // 强制覆盖所有白色边框
  input,
  textarea,
  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper,
  .el-form-item,
  .card-container,
  .section-header,
  .drawer-header,
  .lesion-text,
  .diagnosis-content,
  .upload-placeholder,
  .image-preview {
    border-color: var(--border-primary) !important;
  }

  // 特殊边框处理 - 更全面的白色边框覆盖
  [style*="border-color: rgb(255, 255, 255)"],
  [style*="border-color: #fff"],
  [style*="border-color: #ffffff"],
  [style*="border-color: white"],
  [style*="border: 1px solid white"],
  [style*="border: 2px solid white"],
  [style*="border: 1px solid #fff"],
  [style*="border: 1px solid #ffffff"],
  [style*="border: 2px solid #fff"],
  [style*="border: 2px solid #ffffff"] {
    border-color: var(--border-primary) !important;
  }

  // 强制覆盖所有可能的白色边框元素
  * {
    &[style*="border-color: white"],
    &[style*="border-color: #fff"],
    &[style*="border-color: #ffffff"],
    &[style*="border-color: rgb(255, 255, 255)"] {
      border-color: var(--border-primary) !important;
    }
  }

  // 针对具体的表单元素强制覆盖
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select,
  .el-input,
  .el-textarea,
  .el-select {
    border-color: var(--border-primary) !important;

    * {
      border-color: var(--border-primary) !important;
    }
  }

  // 单选框和复选框样式
  .el-radio {
    .el-radio__label {
      color: var(--text-primary) !important;
    }
  }

  .el-checkbox {
    .el-checkbox__label {
      color: var(--text-primary) !important;
    }
  }

  // 标签输入组件样式
  .el-tag {
    &.el-tag--info {
      background-color: var(--bg-secondary) !important;
      border-color: var(--border-primary) !important;
      color: var(--text-primary) !important;
    }
  }

  // 图像上传区域样式
  .el-upload {
    .el-upload-dragger {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        border-color: var(--medical-blue) !important;
      }
    }
  }

  // 进度条样式
  .el-progress {
    .el-progress__text {
      color: var(--text-primary) !important;
    }
  }

  // Element Plus 输入框组件边框修复
  .el-input {
    .el-input__wrapper {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
      box-shadow: 0 0 0 1px var(--border-primary) inset !important;

      &:hover {
        box-shadow: 0 0 0 1px var(--medical-blue) inset !important;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--medical-blue) inset !important;
      }
    }

    .el-input__inner {
      color: var(--text-primary) !important;

      &::placeholder {
        color: var(--text-tertiary) !important;
      }
    }

    // 特别处理 el-input type="textarea" 的情况
    &.el-input--type-textarea {
      .el-textarea__inner {
        background-color: var(--bg-primary) !important;
        border: 1px solid var(--border-primary) !important;
        color: var(--text-primary) !important;
        box-shadow: none !important;

        &::placeholder {
          color: var(--text-tertiary) !important;
        }

        &:hover {
          border-color: var(--medical-blue) !important;
        }

        &:focus {
          border-color: var(--medical-blue) !important;
          box-shadow: 0 0 0 1px var(--medical-blue) !important;
        }
      }
    }
  }

  // 选择器组件边框修复
  .el-select {
    .el-select__wrapper {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
      box-shadow: 0 0 0 1px var(--border-primary) inset !important;

      &:hover {
        box-shadow: 0 0 0 1px var(--medical-blue) inset !important;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--medical-blue) inset !important;
      }
    }
  }

  // 表单项边框修复
  .el-form-item {
    .el-form-item__content {
      * {
        border-color: var(--border-primary) !important;
      }
    }
  }
}

// Element Plus 对话框样式
.dark-theme {
  .el-dialog {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;

    .el-dialog__header {
      border-color: var(--border-primary) !important;
    }

    .el-dialog__title {
      color: var(--text-primary) !important;
    }
  }
}

// Element Plus 消息提示样式
.el-message {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

// Element Plus 通知样式
.dark-theme {
  .el-notification {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;

    .el-notification__title {
      color: var(--text-primary) !important;
    }

    .el-notification__content {
      color: var(--text-secondary) !important;
    }
  }

  // Element Plus 选择器
  .el-select {
    .el-select__wrapper {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
      color: var(--text-primary) !important;

      &:hover {
        border-color: var(--medical-blue) !important;
      }

      &.is-focus {
        border-color: var(--medical-blue) !important;
      }
    }

    .el-select__placeholder {
      color: var(--text-tertiary) !important;
    }

    .el-select__caret {
      color: var(--text-secondary) !important;
    }
  }

  .el-select-dropdown {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;

    .el-select-dropdown__item {
      color: var(--text-primary) !important;

      &:hover {
        background-color: var(--bg-hover) !important;
      }

      &.is-selected {
        background-color: var(--medical-blue) !important;
        color: #ffffff !important;
      }
    }
  }

  // Element Plus 日期选择器
  .el-date-editor {
    .el-input__wrapper {
      background-color: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
    }

    .el-input__inner {
      color: var(--text-primary) !important;
    }
  }

  // Element Plus 弹出层
  .el-popper {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
  }

  // Element Plus 加载遮罩
  .el-loading-mask {
    background-color: rgba(26, 26, 26, 0.8) !important;

    .el-loading-text {
      color: var(--text-primary) !important;
    }
  }

  // Element Plus 标签
  .el-tag {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;

    &.el-tag--success {
      background-color: rgba(26, 230, 196, 0.2) !important;
      border-color: var(--medical-green) !important;
      color: var(--medical-green) !important;
    }

    &.el-tag--warning {
      background-color: rgba(255, 167, 38, 0.2) !important;
      border-color: var(--medical-orange) !important;
      color: var(--medical-orange) !important;
    }

    &.el-tag--danger {
      background-color: rgba(255, 107, 122, 0.2) !important;
      border-color: var(--medical-red) !important;
      color: var(--medical-red) !important;
    }
  }

  // 最强制的边框覆盖 - 确保所有白色边框都被替换
  * {
    &[style*="border"] {
      border-color: var(--border-primary) !important;
    }
  }

  // 针对病史文本区域的特殊处理
  .history-report-section {
    .el-input,
    .el-textarea {
      .el-textarea__inner {
        background-color: var(--bg-primary) !important;
        border: 1px solid var(--border-primary) !important;
        color: var(--text-primary) !important;

        &:hover {
          border-color: var(--medical-blue) !important;
        }

        &:focus {
          border-color: var(--medical-blue) !important;
          box-shadow: 0 0 0 1px var(--medical-blue) !important;
        }
      }
    }
  }

  // 数据采集页面所有表单项的强制覆盖
  .medical-data-collection {
    .el-form-item {
      .el-input,
      .el-textarea,
      .el-select {
        .el-input__wrapper,
        .el-textarea__inner,
        .el-select__wrapper {
          background-color: var(--bg-primary) !important;
          border: 1px solid var(--border-primary) !important;
          color: var(--text-primary) !important;

          &:hover {
            border-color: var(--medical-blue) !important;
          }

          &:focus,
          &.is-focus {
            border-color: var(--medical-blue) !important;
            box-shadow: 0 0 0 1px var(--medical-blue) !important;
          }
        }

        // 字符计数器修复
        .el-input__count {
          background-color: var(--bg-secondary) !important;
          color: var(--text-secondary) !important;
          border: 1px solid var(--border-primary) !important;
          border-radius: 4px !important;
        }
      }
    }
  }

  // 全局字符计数器修复
  .el-input__count,
  .el-textarea__count {
    background-color: var(--bg-secondary) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: 4px !important;
  }

  // 图像标注页面特定样式
  .image-analysis-container {
    background: var(--bg-secondary) !important;
  }

  .image-workspace {
    background: var(--bg-primary) !important;
  }

  .annotation-toolbar-area {
    background: var(--bg-primary) !important;
  }

  .medical-image-canvas-container {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    box-shadow: var(--shadow-medium) !important;
  }

  .image-display-area {
    background: var(--bg-primary) !important;
  }

  .left-aside,
  .right-aside {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    box-shadow: var(--shadow-light) !important;
  }

  // Canvas 加载和错误状态样式
  .canvas-loading {
    color: var(--text-secondary) !important;

    .el-icon {
      color: var(--medical-blue) !important;
    }
  }

  .canvas-error {
    color: var(--medical-red) !important;
  }

  // 图像上传组件样式
  .image-upload {
    :deep(.el-upload-dragger) {
      background: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        border-color: var(--medical-blue) !important;
        background: var(--bg-hover) !important;
      }
    }
  }

  // 缩略图网格样式
  .thumbnail-item {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;

    &:hover {
      border-color: var(--medical-blue) !important;
      box-shadow: var(--shadow-medium) !important;
    }

    &--active {
      border-color: var(--medical-blue) !important;
    }

    &--loading {
      border-color: var(--border-secondary) !important;
    }
  }

  // 标注坐标显示样式
  .annotation-item {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;

    &:hover {
      border-color: var(--medical-blue) !important;
      box-shadow: var(--shadow-light) !important;
    }
  }

  .annotation-index {
    background: var(--medical-blue) !important;
    color: var(--text-inverse) !important;
  }

  // ModernPanel 组件样式
  .modern-panel {
    background: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    box-shadow: var(--shadow-medium) !important;

    &:hover {
      box-shadow: var(--shadow-heavy) !important;
    }

    &--bordered {
      border-color: var(--border-primary) !important;
      box-shadow: var(--shadow-heavy) !important;
    }
  }

  .modern-panel__title {
    color: var(--text-primary) !important;
  }

  .modern-panel__icon {
    color: var(--medical-blue) !important;
  }

  .modern-panel__content {
    background: var(--bg-primary) !important;
  }

  // ClinicalInputPanel 组件样式
  .clinical-input-panel {
    .upload-title,
    .lesion-title {
      color: var(--text-primary) !important;
    }

    .help-icon {
      color: var(--text-secondary) !important;

      &:hover {
        color: var(--medical-blue) !important;
      }
    }

    .divider {
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--border-primary) 20%,
        var(--border-primary) 80%,
        transparent 100%
      ) !important;
    }

    .fixed-bottom-actions {
      background: linear-gradient(to top, var(--bg-primary) 70%, transparent 100%) !important;
      border-top-color: var(--border-secondary) !important;
    }

    .clear-btn {
      background: var(--medical-blue) !important;
      color: var(--text-inverse) !important;
    }
  }

  // Textarea 组件样式
  .lesion-textarea {
    :deep(.el-textarea__inner) {
      background: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;
      color: var(--text-primary) !important;

      &::placeholder {
        color: var(--text-tertiary) !important;
      }

      &:focus {
        border-color: var(--medical-blue) !important;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1) !important;
      }
    }
  }

  // AnnotationInfoPanel 组件样式
  .annotation-info-panel {
    // 空状态样式
    .empty-annotations {
      color: var(--text-secondary) !important;
    }

    .empty-icon {
      color: var(--text-tertiary) !important;
    }

    .empty-text {
      color: var(--text-primary) !important;
    }

    .empty-hint {
      color: var(--text-secondary) !important;
    }

    // 列表头部样式
    .list-header {
      background: var(--bg-secondary) !important;
      border-color: var(--border-primary) !important;
    }

    .lesion-count {
      color: var(--text-primary) !important;
    }

    // 病灶项目样式
    .lesion-item {
      background: var(--bg-primary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        border-color: var(--medical-blue) !important;
        box-shadow: var(--shadow-light) !important;
      }

      &--selected {
        border-color: var(--medical-blue) !important;
        background: var(--bg-hover) !important;
      }
    }

    .lesion-title {
      color: var(--text-primary) !important;
    }

    .info-label {
      color: var(--text-primary) !important;
    }

    .info-content {
      color: var(--text-secondary) !important;
    }

    .point-coord {
      background: var(--bg-secondary) !important;
      color: var(--text-primary) !important;
    }

    .more-points {
      color: var(--text-secondary) !important;
    }
  }

  // 外部底部按钮区域
  .external-footer {
    background: var(--bg-primary) !important;
    border-top-color: var(--border-primary) !important;
  }

  // LesionDetailDialog 组件样式
  .lesion-detail {
    .section-title {
      color: var(--text-primary) !important;
    }

    .section-subtitle {
      color: var(--text-primary) !important;
    }

    .detail-label {
      color: var(--text-primary) !important;
    }

    .detail-content {
      color: var(--text-secondary) !important;
    }

    .point-item {
      background: var(--bg-secondary) !important;
      border-color: var(--border-primary) !important;
    }

    .point-index {
      background: var(--medical-blue) !important;
      color: var(--text-inverse) !important;
    }

    .point-coord {
      color: var(--text-primary) !important;
    }
  }

  // EmptyStateDisplay 组件样式
  .empty-state {
    background: var(--bg-primary) !important;

    .decoration-circle {
      background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(74, 144, 226, 0.05) 100%) !important;
    }

    .main-icon {
      color: var(--medical-blue) !important;
      filter: drop-shadow(0 4px 12px rgba(74, 144, 226, 0.3)) !important;
    }

    .icon-pulse {
      border-color: var(--medical-blue) !important;
    }

    .empty-title {
      color: var(--text-primary) !important;
    }

    .empty-description {
      color: var(--text-secondary) !important;
    }

    .feature-item {
      background: var(--bg-secondary) !important;
      border-color: var(--border-primary) !important;

      &:hover {
        background: var(--bg-hover) !important;
        border-color: var(--medical-blue) !important;
      }

      span {
        color: var(--text-primary) !important;
      }
    }

    .feature-icon {
      color: var(--medical-green) !important;
    }
  }
}
