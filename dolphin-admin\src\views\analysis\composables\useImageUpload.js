import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export function useImageUpload() {
  // 响应式数据
  const uploadedImages = ref([])
  const currentImageIndex = ref(0)

  // 计算属性
  const currentImageData = computed(() => {
    return uploadedImages.value[currentImageIndex.value]
  })

  const hasImages = computed(() => {
    return uploadedImages.value.length > 0
  })

  // 文件验证
  const validateFile = (file, maxSize = 10 * 1024 * 1024) => {
    // 检查文件大小
    if (file.size > maxSize) {
      ElMessage.error(`文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`)
      return false
    }

    // 检查文件类型
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/dicom']
    const isValidType = validTypes.includes(file.type) || 
                       file.name.toLowerCase().endsWith('.dcm') ||
                       file.name.toLowerCase().endsWith('.dicom')
    
    if (!isValidType) {
      ElMessage.error('只支持 JPG、PNG、DICOM 格式的文件')
      return false
    }

    return true
  }

  // 处理文件选择 - 直接添加到图片列表，无需裁剪
  const handleFileSelected = (imageData) => {
    if (!validateFile({ size: imageData.size, type: imageData.type, name: imageData.name })) {
      return
    }

    // 直接添加图片到列表
    uploadedImages.value.push(imageData)
    currentImageIndex.value = uploadedImages.value.length - 1

    ElMessage.success('图片上传成功')
  }

  // 选择图片
  const selectImage = (index) => {
    if (index >= 0 && index < uploadedImages.value.length) {
      currentImageIndex.value = index
    }
  }

  // 删除图片
  const removeImage = (index) => {
    if (index >= 0 && index < uploadedImages.value.length) {
      uploadedImages.value.splice(index, 1)
      
      // 调整当前图片索引
      if (currentImageIndex.value >= uploadedImages.value.length) {
        currentImageIndex.value = Math.max(0, uploadedImages.value.length - 1)
      }
      
      return uploadedImages.value.length === 0
    }
    return false
  }

  // 图片导航
  const prevImage = () => {
    if (currentImageIndex.value > 0) {
      selectImage(currentImageIndex.value - 1)
    }
  }

  const nextImage = () => {
    if (currentImageIndex.value < uploadedImages.value.length - 1) {
      selectImage(currentImageIndex.value + 1)
    }
  }

  // 清空所有图片
  const clearAllImages = () => {
    uploadedImages.value = []
    currentImageIndex.value = 0
  }

  return {
    // 响应式数据
    uploadedImages,
    currentImageIndex,

    // 计算属性
    currentImageData,
    hasImages,

    // 方法
    validateFile,
    handleFileSelected,
    selectImage,
    removeImage,
    prevImage,
    nextImage,
    clearAllImages
  }
}
