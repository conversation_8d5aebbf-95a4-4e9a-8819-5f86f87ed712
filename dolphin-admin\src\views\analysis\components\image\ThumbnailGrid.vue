<script setup>
import { defineEmits, defineProps } from 'vue'
import { Close, Check, Loading, Picture } from '@element-plus/icons-vue'

// 定义事件
const emit = defineEmits(['select-image', 'remove-image'])

// 定义props
const props = defineProps({
  images: {
    type: Array,
    default: () => [],
  },
  currentIndex: {
    type: Number,
    default: 0,
  },
})

// 选择图片
const handleSelectImage = (index) => {
  emit('select-image', index)
}

// 删除图片
const handleRemoveImage = (index) => {
  emit('remove-image', index)
}

// 图片加载完成
const handleImageLoad = (index) => {
  // 可以在这里处理图片加载完成的逻辑
}

// 图片加载错误
const handleImageError = (index) => {
  // 可以在这里处理图片加载错误的逻辑
}
</script>

<template>
  <div class="thumbnail-grid">
    <!-- 图片列表 -->
    <div class="thumbnail-list" :class="{ 'single-image': images.length === 1 }">
      <div
        v-for="(image, index) in images"
        :key="image.id"
        class="thumbnail-item"
        :class="{
          'thumbnail-item--active': index === currentIndex,
          'thumbnail-item--loading': image.loading,
        }"
        @click="handleSelectImage(index)"
      >
        <!-- 图片预览 -->
        <div class="thumbnail-image-container">
          <img
            :src="image.src"
            :alt="image.name"
            class="thumbnail-image"
            @load="handleImageLoad(index)"
            @error="handleImageError(index)"
          />

          <!-- 加载状态 -->
          <div v-if="image.loading" class="thumbnail-loading">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
          </div>

          <!-- 错误状态 -->
          <div v-if="image.error" class="thumbnail-error">
            <el-icon>
              <Picture />
            </el-icon>
            <span>加载失败</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="thumbnail-actions">
          <el-button
            type="danger"
            :icon="Close"
            size="small"
            circle
            @click.stop="handleRemoveImage(index)"
            class="remove-btn"
          />
        </div>

        <!-- 选中指示器 -->
        <div v-if="index === currentIndex" class="thumbnail-indicator">
          <el-icon>
            <Check />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.thumbnail-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.thumbnail-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.75rem;
  height: 100%;
}

/* 当只有一张图片时，让它占据整个空间 */
.thumbnail-list.single-image {
  grid-template-columns: 1fr;
}

.thumbnail-list.single-image .thumbnail-item {
  aspect-ratio: unset;
  height: 100%;
}

.thumbnail-list.single-image .thumbnail-image-container {
  height: 90%;
}

.thumbnail-item {
  position: relative;
  aspect-ratio: 1;
  border: 2px solid var(--border-primary);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-primary);
}

.thumbnail-item:hover {
  border-color: var(--medical-blue);
  transform: scale(1.02);
  box-shadow: var(--shadow-medium);
}

.thumbnail-item--active {
  border-color: var(--medical-blue);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.thumbnail-item--loading {
  border-color: var(--border-secondary);
}

.thumbnail-image-container {
  position: relative;
  width: 100%;
  height: 70%;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.thumbnail-item:hover .thumbnail-image {
  transform: scale(1.05);
}

.thumbnail-loading,
.thumbnail-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  color: #6b7280;
  font-size: 0.75rem;
}

.thumbnail-loading .el-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.thumbnail-error .el-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.thumbnail-actions {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.thumbnail-item:hover .thumbnail-actions {
  opacity: 1;
}

.remove-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  background: rgba(239, 68, 68, 0.9);
  border: none;
}

.remove-btn:hover {
  background: #ef4444;
}

.thumbnail-indicator {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: 20px;
  height: 20px;
  background: #22c55e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thumbnail-list {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
  }
}
</style>
