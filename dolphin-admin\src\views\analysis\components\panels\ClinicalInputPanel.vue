<template>
  <ModernPanel title="病例输入" :icon="Edit" size="default" :bordered="true">
    <div class="clinical-input-panel">
      <!-- 滚动内容区域 -->
      <div class="scroll-content">
        <!-- 图像上传区域（无容器包装） -->
        <div class="image-upload-area">
          <div class="upload-title">医学影像</div>
          <ImageUpload
            :uploaded-images="uploadedImages"
            :current-image-index="currentImageIndex"
            @file-selected="handleFileSelected"
            @select-image="handleSelectImage"
            @remove-image="handleRemoveImage"
          />
        </div>

        <!-- 分隔线 -->
        <div class="divider"></div>

        <!-- 病灶输入区域（无容器包装） -->
        <div class="lesion-input-area">
          <div class="lesion-title">
            病灶信息
            <el-tooltip content="必须先填写病灶信息才可以标注" placement="top" effect="dark">
              <el-icon class="help-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
          <el-input
            v-model="lesionInput"
            type="textarea"
            :rows="6"
            placeholder="请输入病灶描述信息..."
            class="lesion-textarea"
            @input="handleLesionInputChange"
          />
        </div>
      </div>

      <!-- 固定底部按钮区域 -->
      <div class="fixed-bottom-actions">
        <el-button type="primary" @click="handleClearAll" class="clear-btn">重置所有</el-button>
      </div>
    </div>
  </ModernPanel>
</template>

<script setup>
import { defineEmits, defineProps, ref, watch } from 'vue'
import { Edit, QuestionFilled } from '@element-plus/icons-vue'
import ModernPanel from '../common/ModernPanel.vue'
import ImageUpload from '../image/ImageUpload.vue'

// 定义props接收父组件数据
const props = defineProps({
  uploadedImages: {
    type: Array,
    default: () => [],
  },
  currentImageIndex: {
    type: Number,
    default: 0,
  },
  lesionData: {
    type: String,
    default: '',
  },
})

// 病灶输入数据
const lesionInput = ref(props.lesionData || '')

// 监听props变化，同步病灶数据
watch(
  () => props.lesionData,
  (newValue) => {
    if (newValue !== undefined && newValue !== lesionInput.value) {
      lesionInput.value = newValue
    }
  },
)

// 定义事件
const emit = defineEmits(['clear-input', 'file-selected', 'select-image', 'remove-image', 'lesion-input-change'])

// 监听病灶输入变化
const handleLesionInputChange = () => {
  emit('lesion-input-change', lesionInput.value)
}

// 处理图像上传相关事件
const handleFileSelected = (imageData) => {
  emit('file-selected', imageData)
}

const handleSelectImage = (index) => {
  emit('select-image', index)
}

const handleRemoveImage = (index) => {
  emit('remove-image', index)
}

// 处理清空所有内容
const handleClearAll = () => {
  lesionInput.value = ''
  emit('clear-input')
}
</script>

<style scoped>
.clinical-input-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.scroll-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  padding-bottom: 217px; /* 为固定底部按钮留出空间 */
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 图像上传区域（无容器包装） */
.image-upload-area {
  flex: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.upload-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  padding-left: 2px;
}

/* 分隔线 */
.divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--border-primary) 20%,
    var(--border-primary) 80%,
    transparent 100%
  );
  margin: 0.5rem 0;
}

/* 病灶输入区域（无容器包装） */
.lesion-input-area {
  width: 100%;
  flex-shrink: 0;
}

.lesion-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  padding-left: 2px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-icon {
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.help-icon:hover {
  color: var(--medical-blue);
}

.lesion-textarea {
  width: 100%;
}

.lesion-textarea :deep(.el-textarea__inner) {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: none;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.lesion-textarea :deep(.el-textarea__inner):focus {
  border-color: var(--medical-blue);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.workflow-alert {
  margin: 0;
  border-radius: 12px;
  border: 2px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  flex-shrink: 0;
}

.workflow-alert :deep(.el-alert__content) {
  padding-left: 0;
}

.workflow-alert :deep(.el-alert__title) {
  color: #1e40af;
  font-weight: 700;
}

.workflow-steps {
  margin: 0.75rem 0 0 0;
  padding-left: 1.5rem;
  color: #1e40af;
}

.workflow-steps li {
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.workflow-steps li::marker {
  color: #3b82f6;
  font-weight: 700;
}

/* 固定底部按钮区域 */
.fixed-bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.5rem 1rem 0.5rem 1rem;
  background: linear-gradient(to top, var(--bg-primary) 70%, transparent 100%);
  border-top: 1px solid var(--border-secondary);
  backdrop-filter: blur(10px);
}

.clear-btn {
  width: 100%;
  height: 44px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  background: var(--medical-blue);
  color: var(--text-inverse);
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .clinical-input-panel {
    gap: 1rem;
  }

  .workflow-steps li {
    font-size: 0.8rem;
  }
}
</style>
