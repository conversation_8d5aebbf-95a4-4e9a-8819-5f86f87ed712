import { defineStore } from 'pinia'

// 本地存储键名
const DARK_MODE_KEY = 'dolphin-admin-dark-mode'

// 获取本地存储的暗色模式设置
const getStoredDarkMode = (): boolean => {
  try {
    const stored = localStorage.getItem(DARK_MODE_KEY)
    return stored ? JSON.parse(stored) : false
  } catch {
    return false
  }
}

// 保存暗色模式设置到本地存储
const setStoredDarkMode = (darkMode: boolean): void => {
  try {
    localStorage.setItem(DARK_MODE_KEY, JSON.stringify(darkMode))
  } catch {
    // 忽略存储错误
  }
}

let useLayoutSettingStore = defineStore('SettingStore', {
  state: () => {
    return {
      fold: false, // 控制菜单是否折叠
      refresh: false, // 控制是否点击刷新
      darkMode: getStoredDarkMode(), // 控制暗色模式，从本地存储恢复
    }
  },
  actions: {
    /**
     * 切换暗色模式
     */
    toggleDarkMode() {
      this.darkMode = !this.darkMode
      setStoredDarkMode(this.darkMode)
      this.applyTheme()
    },

    /**
     * 设置暗色模式
     * @param darkMode 是否启用暗色模式
     */
    setDarkMode(darkMode: boolean) {
      this.darkMode = darkMode
      setStoredDarkMode(this.darkMode)
      this.applyTheme()
    },

    /**
     * 应用主题到DOM
     */
    applyTheme() {
      const htmlElement = document.documentElement
      if (this.darkMode) {
        htmlElement.classList.add('dark-theme')
      } else {
        htmlElement.classList.remove('dark-theme')
      }
    },

    /**
     * 初始化主题
     */
    initTheme() {
      this.applyTheme()
    }
  },
  getters: {
    /**
     * 获取当前主题名称
     */
    currentTheme: (state) => state.darkMode ? 'dark' : 'light',

    /**
     * 获取主题图标名称
     */
    themeIcon: (state) => state.darkMode ? 'Sunny' : 'Moon',
  },
})

export default useLayoutSettingStore
