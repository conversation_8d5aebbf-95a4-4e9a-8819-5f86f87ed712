<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'DataOperationBar',
})

// 定义props
interface Props {
  selectedCount: number
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectedCount: 0,
  loading: false,
})

// 定义emits
interface Emits {
  export: []
  deleteSelected: []
}

const emit = defineEmits<Emits>()

// 处理导出按钮点击
const handleExport = () => {
  emit('export')
}

// 处理批量删除按钮点击
const handleDeleteSelected = () => {
  emit('deleteSelected')
}
</script>

<template>
  <div class="operation-bar">
    <el-button type="success" icon="Download" @click="handleExport" :loading="loading">
      导出数据
    </el-button>
    <el-button 
      type="danger" 
      icon="Delete" 
      :disabled="selectedCount === 0 || loading" 
      @click="handleDeleteSelected"
    >
      删除选中 ({{ selectedCount }})
    </el-button>
  </div>
</template>

<style scoped lang="scss">
.operation-bar {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;
  }
}
</style>
