// axios二次封装
import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserStore from '@/store/modules/user'
import router from '@/router'

let request = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API, // 基础路径带上/api
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// 定义后端返回的数据结构
interface Result<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

//请求拦截器
request.interceptors.request.use((config) => {
  let useUser = useUserStore()
  //获取token,在请求头携带
  const token = useUser.token
  if (token) {
    // 确保令牌格式正确，添加Bearer前缀（如果后端需要）
    config.headers.Authorization = token.startsWith('Bearer ') ? token : `Bearer ${token}`

  }
  console.log('📤 发送请求:', config.method?.toUpperCase(), config.url)
  return config
})

//响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<Result | Blob>) => {
    const { data, config } = response

    // 如果是 Blob 响应（如图像数据），直接返回
    if (data instanceof Blob) {
      return data
    }

    // 处理后端返回的Result结构
    const resultData = data as Result
    if (resultData.code === 200) {
      // 检查是否为静默请求
      const isSilentRequest = config.headers?.['X-Silent-Request'] === 'true'

      // 成功情况 - 非静默请求才显示成功提示
      if (!isSilentRequest) {
        ElMessage.success(resultData.message || '操作成功')
      }
      // console.log('请求成功', resultData.data)
      return resultData.data
    } else {
      // 业务错误处理
      handleBusinessError(resultData)
      return Promise.reject({
        code: resultData.code,
        message: resultData.message,
      })
    }
  },
  (error) => {
    const { response } = error

    console.error('❌ 请求失败:', error)

    // 处理HTTP状态码错误
    if (response) {
      console.error('📋 响应数据:', response.data)
      handleHttpError(response)
    } else {
      // 网络错误
      console.error('🌐 网络错误:', error.message)
      ElMessage.error('网络连接异常，请检查网络连接和后端服务状态')
    }

    return Promise.reject(error)
  },
)

// 处理业务错误（后端返回的错误码）
const handleBusinessError = (data: Result) => {
  switch (data.code) {
    case 401:
    case 4001: // Token过期
    case 4002: // Token无效
      handleTokenError(data.message)
      break

    case 403:
    case 3001: // 权限不足
      ElMessage.error(data.message || '无权限访问')
      break

    default:
      // 如果错误码大于4200，跳转到登录界面
      ElMessageBox.alert(JSON.stringify(data, null, 2), '错误信息', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: false, // 避免 XSS
      })
      if (data.code > 4200) {
        handleTokenError(data.message || '认证失败，请重新登录')
      } else {
        ElMessage.error(data.message || '操作失败')
      }
  }
}

// 处理HTTP错误（网络状态码错误）
const handleHttpError = (response: any) => {
  const status = response.status
  const url = response.config?.url || ''
  let message = ''

  switch (status) {
    case 400:
      message = '请求参数错误'
      break
    case 401:
      message = '未授权，请重新登录'
      handleTokenError(message)
      return
    case 403:
      message = '拒绝访问'
      break
    case 404:
      // 特殊处理：如果是登出接口404，不显示错误提示
      if (url.includes('/auth/logout')) {
        console.warn('⚠️ 登出接口未实现，跳过错误提示')
        return
      }
      message = '请求地址不存在'
      break
    case 500:
      message = '服务器内部错误'
      break
    case 502:
      message = '网关错误'
      break
    case 503:
      message = '服务不可用'
      break
    default:
      message = `连接错误${status}`
  }

  ElMessage.error(message)
}

// 防止重复处理token过期的标志
let isHandlingTokenError = false

// 处理Token错误
const handleTokenError = (message: string) => {
  // 防止重复处理
  if (isHandlingTokenError) {
    return
  }

  isHandlingTokenError = true
  const userStore = useUserStore()

  // 显示错误消息
  ElMessage.error(message || '登录已过期，请重新登录')

  // 直接执行登出并跳转，不需要用户确认
  userStore.userLogout()
  router.push('/login')

  // 延迟重置标志，避免在跳转过程中重复处理
  setTimeout(() => {
    isHandlingTokenError = false
  }, 1000)
}

export default request
