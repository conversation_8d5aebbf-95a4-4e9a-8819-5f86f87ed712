<script setup lang="ts">
import { View, Check, Refresh } from '@element-plus/icons-vue'

// Props 定义
interface Props {
  loading: boolean
}

const props = defineProps<Props>()

// Events 定义
interface Emits {
  'preview-data': []
  'submit-form': []
  'reset-form': []
}

const emit = defineEmits<Emits>()

// 事件处理
const handlePreviewData = () => {
  emit('preview-data')
}

const handleSubmitForm = () => {
  emit('submit-form')
}

const handleResetForm = () => {
  emit('reset-form')
}
</script>

<template>
  <div class="form-actions card-container">
    <el-button size="default" @click="handlePreviewData">
      <el-icon><View /></el-icon>
      预览数据
    </el-button>
    <el-button type="primary" size="default" @click="handleSubmitForm" :loading="loading">
      <el-icon><Check /></el-icon>
      保存数据
    </el-button>
    <el-button size="default" @click="handleResetForm">
      <el-icon><Refresh /></el-icon>
      重置表单
    </el-button>
  </div>
</template>

<style scoped>
/* 统一卡片容器样式 */
.card-container {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: var(--shadow-medium);
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  /* flex-wrap: wrap; */
  padding: 20px 24px;
  align-self: end; /* 推到底部 */
}

/* 按钮统一样式 */
:deep(.el-button) {
  height: 36px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}
</style>
