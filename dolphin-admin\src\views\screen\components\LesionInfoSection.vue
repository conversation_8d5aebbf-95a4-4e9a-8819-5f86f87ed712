<script setup lang="ts">
import { ref, computed } from 'vue'
import { InfoFilled, Document, ArrowRight, EditPen } from '@element-plus/icons-vue'
import type { MedicalFormData } from '../types'
import useImageStore from '@/store/modules/image'

// Props 定义
interface Props {
  formData: MedicalFormData
}

const props = defineProps<Props>()

// Events 定义
interface Emits {
  'update:report': [report: string]
}

const emit = defineEmits<Emits>()

// 获取状态管理实例
const imageStore = useImageStore()

// 抽屉状态管理
const lesionExpanded = ref(true) // 病灶信息
const historyExpanded = ref(true) // 病史

// 计算属性
const lesionDescription = computed(() => {
  // 从Pinia获取合并的病灶信息
  return imageStore.getCombinedLesionDescription()
})

const report = computed({
  get: () => props.formData.report,
  set: (value) => emit('update:report', value),
})

// 切换抽屉状态的方法
const toggleLesionExpanded = () => {
  lesionExpanded.value = !lesionExpanded.value
}

const toggleHistoryExpanded = () => {
  historyExpanded.value = !historyExpanded.value
}
</script>

<template>
  <div class="lesion-info-container">
    <!-- 病灶信息输入区域 -->
    <div class="lesion-section card-container drawer-container">
      <div class="section-header drawer-header" @click="toggleLesionExpanded">
        <div class="header-left">
          <el-icon class="section-icon"><InfoFilled /></el-icon>
          <h3>病灶信息</h3>
        </div>
        <div class="header-right">
          <el-icon class="expand-icon" :class="{ expanded: lesionExpanded }">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="drawer-content" :class="{ expanded: lesionExpanded }">
        <div class="lesion-display-area">
          <div v-if="lesionDescription" class="lesion-content">
            <div class="lesion-text">{{ lesionDescription }}</div>
          </div>
          <div v-else class="lesion-empty">
            <el-icon class="empty-icon"><EditPen /></el-icon>
            <p class="empty-text">暂无病灶信息</p>
            <p class="empty-hint">请点击图片上的"图像标注"按钮进行标注并填写病灶信息</p>
          </div>
          <div class="lesion-actions">
            <el-text type="info" size="small">💡 如需修改病灶信息，请点击图片上的"重新标注"按钮</el-text>
          </div>
        </div>
      </div>
    </div>

    <!-- 病史 -->
    <div class="history-report-section card-container drawer-container">
      <div class="section-header drawer-header" @click="toggleHistoryExpanded">
        <div class="header-left">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3>病史</h3>
        </div>
        <div class="header-right">
          <el-icon class="expand-icon" :class="{ expanded: historyExpanded }">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="drawer-content" :class="{ expanded: historyExpanded }">
        <el-form-item prop="report">
          <el-input
            v-model="report"
            type="textarea"
            :rows="4"
            placeholder="请填写患者病史、主诉等信息"
            maxlength="500"
            show-word-limit
            resize="none"
          />
        </el-form-item>
      </div>
    </div>
  </div>
</template>

<style scoped>
.lesion-info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 统一卡片容器样式 */
.card-container {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: var(--shadow-medium);
}

/* 抽屉容器样式 */
.drawer-container {
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 60px; /* 统一折叠状态的最小高度 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 8px;
  margin: -8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.drawer-header:hover {
  background-color: var(--bg-hover);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: var(--text-secondary);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.drawer-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0;
}

.drawer-content.expanded {
  max-height: 500px;
  padding-top: 16px;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-primary);
}

.section-icon {
  font-size: 18px;
  color: var(--medical-blue);
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 病灶信息显示区域样式 */
.lesion-display-area {
  width: 100%;
}

.lesion-content {
  margin-bottom: 12px;
}

.lesion-text {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 80px;
}

.lesion-empty {
  text-align: center;
  padding: 24px 12px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 32px;
  color: var(--text-tertiary);
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  margin: 8px 0 4px 0;
  color: var(--text-secondary);
}

.empty-hint {
  font-size: 12px;
  margin: 0;
  color: var(--text-tertiary);
  line-height: 1.4;
}

.lesion-actions {
  padding-top: 8px;
  border-top: 1px solid var(--border-secondary);
}
</style>
