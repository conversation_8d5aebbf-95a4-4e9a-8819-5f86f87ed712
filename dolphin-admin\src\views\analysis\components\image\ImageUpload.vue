<script setup>
import { ref } from 'vue'
import { UploadFilled, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义事件
const emit = defineEmits(['file-selected', 'images-updated', 'select-image', 'remove-image'])

// 定义props
const props = defineProps({
  uploadedImages: {
    type: Array,
    default: () => [],
  },
  currentImageIndex: {
    type: Number,
    default: 0,
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024, // 10MB
  },
})

// 响应式数据
const uploadRef = ref()
const isDragOver = ref(false)
const errorMessage = ref('')

// 分析页面不需要裁剪相关状态，已移除

// 支持的文件类型
const acceptTypes = '.jpg,.jpeg,.png,.dcm,.dicom'

// 文件上传前的检查
const handleBeforeUpload = (file) => {
  // 检查文件大小
  if (file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${Math.round(props.maxSize / 1024 / 1024)}MB`)
    return false
  }

  // 检查文件类型
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/dicom']
  const isValidType =
    validTypes.includes(file.type) ||
    file.name.toLowerCase().endsWith('.dcm') ||
    file.name.toLowerCase().endsWith('.dicom')

  if (!isValidType) {
    ElMessage.error('只支持 JPG、PNG、DICOM 格式的文件')
    return false
  }

  return true
}

// 处理文件变化
const handleFileChange = (file, fileList) => {
  if (file.status === 'ready') {
    // 如果已经有图片，先清除
    if (props.uploadedImages.length > 0) {
      emit('remove-image', 0)
    }
    processFile(file.raw)
  }
}

// 处理文件 - 分析页面直接处理图片，无需裁剪
const processFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    // 分析页面直接处理所有图片文件，无需裁剪
    if (file.type.startsWith('image/')) {
      const img = new Image()
      img.onload = () => {
        const imageData = {
          id: Date.now() + Math.random(),
          name: file.name,
          src: e.target.result,
          size: file.size,
          type: file.type,
          originalWidth: img.naturalWidth,
          originalHeight: img.naturalHeight,
          uploadTime: new Date().toLocaleString(),
        }

        // 直接发送文件选择事件到画布
        emit('file-selected', imageData)

        ElMessage.success('医学影像上传成功！')
      }

      img.onerror = () => {
        errorMessage.value = '图片加载失败，请检查文件格式'
      }

      img.src = e.target.result
    } else {
      // 非图片文件不支持
      errorMessage.value = '请上传图片文件（JPG、PNG、GIF等格式）'
    }
  }

  reader.onerror = () => {
    errorMessage.value = '文件读取失败，请重试'
  }

  reader.readAsDataURL(file)
}

const handleRemoveImage = (index) => {
  emit('remove-image', index)
}
</script>

<template>
  <div class="image-upload-container">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="image-upload"
      :class="{ 'is-dragover': isDragOver }"
      drag
      :accept="acceptTypes"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
      :on-change="handleFileChange"
      :auto-upload="false"
    >
      <div v-if="uploadedImages.length === 0" class="upload-content">
        <el-icon class="upload-icon">
          <UploadFilled />
        </el-icon>
        <div class="upload-text">点击上传或拖拽文件至此</div>
      </div>

      <!-- 单张图片显示 -->
      <div v-else class="single-image-container">
        <img :src="uploadedImages[0].src" :alt="uploadedImages[0].name" class="single-image" />
        <!-- 删除按钮 -->
        <el-button
          type="danger"
          :icon="Close"
          size="small"
          circle
          @click.stop="handleRemoveImage(0)"
          class="remove-btn"
        />
      </div>
    </el-upload>

    <!-- 错误提示 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      :closable="true"
      @close="errorMessage = ''"
      class="error-alert"
    />

    <!-- 分析页面不需要裁剪功能 -->
  </div>
</template>


<style scoped>
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  overflow: hidden;
}

.image-upload {
  width: 100%;
  height: 100%;
  min-height: 300px;
  overflow: hidden;
}

.image-upload :deep(.el-upload) {
  width: 100%;
  height: 100%;
}

.image-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
  border: 2px dashed var(--border-primary);
  border-radius: 8px;
  background: var(--bg-primary);
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.image-upload :deep(.el-upload-dragger:hover) {
  border-color: var(--medical-blue);
  background: var(--bg-hover);
}

.image-upload.is-dragover :deep(.el-upload-dragger) {
  border-color: #3b82f6;
  background: #dbeafe;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  gap: 1rem;
  height: 100%;
  min-height: 300px;
}

.upload-icon {
  font-size: 1.8rem;
  color: #6b7280;
}

.upload-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6b7280;
}

.single-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.single-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  padding: 0;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.single-image-container:hover .remove-btn {
  opacity: 1;
}

.remove-btn:hover {
  background: #ef4444;
}

.error-alert {
  margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-content {
    padding: 1.5rem;
  }

  .upload-icon {
    font-size: 2.5rem;
  }

  .upload-text {
    font-size: 0.875rem;
  }
}
</style>
